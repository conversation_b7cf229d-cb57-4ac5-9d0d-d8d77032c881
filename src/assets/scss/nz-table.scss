@import '~@haizhi/ui/styles/themes/light.data';
.ant-table-wrapper {
  width: 100%;
  .ant-table-column-title {
    display: flex;
    align-items: center;
  }
  .ant-table-header {
    background: $mono-100;
  }
  .ant-table-thead tr th {
    font-weight: 600;
    font-size: 13px;
    line-height: 20px;
    padding: 10px 16px;
    color: $type-600;
    white-space: nowrap;
    background: $mono-200;
    // background-color: $mono-100;
    border-bottom: none;
    // border-bottom: 1px solid $mono-a300;
    box-shadow: inset 0 -1px $mono-a300;
  }
  .ant-table-tbody tr td {
    line-height: 16px;
    padding: 12px 16px;
    font-size: 12px;
    color: $type-800;
    border-bottom: none;
    box-shadow: inset 0 -1px $mono-a300;
    .ant-table-wrapper {
      width: auto;
    }
  }
  .ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
  .ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
  .ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td,
  .ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td {
    // background: $mono-a100;
    background: linear-gradient(0deg, $mono-a100, $mono-a100), $white-100;
  }
  // .ant-table-fixed-header > .ant-table-content > .ant-table-scroll > .ant-table-body {
  //   overflow: auto !important;
  // }
  .ant-table-fixed-header .ant-table-scroll .ant-table-header {
    &::-webkit-scrollbar-track {
      background: unset;
    }
  }
}
.ant-table-placeholder {
  display: none;
}
