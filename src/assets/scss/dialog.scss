@import '~@haizhi/ui/styles/themes/light.data';

html.cdk-global-scrollblock {
  overflow-y: unset;
}
body .cdk-overlay-container {
  z-index: 10000;
  word-wrap: break-word;
  .cdk-overlay-backdrop {
    transform: none;
    background: $mono-m100;
  }
  .cdk-overlay-dark-backdrop {
    opacity: 1;
  }
  .cdk-overlay-transparent-backdrop {
    opacity: 0;
  }
  .cdk-overlay-pane {
    font-size: 12px;
    line-height: 1.5em;
    //margin: 0 auto; 影响 overlay 服务定位
    box-sizing: border-box;
    position: absolute;
    transform: none;
  }
  .mat-dialog-container {
    max-width: none;
    margin: 0;
    padding: 0;
    transform: none;
    overflow: inherit !important; /* 覆盖组件自带的样式 */
    background: $mono-100;
    color: $type-800;
    box-shadow: $container-soft-04;
    border-radius: 8px;
  }
  .mat-dialog-title {
    margin: 0;
    line-height: 32px;
    text-align: left;
    font-size: 14px;
    font-weight: bold;
    padding: 8px 24px;
    color: $type-800;
    position: relative;
    .sub-title-tip {
      font-size: 12px;
      display: inline-block;
      color: rgba(5, 16, 33, 0.28);
      vertical-align: middle;
      margin-left: 8px;
    }
    .hz-icon[hzname='close'] {
      cursor: pointer;
      right: 24px;
      top: 16px;
      position: absolute;
      &:hover {
        color: $type-800;
      }
    }
    .ico-f-close {
      float: right;
      margin: 8px 16px;
    }
  }
  .mat-dialog-content {
    max-height: none;
    margin: 0;
    overflow: inherit !important; /* 覆盖组件自带的样式 */
    padding: 8px 32px 16px;
  }
  .no-button-dialog .mat-dialog-content {
    padding: 8px 32px;
  }
  .mat-dialog-actions {
    display: block;
    text-align: right;
    padding: 8px 16px;
    margin: 0;
    height: 52px;
    box-sizing: border-box;
    // margin-top: 16px;
    .mat-button {
      display: inline-block;
      height: 32px;
      padding: 6px 10px;
      cursor: pointer;
      text-transform: uppercase;
      font-size: 14px;
      margin: 0 0 0 8px;
      border: 0 none;
      font-weight: bold;
      background: transparent;
      color: $primary-900;
      line-height: normal;
      min-width: auto;
      &.disable {
        opacity: 0.5;
      }
      &.disable:hover {
        background: transparent;
      }
      &:hover {
        background: $primary-a100;
        border-radius: 8px;
      }
      &:focus {
        -webkit-animation: ngdialog-pulse 1.1s infinite;
        animation: ngdialog-pulse 1.1s infinite;
        outline: 0;
      }
    }
  }
  .model-overlay {
    background: rgba(189, 190, 191, 0.5);
  }
}
