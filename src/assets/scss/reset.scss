html,
body {
  height: 100%;
}
ul {
  list-style: none;
  margin: 0;
}
body {
  color: $type-800;
  position: relative;
  overflow: hidden;
  margin: 0;
  padding: 0;
}
html body,
div,
dl,
dt,
dd,
ul,
ol,
li,
h1,
h2,
h3,
h4,
h5,
h6,
form,
fieldset,
input,
textarea,
p,
th,
td,
img,
em,
hr {
  margin: 0;
  padding: 0;
  -webkit-text-size-adjust: none;
}

html {
  -webkit-box-sizing: border-box;
  -moz-box-sizing: border-box;
  box-sizing: border-box;
}

*,
*:before,
*:after {
  -webkit-box-sizing: inherit;
  -moz-box-sizing: inherit;
  box-sizing: inherit;
}

body {
  font-family: 'PingFang SC', 'Helvetica Neue', 'Helvetica', 'STHeitiSC-Light', 'WOL_SB', 'Segoe UI Semibold', 'Segoe UI', Tahoma, Helvetica,
    sans-serif;
  font-size: 12px;
  font-weight: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizelegibility;
}

select {
  -webkit-appearance: menulist-button;
}

ol,
ul {
  list-style: none;
}

fieldset,
img {
  border: 0;
}

pre {
  margin: 0;
  padding: 0;
  font-family: 'PingFang SC', 'Helvetica Neue', 'Helvetica', 'STHeitiSC-Light', 'WOL_SB', 'Segoe UI Semibold', 'Segoe UI', Tahoma, Helvetica,
    sans-serif;
}

table {
  border-spacing: 0;
  border-collapse: collapse;
  -webkit-font-smoothing: initial;
  -moz-osx-font-smoothing: unset;
  text-rendering: auto;
}

i {
  font-style: normal;
}

.none,
.hidden {
  display: none;
}

.visible {
  visibility: visible !important;
}

.invisible {
  visibility: hidden !important;
}

em {
  font-style: normal;
}

th {
  font-weight: normal;
}

hr {
  border: 0;
  height: 1px;
}

:focus {
  outline: none;
}

.display-none {
  display: none;
}

.display-inline {
  display: inline !important;
}

.display-inline-block {
  display: inline-block;
}

.display-block {
  display: block !important;
}

button {
  border: none;
}

/**
 * 鼠标样式
 * @example
 * <div>
 *   <span class="cursor-default">默认指针</span>
 *   <span class="cursor-pointer">手型</span>
 *   <span class="cursor-move">拖动</span>
 *   <span class="cursor-crosshair">十字（框选）</span>
 *   <span class="cursor-no-drop">禁止</span>
 *   <span class="cursor-none">隐藏指针</span>
 * </div>
 */

.cursor-pointer {
  cursor: pointer;
}

.cursor-default {
  cursor: default !important;
}

.cursor-move {
  cursor: move;
}

.cursor-none {
  cursor: none !important;
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

.cursor-text {
  cursor: text !important;
}

.user-select-none {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/**
 * 浮动设置
 * @example
 * <div class="clearfix">
 *
 *  <div class="fl" style="height: 150px; background: #CCC">左浮动</div>
 *  <div class="fr" style="height: 150px; background: #CCC">右浮动</div>
 *  <p>
      <span class="vt">靠上</span>
      <span class="vm">垂直居中</span>
      <span class="vb">靠底</span>
 *  </p>
 *  已清除浮动
 * </div>
 */

.fl {
  float: left;
}

.fr {
  float: right;
}

.clear {
  clear: both;
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: '';
}

.clearfix:after {
  clear: both;
}

.clearfix {
  zoom: 1;
}

.vm {
  vertical-align: middle !important;
}

.vt {
  vertical-align: top !important;
}

.vb {
  vertical-align: bottom !important;
}
.w-full {
  width: 100%;
}

/*边距设置*/

.no-margin {
  margin: 0 !important;
}

.no-padding {
  padding: 0 !important;
}

// margin

.mr-4,
.mr4 {
  margin-right: 4px;
}
.mr-8,
.mr8 {
  margin-right: 8px;
}
.mr-12,
.mr12 {
  margin-right: 12px;
}
.mr-16,
.mr16 {
  margin-right: 16px;
}
.mr-24,
.mr24 {
  margin-right: 24px;
}
.mr-32,
.mr32 {
  margin-right: 32px;
}

.ml-4,
.ml4 {
  margin-left: 4px;
}
.ml-8,
.ml8 {
  margin-left: 8px;
}
.ml-12,
.ml12 {
  margin-left: 12px;
}
.ml-16,
.ml16 {
  margin-left: 16px;
}
.ml-24,
.ml24 {
  margin-left: 24px;
}
.ml-32,
.ml32 {
  margin-left: 32px;
}

.mt-4,
.mt4 {
  margin-top: 4px;
}
.mt-8,
.mt8 {
  margin-top: 8px;
}
.mt-12,
.mt12 {
  margin-top: 12px;
}
.mt-16,
.mt16 {
  margin-top: 16px;
}
.mt-24,
.mt24 {
  margin-top: 24px;
}
.mt-32,
.mt32 {
  margin-top: 32px;
}

.mb-4,
.mb4 {
  margin-bottom: 4px;
}
.mb-8,
.mb8 {
  margin-bottom: 8px;
}
.mb-12,
.mb12 {
  margin-bottom: 8px;
}
.mb-16,
.mb16 {
  margin-bottom: 16px;
}
.mb-24,
.mb24 {
  margin-bottom: 24px;
}
.mb-32,
.mb32 {
  margin-bottom: 32px;
}

// padding

.pb-4,
.pb4 {
  padding-bottom: 4px;
}
.pb-8,
.pb8 {
  padding-bottom: 8px;
}
.pb-12,
.pb12 {
  padding-bottom: 12px;
}
.pb-16,
.pb16 {
  padding-bottom: 16px;
}
.pb-24,
.pb24 {
  padding-bottom: 24px;
}
.pb-32,
.pb32 {
  padding-bottom: 32px;
}

.pt-4,
.pt4 {
  padding-top: 4px;
}
.pt-8,
.pt8 {
  padding-top: 8px;
}
.pt10 {
  padding-top: 10px;
}

.pt-12,
.pt12 {
  padding-top: 12px;
}
.pt-16,
.pt16 {
  padding-top: 16px;
}
.pt-24,
.pt24 {
  padding-top: 24px;
}
.pt-32,
.pt32 {
  padding-top: 32px;
}

.pl-4,
.pl4 {
  padding-left: 4px;
}
.pl-8,
.pl8 {
  padding-left: 8px;
}
.pl-12,
.pl12 {
  padding-left: 12px;
}
.pl-16,
.pl16 {
  padding-left: 16px;
}
.pl-24,
.pl24 {
  padding-left: 24px;
}
.pl-32,
.pl32 {
  padding-left: 32px;
}

.pr-4,
.pr4 {
  padding-right: 4px;
}
.pr-8,
.pr8 {
  padding-right: 8px;
}
.pr-12,
.pr12 {
  padding-right: 12px;
}
.pr-16,
.pr16 {
  padding-right: 16px;
}
.pr-24,
.pr24 {
  padding-right: 24px;
}
.pr-32,
.pr32 {
  padding-right: 32px;
}

/*圆角设置*/

.br2 {
  border-radius: 2px;
}

.br4 {
  border-radius: 4px;
}

.br6 {
  border-radius: 6px;
}

.br8 {
  border-radius: 8px;
}

.ps {
  position: static !important;
}

.pr {
  position: relative;
}

.pa {
  position: absolute;
}

.pf {
  position: fixed;
}

.nowrap {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/**
 * 文字格式
 * @example
 * <div style="width: 200px">
 *  <p class="text-left">文字居左</p>
 *  <p class="text-center">文字居中</p>
 *  <p class="text-right">文字居右</p>
 *  <p class="nowrap">文字禁止换行，超出部分省略号，省略号，省略号</p>
 * </div>
 */

.text-center {
  text-align: center !important;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-decoration-none {
  text-decoration: none;
}

.text-decoration-underline,
.text-decoration-hover-underline:hover {
  text-decoration: underline;
}

.text-decoration-underline:hover {
  text-decoration: none;
}

.text-underline {
  text-decoration: underline;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

.overflow-scroll {
  overflow: scroll;
}

.word-break-all {
  word-break: break-all;
}

.white-space-normal {
  white-space: normal;
}

.font-w-n {
  font-weight: normal;
}

.font-w-b {
  font-weight: bold;
}

.fontsize-10 {
  font-size: 10px;
}

.font-size-12 {
  font-size: 12px;
}

.font-size-14 {
  font-size: 14px;
}

.font-size-22 {
  font-size: 22px;
}

.height-auto {
  height: auto !important;
}

.height-32 {
  height: 32px;
  line-height: 32px;
}

.line-height-20 {
  line-height: 20px !important;
}

.line-height-24 {
  line-height: 24px !important;
}

.line-height-28 {
  line-height: 28px !important;
}

.line-height-32 {
  line-height: 32px !important;
}

/* --------- 表格样式 --------- */

table {
  border-spacing: 0;
  border-collapse: collapse;
  -webkit-font-smoothing: initial;
  -moz-osx-font-smoothing: initial;
  text-rendering: auto;
}

/* --------- 字体颜色 --------- */

.font-normal {
  font-weight: normal;
}

.font-bold {
  font-weight: bold;
}

/* --------- 定位 --------- */

.fl {
  float: left;
}

.fr {
  float: right;
}

/* --------- 定位 --------- */

.pos-static {
  position: static;
}

.pos-relative {
  position: relative;
}

.pos-absolute {
  position: absolute;
}

.pos-fixed {
  position: fixed;
}

.display-block {
  display: block;
}

.display-inline {
  display: inline;
}

.display-inline-block {
  display: inline-block;
}

/* --------- 行高 --------- */

.line-height-24 {
  line-height: 24px;
}

.line-height-28 {
  line-height: 28px;
}

.line-height-32 {
  line-height: 32px;
}

// ::-webkit-scrollbar {
//   width: 14px;
//   height: 14px;
//   padding: 0;
// }

/*Pseudo ::scrollbar-button element*/

::-webkit-scrollbar-button {
  display: block;
  width: 0;
  height: 0;
}

/*Pseudo ::scrollbar-corner element*/

::-webkit-scrollbar-corner {
  background-color: transparent;
}

/*Pseudo ::scrollbar-thumb element*/

::-webkit-scrollbar-thumb {
  border-radius: 2px;
  background: rgba(128, 133, 144, 0.15);
}

/*Pseudo ::scrollbar-track element*/

::-webkit-scrollbar-track {
  border-radius: 2px;
  background: rgba(128, 133, 144, 0.06);
}

.ng-hide {
  display: none;
}
