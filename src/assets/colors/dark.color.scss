:root[theme='dark'] {
  /** primary **/
  --primary-900: #3d85ff;
  --primary-800: #4c8eff;
  --primary-700: #5c98ff;
  --primary-a100: rgba(61, 133, 255, 0.15);
  --primary-a200: rgba(61, 133, 255, 0.3);
  --primary-a300: rgba(61, 133, 255, 0.4);

  /** mono **/
  --mono-100: #13151a;
  --mono-200: #191d25;
  --mono-300: #1f2430;
  --mono-400: #293246;
  --mono-500: #34405c;
  --mono-600: #3f4f73;
  --mono-a100: rgba(204, 219, 255, 0.06);
  --mono-a200: rgba(204, 219, 255, 0.1);
  --mono-a300: rgba(204, 219, 255, 0.16);
  --mono-a400: rgba(204, 219, 255, 0.25);
  --mono-a500: rgba(204, 219, 255, 0.32);
  --mono-a900: rgba(230, 235, 255, 0.9);
  --mono-m100: rgba(22, 28, 40, 0.82);

  /** background **/
  --background-100: #13151a;
  --background-200: #191d25;
  --background-300: #1f2430;
  --background-400: #293246;
  --background-500: #34405c;
  --background-600: #34405c;

  /** type **/
  --type-600: rgba(255, 255, 255, 0.32);
  --type-700: rgba(255, 255, 255, 0.5);
  --type-800: rgba(255, 255, 255, 0.7);
  --type-900: rgba(255, 255, 255, 1);

  /** general **/
  --general-100: #ffffff;
  --general-900: #151618;

  /** cerulean **/
  --ceruleam-900: #0092e0;
  --ceruleam-a100: rgba(0, 146, 224, 0.15);

  /** teal **/
  --teal-900: #00b1b8;
  --teal-450: rgba(0, 177, 184, 0.08);
  --teal-a100: rgba(0, 177, 184, 0.15);
  --teal-a200: rgba(0, 177, 184, 0.25);
  --teal-a300: rgba(0, 177, 184, 0.38);
  --teal-a500: rgba(0, 177, 184, 0.54);

  /** orange **/
  --orange-900: #ff9029;
  --orange-800: #ffaf2e;
  --orange-700: #e79f23;
  --orange-a100: rgba(255, 144, 41, 0.15);

  /** violet **/
  --violet-900: #913dff;
  --violet-a100: rgba(145, 61, 255, 0.15);

  /** yellow **/
  --yellow-900: #ebc400;
  --yellow-800: #fadc43;
  --yellow-700: #d4b101;
  --yellow-a100: rgba(235, 196, 0, 0.15);

  /** red **/
  --red-100: #361d24;
  --red-200: #421f28;
  --red-900: #ff475d;
  --red-800: #fa6678;
  --red-700: #fa4158;
  --red-a100: rgba(255, 71, 93, 0.15);
  --red-a200: rgba(255, 71, 93, 0.2);
  --red-a300: rgba(255, 71, 93, 0.25);

  /** Lime **/
  --lime-900: #218d41;
  --lime-800: #4cbb58;
  --lime-700: #3c9a50;
  --lime-a100: rgba(33, 141, 65, 0.15);
  --lime-a300: rgba(33, 141, 65, 0.25);

  /** shadow **/
  --container-c100: 0px 8px 64px rgba(4, 8, 16, 0.48), 0px 0px 1px rgba(4, 8, 16, 0.32);
  --container-c200: 0px 4px 8px rgba(4, 8, 16, 0.16), 0px 1px 3px rgba(4, 8, 16, 0.32), 0px 0px 1px rgba(4, 8, 16, 0.32);
  --container-c300: 0px 8px 24px -6px rgba(4, 8, 16, 0.56), 0px 1px 3px rgba(4, 8, 16, 0.3), 0px 0px 1px rgba(4, 8, 16, 0.32);
  --container-c400: 0px 16px 32px -6px rgba(4, 8, 16, 0.64), 0px 3px 8px -2px rgba(4, 8, 16, 0.48), 0px 0px 1px rgba(4, 8, 16, 0.32);
  --container-c500: 0px 48px 128px -16px rgba(4, 8, 16, 0.64), 0px 16px 64px -16px rgba(4, 8, 16, 0.72), 0px 0px 1px rgba(4, 8, 16, 0.32);
}
