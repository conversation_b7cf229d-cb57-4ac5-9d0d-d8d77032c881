<article mat-dialog-title>
  批量设置字段名
  <i hz-icon hzName="close" (click)="dialogRef.close()"></i>
</article>
<mat-dialog-content>
  <div class="box-warp mb16">
    <span class="title mr16">命名方式</span>
    <label class="cursor-pointer mr16">
      <input type="radio" class="mr4-i" [(ngModel)]="setType" name="setType" value="desc" />
      字段备注
    </label>
    <label class="cursor-pointer">
      <input type="radio" class="mr4-i" [(ngModel)]="setType" name="setType" value="upload" />
      上传文件
    </label>
  </div>
  <div class="box-warp" *ngIf="setType === 'desc'; else uploadTpl">
    <span class="title mr16">覆盖方式</span>
    <label class="cursor-pointer mr16">
      <input type="radio" [(ngModel)]="dialogCloseData" name="selectField" [value]="1" />
      将字段描述批量作为新字段名
    </label>
    <label class="cursor-pointer">
      <input type="radio" [(ngModel)]="dialogCloseData" name="selectField" [value]="2" />
      将原字段名批量作为新字段名
    </label>
  </div>
  <ng-template #uploadTpl>
    <!-- [hzTotal]="1" -->
    <hz-upload
      class="upload"
      [(hzFileList)]="fileList"
      (hzChange)="onHzChange($event)"
      [hzBeforeRequest]="beforeRequest"
      [hzBeforeUpload]="beforeUpload"
      [hzBeforeFileQueued]="beforeFileQueued"
      hzType="drag"
      [hzAccept]="accept"
      (hzRemove)="onHzRemove()"
      hzAction="/api/field/upload_templete"
    >
      <div class="drag-content">
        <i hz-icon hzName="upload-1" hzColor="#1F71FF" hzSize="32px"></i>
        <p>点击或拖拽上传</p>
        <p class="subtitle">仅支持上传单个文件</p>
      </div>
    </hz-upload>
    <div class="box-warp" *ngIf="isCsvFile">
      <p class="mb8">请选择csv文件中用于分列的分隔符</p>
      <ul class="warp-list">
        <li class="mr24" *ngFor="let item of separators">
          <label>
            <input type="radio" name="terminate" [(ngModel)]="separator" [value]="item.value" />
            {{ item.label }}
            <input
              *ngIf="item.value === 'other'"
              type="text"
              class="hz-input"
              [(ngModel)]="otherSeparator"
              placeholder="其他"
            />
          </label>
        </li>
      </ul>
    </div>
  </ng-template>
</mat-dialog-content>
<mat-dialog-actions>
  <div class="block-opt" *ngIf="setType === 'upload'">
    <a class="tpl-download" href="./assets/docs/字段批量命名模板.xlsx" target="_blank">
      下载模板
    </a>
    <i
      hz-icon
      class="ml4"
      hzName="help"
      hzSize="16px"
      [hzTooltipType]="'info'"
      hz-tooltip
      [hzTooltipTitle]="hzTooltipTemplate"
      [hzTooltipPlacement]="'top'"
    >
      <ng-template #hzTooltipTemplate>
        <div class="prev-upload-tip">
          <div class="table-wrap w100">
            <table class="bdp-table-normal w100 hz-table">
              <thead>
                <tr>
                  <th>原字段名</th>
                  <th>新字段名</th>
                  <th>字段描述</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>ABC</td>
                  <td>abc</td>
                  <td>123</td>
                </tr>
                <tr>
                  <td>DEF</td>
                  <td>def</td>
                  <td>456</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </ng-template>
    </i>
  </div>
  <div *ngIf="isUpload" style="display: inline-block">
    <div
      *ngIf="errorString"
      hz-tooltip
      [hzTooltipTitle]="errorString"
      style="display: inline-block"
    >
      <button hz-button hzType="text" disabled>确定</button>
    </div>
    <button *ngIf="!errorString" hz-button hzType="text" disabled>确定</button>
  </div>
  <button *ngIf="!isUpload" hz-button hzType="text" (click)="closeDialog()">确定</button>
  <button hz-button hzType="text" (click)="dialogRef.close()">取消</button>
</mat-dialog-actions>
