import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BatchSetFieldNameComponent } from './batch-set-field-name.component';
import { MatDialogModule } from '@angular/material';
import { FormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { UploadModule } from '@haizhi/ng-hertz/upload';

@NgModule({
  declarations: [BatchSetFieldNameComponent],
  imports: [CommonModule, MatDialogModule, FormsModule, SharedModule, UploadModule],
  entryComponents: [BatchSetFieldNameComponent],
  exports: [BatchSetFieldNameComponent]
})
export class BatchSetFieldNameModule {}
