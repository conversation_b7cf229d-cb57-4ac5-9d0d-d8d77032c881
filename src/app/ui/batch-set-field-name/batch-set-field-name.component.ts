import { Component, Inject, OnInit, ViewChild } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MatDialog } from '@angular/material/dialog';
import { MessageService } from '@haizhi/ng-hertz/message';
import { UploadFile, UploadFileChange, UploadComponent } from '@haizhi/ng-hertz/upload';

@Component({
  selector: 'batch-set-field-name',
  templateUrl: './batch-set-field-name.component.html',
  styleUrls: ['./batch-set-field-name.component.scss']
})
export class BatchSetFieldNameComponent implements OnInit {
  tbId: string;
  setType = 'desc';
  accept = 'text/csv, .xlsx, .xls';
  fileList = [];
  isCsvFile = false;
  fieldTerminate = '';
  separator = 'comma';
  otherSeparator = '';
  isUpload = false;
  errorString: string;
  separators: Array<{
    label: string;
    value: string;
  }> = [
    { label: '制表符', value: 'tab' },
    { label: '分号', value: 'semicolon' },
    { label: '逗号', value: 'comma' },
    { label: '空格', value: 'space' },
    { label: '', value: 'other' }
  ];
  dialogCloseData:
    | number
    | {
        [key: string]: string;
      } = 1;

  @ViewChild(UploadComponent, { static: false }) uploadComponent: UploadComponent;

  constructor(
    private message: MessageService,
    public dialog: MatDialog,
    public dialogRef: MatDialogRef<BatchSetFieldNameComponent>,
    @Inject(MAT_DIALOG_DATA)
    public dialogData: {
      tbId: string;
    }
  ) {}

  beforeRequest = (file: UploadFile) => {
    file.requestParams = {
      tb_id: this.tbId,
      key: file.originFile.name,
      filename: file.originFile.name,
      field_terminate: this.isCsvFile ? this.fieldTerminate : null,
      dmc_request: 1
    };
  };

  beforeUpload = (file: UploadFile, fileList: Array<UploadFile>): boolean => {
    this.isCsvFile = /\.csv$/.test(file.originFile.name);
    this.isUpload = !this.isCsvFile;
    return !this.isCsvFile;
  };

  beforeFileQueued = (file: UploadFile, fileList: Array<UploadFile>): boolean => {
    if (fileList.length === 1) {
      this.onHzRemove();
      fileList.splice(0, 1);
    }
    return true;
  };

  ngOnInit() {
    this.tbId = this.dialogData.tbId;
  }

  closeDialog() {
    if (this.isCsvFile) {
      if (this.separator === 'other') {
        this.fieldTerminate = this.otherSeparator;
      } else {
        this.fieldTerminate = this.separator;
      }
      if (this.separator === 'other' && this.fieldTerminate.length > 1) {
        this.message.warning('只可定义一个分隔符');
      } else {
        this.isUpload = true;
        this.uploadComponent.uploadWaitingFile();
      }
    } else {
      if (this.setType === 'upload' && !this.isUpload && !this.isCsvFile && this.dialogCloseData === 1) {
        this.dialogRef.close(null);
      } else {
        this.dialogRef.close(this.dialogCloseData);
      }
    }
  }

  onHzChange(e: UploadFileChange) {
    if (e.file.response) {
      if (e.file.response.status === '0') {
        this.isUpload = false;
        this.isCsvFile = false;
        this.dialogCloseData = e.file.response.result;
      } else {
        this.errorString = e.file.response.errstr;
        this.message.error(this.errorString);
      }
    }
  }

  onHzRemove() {
    this.otherSeparator = '';
    this.isCsvFile = false;
    this.separator = 'comma';
    this.isUpload = false;
  }
}
