.box-warp {
  text-align: left;
  line-height: 32px;

  .title {
    color: rgba(21, 22, 24, 0.36);
    font-weight: bold;
  }
}

.upload {
  ::ng-deep .hz-upload {
    width: 100%;
  }
}

.drag-content {
  font-size: 14px;
  height: 120px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  width: 100%;
  box-sizing: border-box;
  text-align: center;
  line-height: 20px;
}

i[hz-icon] {
  margin-bottom: 8px;
}

.subtitle {
  font-size: 12px;
  color: rgba(15, 34, 67, 0.48);
}

.warp-list {
  display: flex;
  line-height: 32px;
  li {
    &:last-child {
      margin-right: 0;
    }
    input[type='text'] {
      width: 60px;
    }
  }
}

.block-opt {
  float: left;
  display: flex;
  align-items: center;
  a {
    text-decoration: underline;
    color: rgba(21, 22, 24, 0.48);
  }
  i {
    margin-bottom: 0;
  }
}
