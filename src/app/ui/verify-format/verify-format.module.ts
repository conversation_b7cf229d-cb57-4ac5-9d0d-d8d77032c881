import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { VerifyFormatComponent } from './verify-format.component';
import { MatDialogModule } from '@angular/material';
import { FormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { ExportWorksheetTipModule } from '../export-worksheet-tip/export-worksheet-tip.module';

@NgModule({
  declarations: [VerifyFormatComponent],
  imports: [CommonModule, MatDialogModule, FormsModule, SharedModule, ExportWorksheetTipModule],
  entryComponents: [VerifyFormatComponent],
  exports: [VerifyFormatComponent]
})
export class VerifyFormatModule {}
