import { Component, OnInit, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { MatDialog } from '@angular/material/dialog';
import { finalize } from 'rxjs/operators';
import { TableField } from '../../pages/tb-details/tb-data-preview/tb-data-preview.model';
import { TbDataPreviewService } from '../../pages/tb-details/tb-data-preview/tb-data-preview.service';
import { ExportWorksheetTipComponent } from '../export-worksheet-tip/export-worksheet-tip.component';

@Component({
  selector: 'verify-format',
  templateUrl: './verify-format.component.html',
  styleUrls: ['./verify-format.component.scss']
})
export class VerifyFormatComponent implements OnInit {
  fieldId: string;
  tbName: string;
  tbId: string;
  fields: Array<TableField>;
  loading = false;

  constructor(
    private tbDataPreviewService: TbDataPreviewService,
    public dialog: MatDialog,
    public dialogRef: MatDialogRef<VerifyFormatComponent>,
    @Inject(MAT_DIALOG_DATA)
    public dialogData: {
      tbId: string;
      fields: Array<TableField>;
      tbName: string;
    }
  ) {}

  ngOnInit(): void {
    this.fieldId = '';
    this.tbId = this.dialogData.tbId;
    this.fields = this.dialogData.fields;
    this.tbName = this.dialogData.tbName;
  }

  onConfirmClick() {
    this.loading = true;
    this.tbDataPreviewService
      .pidNumberCheckFormat({
        tb_id: this.tbId,
        tb_name: this.tbName,
        field_id: this.fieldId
      })
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(() => {
        this.dialogRef.close();
        this.dialog.open(ExportWorksheetTipComponent, {
          width: '400px',
          disableClose: true
        });
      });
  }
}
