<div>
  <article mat-dialog-title>
    身份证格式检查
    <i hz-icon hzName="close" (click)="dialogRef.close()"></i>
  </article>
  <mat-dialog-content>
    <div class="form-group mt10 dashboard-message create-folder-message">
      <div class="row mb8">
        <label class="form-label mr8 nowrap">选择字段</label>
        <hz-select
          class="form-select"
          [(ngModel)]="fieldId"
          hzSize="small"
          [hzNoMatchPlaceholder]="'not found'"
          hzPlaceholder="请选择身份证相关字段"
        >
          <hz-option
            *ngFor="let item of fields"
            [hzLabel]="item.name"
            [hzValue]="item.field_id"
          ></hz-option>
        </hz-select>
      </div>
      <div class="row mb8">
        <p class="form-label mr8">
          验证任务启动后，可以在离线任务列表中查看结果和进度，任务完成后，右上角会有气泡提醒，请注意查看。
        </p>
      </div>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions>
    <button *ngIf="fieldId == ''" disabled hz-button hzType="text">开始检验</button>
    <button *ngIf="fieldId != ''" hz-button hzType="text" (click)="onConfirmClick()">
      开始检验
    </button>
  </mat-dialog-actions>
</div>
<hz-loading-gif *ngIf="loading"></hz-loading-gif>
