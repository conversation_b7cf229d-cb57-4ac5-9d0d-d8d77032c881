import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'confirm-dialog',
  template: `
    <div mat-dialog-title>
      <span>{{ title }}</span>
      <i hz-icon hzName="close" (click)="dialogRef.close()"></i>
    </div>
    <mat-dialog-content>
      <div>{{ content }}</div>
    </mat-dialog-content>
    <mat-dialog-actions>
      <button mat-button class="mat-button" (click)="onOkClick()">{{ sureText }}</button>
      <button mat-button class="mat-button" (click)="onCancelClick()">取消</button>
    </mat-dialog-actions>
  `
})
export class ConfirmDialogComponent {
  title: string;
  content: string;
  sureText: string;
  constructor(public dialogRef: MatDialogRef<ConfirmDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: any) {
    this.title = this.data.title || '提示';
    this.content = this.data.content || '这是确认弹窗';
    this.sureText = this.data.sureText || '确认';
  }

  onOkClick(): void {
    this.dialogRef.close(true);
  }

  onCancelClick(): void {
    this.dialogRef.close(false);
  }
}
