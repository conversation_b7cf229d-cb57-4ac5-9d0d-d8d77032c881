import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'delete-dialog',
  template: `
    <div mat-dialog-title>
      <span>{{ title }}</span>
      <i hz-icon hzName="close" (click)="dialogRef.close()"></i>
    </div>
    <mat-dialog-content>
      <div class="result-title clearfix alter-dialog">
        <a class="success-icon"></a>
        <div class="success-text mb20">
          {{ content }}
        </div>
      </div>
    </mat-dialog-content>
  `,
  styles: [
    `
      .result-title {
        text-align: center;
        margin: 6px auto 0;
      }

      .success-icon {
        height: 54px;
        width: 54px;
        display: inline-block;
      }

      .result-title .success-text {
        padding: 0 24px;
      }

      .result-title .success-text {
        margin-top: 8px;
        color: rgba(10, 18, 32, 0.64);
      }

      .result-title .success-text {
        font-size: 12px;
        max-width: 778px;
        margin-top: 16px;
      }
    `
  ]
})
export class DeleteDialogComponent {
  title: string;
  content: string;

  constructor(public dialogRef: MatDialogRef<DeleteDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: any) {
    this.title = this.data.title || '提示';
    this.content = this.data.content || '这是删除弹窗';
    setTimeout(() => {
      this.dialogRef.close();
    }, 3000);
  }

  onOkClick() {
    this.dialogRef.close(true);
  }
}
