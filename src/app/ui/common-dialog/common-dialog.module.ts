import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { MatDialogModule } from '@angular/material/dialog';
import { AlertDialogComponent } from './dialog-alert.component';
import { ConfirmDialogComponent } from './dialog-confirm.component';
import { DeleteDialogComponent } from './dialog-delete.component';
import { IconModule } from '@haizhi/ng-hertz/icon';

const dialogs = [AlertDialogComponent, ConfirmDialogComponent, DeleteDialogComponent];

@NgModule({
  imports: [CommonModule, MatDialogModule, IconModule],
  declarations: [...dialogs],
  entryComponents: [...dialogs],
  exports: [...dialogs]
})
export class CommonDialogModule {}
