import { Component, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'alert-dialog',
  template: `
    <div mat-dialog-title>
      <span>{{ title }}</span>
      <i hz-icon hzName="close" (click)="dialogRef.close()"></i>
    </div>
    <mat-dialog-content>
      <div>{{ content }}</div>
    </mat-dialog-content>
    <mat-dialog-actions>
      <button mat-button class="mat-button" (click)="onOkClick()">确定</button>
    </mat-dialog-actions>
  `
})
export class AlertDialogComponent {
  title: string;
  content: string;

  constructor(public dialogRef: MatDialogRef<AlertDialogComponent>, @Inject(MAT_DIALOG_DATA) public data: any) {
    this.title = this.data.title || '提示';
    this.content = this.data.content || '这是警告弹窗';
  }

  onOkClick(): void {
    this.dialogRef.close();
  }
}
