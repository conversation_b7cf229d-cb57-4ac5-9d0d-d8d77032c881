import { Component, Input, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { debounce, debounceTime, distinctUntilChanged, finalize, map, takeUntil } from 'rxjs/operators';
import { CodemirrorComponent } from 'ng2-codemirror';
import { MessageService } from '@haizhi/ng-hertz/message';
import { HttpService } from '../../../core/services/http.service';
import {
  TableField,
  TableFieldTypeIconMap,
  TableFilterWhere,
  TableSqlFunctionItem
} from '../../../pages/tb-details/tb-data-preview/tb-data-preview.model';
import { Subject } from 'rxjs';

@Component({
  selector: 'tb-sql-filter',
  templateUrl: './tb-sql-filter.component.html',
  styleUrls: ['./tb-sql-filter.component.scss']
})
export class TbSqlFilterComponent implements OnInit, OnDestroy {
  functionList: TableSqlFunctionItem[];
  iconMap = TableFieldTypeIconMap;
  loading = false;
  queryText = '';
  queryTextChange$ = new Subject();
  codemirrorConfig = {
    mode: {
      name: 'text/x-bdp-sql'
    },
    indentWithTabs: true,
    smartIndent: true,
    lineWrapping: true,
    matchBrackets: true,
    readOnly: false,
    autofocus: true,
    extraKeys: { 'Ctrl-Space': 'autocomplete' },
    placeholder: "请输入SQL语句筛选数据，如 [城市] = '北京' and [部门] = '销售部'。请注意，需要使用英文输入法。字段、符号之间需加空格。"
  };
  destroy$ = new Subject();
  filteredFields: TableField[];
  @Input() fields: TableField[];
  @Input() where: TableFilterWhere;

  @ViewChild(CodemirrorComponent, { static: false }) codeMirror: CodemirrorComponent;

  constructor(private http: HttpService, private message: MessageService) {}

  ngOnInit() {
    this.getSqlFunctionList();
    this.filteredFields = [...this.fields];
    // 搜索
    this.queryTextChange$
      .pipe(
        debounceTime(500),
        map(e => this.queryText),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.filteredFields = this.fields.filter(e => e.title.toLocaleLowerCase().indexOf(this.queryText.trim().toLocaleLowerCase()) > -1);
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getSqlFunctionList() {
    this.loading = true;
    this.http
      .get('/api/function/list')
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        // 取 2 - 6
        const classification = res.classification;
        this.functionList = [2, 3, 4, 5, 6]
          .map(e => classification[e])
          .reduce((prev: TableSqlFunctionItem[], cur: TableSqlFunctionItem[]) => {
            prev.push(...cur);
            return prev;
          }, []);
      });
  }

  insertFields(field: TableField) {
    const codemirror = this.codeMirror.instance;
    const text = '[' + field.name + ']';
    codemirror.replaceSelection(text);
    codemirror.focus();
  }

  insertFunction(sqlFn: TableSqlFunctionItem) {
    const codemirror = this.codeMirror.instance;
    const text = sqlFn.name === 'COUNT_DISTINCT' ? 'COUNT(DISTINCT())' : sqlFn.name + '()';
    codemirror.replaceSelection(text);
    const offset = text === 'COUNT(DISTINCT())' ? 2 : 1;
    const cur = codemirror.getCursor();
    const ch = cur.ch - offset;
    codemirror.setCursor(cur.line, ch);
    codemirror.focus();
  }

  undo() {
    this.codeMirror.instance.undo();
  }

  redo() {
    this.codeMirror.instance.redo();
  }

  formatSql() {
    if (!this.checkSql()) {
      return;
    }
    const sql = this.where.sql[0].sql;
    this.http.post('/api/sql_script/format', { sql }).subscribe(res => {
      this.where.sql[0].sql = res.sql;
    });
  }

  grammarCheck() {
    if (!this.checkSql()) {
      return;
    }
    const sql = this.where.sql[0].sql;
    this.http.post('/api/tb/sql_trans', { sql, tb_id: this.where.sql[0].tb_id }).subscribe(res => {
      this.message.success('语法校验通过');
    });
  }

  checkSql() {
    if (!this.where.sql[0].sql) {
      this.message.error('请输入表达式');
      return false;
    }
    return true;
  }
}
