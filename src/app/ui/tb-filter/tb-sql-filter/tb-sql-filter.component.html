<article class="tb-sql-filter">
  <div class="tb-sql-filter-field">
    <div class="field-title">字段名称</div>
    <div class="field-search">
      <hz-search
        hzWidth="100%"
        [(ngModel)]="queryText"
        (ngModelChange)="queryTextChange$.next()"
      ></hz-search>
    </div>
    <div class="field-list">
      <ul>
        <li *ngFor="let item of filteredFields" [title]="item.title">
          <i
            class="mr-4"
            hz-icon
            [hzName]="iconMap[item.data_type]"
            hzColor="rgba(31,113,255,1)"
          ></i>
          <span>{{ item.title }}</span>
          <button
            class="field-insert-btn"
            hz-button
            hzType="icon-only"
            hzSize="xs"
            (click)="insertFields(item)"
          >
            <i hz-icon hzName="insert-field"></i>
          </button>
        </li>
      </ul>
      <div class="field-no-result" *ngIf="filteredFields && filteredFields.length === 0">
        无相关字段
      </div>
    </div>
  </div>
  <div class="tb-sql-filter-codemirror">
    <div class="codemirror-ctrl">
      <div class="codemirror-ctrl-left">
        <button hz-button hzType="action" class="mr-4" (click)="undo()">
          <i hz-icon hzName="undo"></i>
        </button>
        <button hz-button hzType="action" (click)="redo()">
          <i hz-icon hzName="redo"></i>
        </button>
      </div>
      <div class="codemirror-ctrl-right">
        <button hz-button hzType="action" class="mr-4" (click)="formatSql()">
          <i hz-icon hzName="code-format"></i>
          <span>格式化</span>
        </button>
        <button hz-button hzType="action" (click)="grammarCheck()">
          <i hz-icon hzName="grammar-check"></i>
          <span>语法校验</span>
        </button>
      </div>
    </div>
    <div class="codemirror-wrap">
      <codemirror [(ngModel)]="where.sql[0].sql" [config]="codemirrorConfig"></codemirror>
    </div>
  </div>
  <div class="tb-sql-filter-function">
    <div class="function-title">函数</div>
    <div class="function-list">
      <ul>
        <ng-container *ngFor="let item of functionList">
          <li
            hz-tooltip
            hzTooltipPlacement="left"
            [hzTooltipTitle]="functionTooltipTpl"
            hzTooltipType="info"
            (click)="insertFunction(item)"
          >
            <i class="mr-4" hz-icon hzName="fx" hzColor="rgba(31,113,255,1)"></i>
            <span>{{ item.name }}</span>
          </li>
          <ng-template #functionTooltipTpl>
            <article class="function-tooltip">
              <div class="function-tooltip-name">{{ item.name }}</div>
              <div class="function-tooltip-item">
                <div class="function-tooltip-item-title">用法</div>
                <div class="function-tooltip-item-desc">{{ item.usage }}</div>
              </div>
              <div class="function-tooltip-item">
                <div class="function-tooltip-item-title">说明</div>
                <div class="function-tooltip-item-desc">{{ item.desc }}</div>
              </div>
              <div class="function-tooltip-item">
                <div class="function-tooltip-item-title">示例</div>
                <div class="function-tooltip-item-desc">{{ item.demo }}</div>
              </div>
            </article>
          </ng-template>
        </ng-container>
      </ul>
    </div>
  </div>
  <hz-loading-gif loadingSize="size-1x" *ngIf="loading"></hz-loading-gif>
</article>
