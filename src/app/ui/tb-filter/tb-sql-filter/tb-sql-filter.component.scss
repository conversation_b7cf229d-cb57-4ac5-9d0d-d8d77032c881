@import '~node_modules/@haizhi/ui/styles/themes/light.data';
.tb-sql-filter {
  height: 100%;
  display: flex;
  &-field {
    box-shadow: -1px 0 0 0 rgba(15, 34, 67, 0.11) inset;
    width: 252px;
    padding-right: 16px;
    .field-title {
      color: $type-600;
      font-weight: 600;
      margin-bottom: 8px;
    }
    .field-search {
      height: 32px;
      line-height: 32px;
      margin-bottom: 8px;
    }
    .field-list {
      height: calc(100% - 80px);
      overflow: auto;
      ul {
        list-style: none;
        li {
          height: 32px;
          line-height: 32px;
          position: relative;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
          &:hover {
            background-color: $mono-a100;
            padding-right: 48px;
            .field-insert-btn {
              display: inline-block;
            }
          }
          .field-insert-btn {
            position: absolute;
            right: 16px;
            top: 8px;
            display: none;
          }
        }
      }
      .field-no-result {
        color: $type-600;
      }
    }
  }
  &-codemirror {
    flex: 1;
    padding: 0 16px;
    min-width: 272px;
    .codemirror-ctrl {
      height: 32px;
      line-height: 32px;
      margin-bottom: 8px;
      &-left {
        float: left;
      }
      &-right {
        float: right;
      }
    }
    .codemirror-wrap {
      height: calc(100% - 40px);
    }
  }
  &-function {
    width: 220px;
    padding-left: 16px;
    box-shadow: 1px 0 0 0 rgba(15, 34, 67, 0.11) inset;
    .function-title {
      color: $type-600;
      font-weight: 600;
      margin-bottom: 8px;
      line-height: 32px;
      height: 32px;
    }
    .function-list {
      overflow: auto;
      height: calc(100% - 40px);
      ul {
        list-style: none;
        li {
          height: 32px;
          line-height: 32px;
          position: relative;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          cursor: pointer;
          &:hover {
            background-color: $mono-a100;
          }
        }
      }
    }
  }
}

.function-tooltip {
  width: 216px;
  padding: 8px 0;
  &-name {
    font-weight: 500;
    color: $type-800;
    font-size: 14px;
    margin-bottom: 8px;
  }
  &-item {
    display: flex;
    margin-bottom: 8px;
    &:last-child {
      margin-bottom: 0;
    }
  }
  &-item-title {
    width: 40px;
    color: $type-600;
    font-weight: 600;
  }
  &-item-desc {
    color: $type-800;
    flex: 1;
  }
}
