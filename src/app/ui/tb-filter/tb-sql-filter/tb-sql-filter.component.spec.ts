import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TbSqlFilterComponent } from './tb-sql-filter.component';

describe('TbSqlFilterComponent', () => {
  let component: TbSqlFilterComponent;
  let fixture: ComponentFixture<TbSqlFilterComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [TbSqlFilterComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TbSqlFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
