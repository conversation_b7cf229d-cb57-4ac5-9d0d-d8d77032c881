import { Component, EventEmitter, Input, OnInit, Output } from '@angular/core';
import { TableField, TableFilterWhere } from '../../pages/tb-details/tb-data-preview/tb-data-preview.model';

@Component({
  selector: 'tb-filter',
  templateUrl: './tb-filter.component.html',
  styleUrls: ['./tb-filter.component.scss']
})
export class TbFilterComponent implements OnInit {
  @Input() fields: TableField[];
  @Input() where: TableFilterWhere;
  @Input() tbId: string;
  @Output() filterClick = new EventEmitter();
  constructor() {}

  ngOnInit() {
    this.where.sql[0].tb_id = this.tbId;
  }
}
