@import '~node_modules/@haizhi/ui/styles/themes/light.data';
.tb-filter {
  font-size: 12px;
  color: $type-800;
  position: relative;
  &-content {
    max-height: 400px;
    .tb-filter-type {
      line-height: 32px;
      height: 32px;
      margin-bottom: 16px;
      display: flex;
      align-items: center;
    }
    .tb-filter-type-title {
      font-size: 12px;
      font-weight: 600;
      color: $type-600;
      display: inline-block;
      margin-right: 16px;
    }
    .tb-filter-type-radio {
      display: inline-block;
      label {
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        input {
          margin-right: 4px;
        }
        margin-right: 16px;
      }
    }
    &-sql {
      height: 200px;
    }
    &-condition {
      max-height: 200px;
    }
  }
  &-trigger-btn {
    position: absolute;
    right: 16px;
    bottom: 8px;
  }
}
