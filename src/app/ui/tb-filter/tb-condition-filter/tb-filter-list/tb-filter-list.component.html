<article class="table-filter-list">
  <div class="table-filter-list-wrap" #listWrapElement>
    <table class="hz-table">
      <tbody>
        <tr *ngFor="let filter of list; let i = index" @dropdownMotion>
          <td>
            <hz-select
              [(ngModel)]="filter.fid"
              hzPlaceholder="请选择"
              hzSize="small"
              [hzIsVirtualScroll]="true"
              [hzClearable]="false"
              [hzLabelRender]="labelTpl"
              (ngModelChange)="onItemFieldChange($event, filter)"
            >
              <hz-option
                *ngFor="let field of fields"
                [hzLabel]="field.title"
                [hzValue]="field.fid"
                [hzData]="field"
                [hzCustomContent]="optionTpl"
              >
                <ng-template #optionTpl>
                  <i
                    class="mr-4"
                    hz-icon
                    [hzName]="iconMap[field.data_type]"
                    hzColor="rgba(31,113,255,1)"
                  ></i>
                  <span>{{ field.title }}</span>
                </ng-template>
              </hz-option>
            </hz-select>
            <ng-template #labelTpl let-data>
              <i
                class="mr-4"
                hz-icon
                [hzName]="iconMap[data.hzData.type]"
                hzColor="rgba(31,113,255,1)"
              ></i>
              <span>{{ data.hzLabel }}</span>
            </ng-template>
          </td>
          <td>
            <hz-select
              [(ngModel)]="filter.operator"
              hzPlaceholder="请选择"
              hzSize="small"
              [hzClearable]="false"
              (ngModelChange)="onItemOperatorChange($event, filter)"
            >
              <hz-option
                *ngFor="let op of operatorGroup[filter.data_type]"
                [hzLabel]="operatorMap[op]"
                [hzValue]="op"
              ></hz-option>
            </hz-select>
          </td>
          <td>
            <ng-container *ngIf="filter.operator !== 10">
              <input
                type="text"
                hz-input
                placeholder="请输入"
                [disabled]="filter.operator == 8 || filter.operator == 9"
                [(ngModel)]="filter.value"
              />
            </ng-container>
            <div *ngIf="filter.operator == 10" class="select-date-input">
              <hz-date-picker
                [isRange]="true"
                [(ngModel)]="filter.rangeDate"
                class="date-picker-input"
                (ngModelChange)="onRangeDateChange(filter)"
              ></hz-date-picker>
              <!--          <input type="text" readonly (click)="setDateVal(filter)" class="hz-input" [(ngModel)]="filter.start_date">-->
              <!--          <span> ~ </span>-->
              <!--          <input type="text" readonly (click)="setDateVal(filter)" class="hz-input" [(ngModel)]="filter.end_date">-->
            </div>
          </td>
          <td>
            <i hz-icon hzName="trash" hz-action-icon (click)="deleteItem(filter, i)"></i>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <button class="add-filter-btn" (click)="addItem()" hz-button hzType="action" hzSize="m">
    <i hz-icon hzName="plus1"></i>
    <span>添加规则</span>
  </button>
</article>
