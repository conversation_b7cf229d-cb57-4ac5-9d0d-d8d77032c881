@import '~@haizhi/ui/styles/themes/light.data';
.table-filter-list {
  height: 100%;
  .table-filter-list-wrap {
    max-height: 120px;
    overflow: auto;
  }
  table.hz-table {
    tbody tr:hover {
      background-color: transparent;
    }
    tbody tr td {
      padding: 0 4px;
      &:nth-child(1),
      &:nth-child(2) {
        width: 224px;
      }
      &:nth-child(3) {
        width: 348px;
        input.hz-input {
          width: 100%;
        }
        .select-date-input {
          display: flex;
          align-items: center;
          input {
            flex: 1;
          }
          span {
            margin: 0 4px;
          }
        }
        .date-picker-input {
          width: 100%;
          min-width: 348px;
        }
      }
      &:nth-child(4) {
        width: 40px;
        max-width: 40px;
        text-align: center;
      }
    }
  }

  button.hz-btn.add-filter-btn {
    margin-top: 8px;
    color: $primary-900;
    i.hz-icon {
      color: $primary-900;
    }
    &:after {
      background-color: $primary-a100;
    }
  }
}
