import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TbFilterListComponent } from './tb-filter-list.component';
import { ButtonModule } from '@haizhi/ng-hertz/button';
import { SelectModule } from '@haizhi/ng-hertz/select';
import { InputModule } from '@haizhi/ng-hertz/input';
import { FormsModule } from '@angular/forms';
import { IconModule } from '@haizhi/ng-hertz/icon';
import { DatePickerModule } from '@haizhi/ng-hertz/date-picker';

@NgModule({
  declarations: [TbFilterListComponent],
  imports: [CommonModule, ButtonModule, SelectModule, InputModule, FormsModule, IconModule, DatePickerModule],
  exports: [TbFilterListComponent]
})
export class TbFilterListModule {}
