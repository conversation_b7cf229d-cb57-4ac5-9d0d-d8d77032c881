import { Component, OnInit, forwardRef, ChangeDetectorRef, ChangeDetectionStrategy, Input, ViewChild, ElementRef } from '@angular/core';
import { NG_VALUE_ACCESSOR } from '@angular/forms';
import { Observable } from 'rxjs';
import { animate, AnimationTriggerMetadata, style, transition, trigger } from '@angular/animations';
import * as moment from 'moment';
import {
  OPERATOR_GROUP,
  TableField,
  TableFieldTypeIconMap,
  TableFilter,
  TableFilterOperator
} from '../../../../pages/tb-details/tb-data-preview/tb-data-preview.model';
import { MessageService } from '@haizhi/ng-hertz/message';

export const dropdownMotion: AnimationTriggerMetadata = trigger('dropdownMotion', [
  transition(':enter', [
    style({
      transformOrigin: '0 0',
      transform: 'scaleY(.8)',
      opacity: 0
    }),
    animate(
      `.18s ease-in`,
      style({
        transformOrigin: '0 0',
        transform: 'scaleY(1)',
        opacity: 1
      })
    )
  ]),
  transition(':leave', [
    style({
      transformOrigin: '0 0',
      transform: 'scaleY(1)',
      opacity: 1
    }),
    animate(
      `.18s ease-out`,
      style({
        transformOrigin: '0 0',
        transform: 'scaleY(.8)',
        opacity: 0
      })
    )
  ])
]);

@Component({
  selector: 'table-filter-list',
  templateUrl: './tb-filter-list.component.html',
  styleUrls: ['./tb-filter-list.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush,
  providers: [
    {
      provide: NG_VALUE_ACCESSOR,
      useExisting: forwardRef(() => TbFilterListComponent),
      multi: true
    }
  ],
  animations: [dropdownMotion]
})
export class TbFilterListComponent implements OnInit {
  list: TableFilter[] = [];
  operatorMap = new TableFilterOperator();
  operatorGroup = OPERATOR_GROUP;
  iconMap = TableFieldTypeIconMap;
  @Input() fields: TableField[];
  @ViewChild('listWrapElement', { static: false }) listWrapElement: ElementRef;
  @Input() beforeAddItem: (list: TableFilter[]) => boolean | Observable<boolean> = list => list.every(e => this.checkFilters(e));

  constructor(private cdr: ChangeDetectorRef, private message: MessageService) {}

  ngOnInit() {}

  deleteItem(item: TableFilter, index: number) {
    this.list.splice(index, 1);
    this.onChange(this.list);
  }

  addItem() {
    const before = this.beforeAddItem(this.list);
    if (before instanceof Observable) {
      before.subscribe(bool => {
        if (bool) {
          this.pushList();
        }
      });
    } else if (before !== false) {
      this.pushList();
    }
  }

  checkFilters(item: TableFilter) {
    if (!item.fid) {
      this.message.error('所有筛选项不能为空');
      return false;
    }
    if (item.operator === undefined || item.operator === null) {
      this.message.error('所有筛选项不能为空');
      return false;
    }
    if (item.operator < 8 && !item.value) {
      this.message.error('所有筛选项不能为空');
      return false;
    }
    if (item.operator === 10 && (!item.start_date || !item.end_date)) {
      this.message.error('所有筛选项不能为空');
      return false;
    }
    return true;
  }

  pushList() {
    this.list.push({
      type: null,
      data_type: null,
      value: '',
      operator: null,
      start_date: '',
      end_date: '',
      fid: null,
      field_name: '',
      field_title: ''
    });
    // 滚动到底部
    setTimeout(() => {
      const el = this.listWrapElement.nativeElement;
      el.scrollTop = el.scrollHeight;
    });
    this.onChange(this.list);
  }

  onItemFieldChange(fid: string, item: TableFilter) {
    const field = this.fields.find(e => e.fid === fid);
    if (field) {
      item.data_type = field.data_type;
      item.type = field.type;
      item.field_name = field.name;
      item.field_title = field.title;
    }
    item.operator = this.operatorGroup[item.data_type][0];
    // if (item.operator !== undefined && item.operator !== null) {
    //   const opIndex = this.operatorGroup[item.data_type].findIndex(e => item.operator === e);
    //   if (opIndex === -1) {
    //     item.operator = null;
    //     this.cdr.detectChanges();
    //   }
    // }
  }

  onItemOperatorChange(fid: string, item: TableFilter) {}

  onRangeDateChange(filter: TableFilter) {
    filter.start_date = moment(filter.rangeDate[0]).format('yyyy-MM-DD HH:mm:ss');
    filter.end_date = moment(filter.rangeDate[1]).format('yyyy-MM-DD HH:mm:ss');
    console.log(filter);
  }

  // 设置日期范围值
  // setDateVal(item: TableFilter): void {
  //   const dialogRef = this.dialog.open(DateRangeDialog, {
  //     data: {
  //       startDate: item.start_date,
  //       endDate: item.end_date
  //     },
  //     width: '500px',
  //     disableClose: true
  //   });
  //   dialogRef.afterClosed().subscribe(result => {
  //     if (result.btn === 'ok') {
  //       item.start_date = result.startDate;
  //       item.end_date = result.endDate;
  //       this.cdr.detectChanges();
  //     }
  //   });
  // }

  // ------------------------------------------------------------------------
  // | Control value accessor implements
  // ------------------------------------------------------------------------

  onChange: (value: TableFilter[]) => void = () => null;

  onTouched: () => void = () => null;

  writeValue(value: TableFilter[]): void {
    this.list = value || [];
    this.cdr.markForCheck();
  }

  registerOnChange(fn: (_: TableFilter[]) => void): void {
    this.onChange = fn;
  }

  registerOnTouched(fn: () => void): void {
    this.onTouched = fn;
  }
}
