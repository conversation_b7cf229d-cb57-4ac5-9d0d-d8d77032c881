import { Component, Input, OnInit } from '@angular/core';
import { MessageService } from '@haizhi/ng-hertz/message';
import { TableField, TableFilter, TableFilterWhere } from '../../../pages/tb-details/tb-data-preview/tb-data-preview.model';

@Component({
  selector: 'tb-condition-filter',
  templateUrl: './tb-condition-filter.component.html',
  styleUrls: ['./tb-condition-filter.component.scss']
})
export class TbConditionFilterComponent implements OnInit {
  @Input() where: TableFilterWhere;
  @Input() fields: TableField[];
  constructor() {}

  ngOnInit() {}
}
