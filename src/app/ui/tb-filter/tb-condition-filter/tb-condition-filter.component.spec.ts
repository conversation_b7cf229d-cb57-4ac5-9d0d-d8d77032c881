import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { TbConditionFilterComponent } from './tb-condition-filter.component';

describe('TbConditionFilterComponent', () => {
  let component: TbConditionFilterComponent;
  let fixture: ComponentFixture<TbConditionFilterComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [TbConditionFilterComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(TbConditionFilterComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
