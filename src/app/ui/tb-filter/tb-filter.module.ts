import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TbFilterListModule } from './tb-condition-filter/tb-filter-list/tb-filter-list.module';
import { TbConditionFilterComponent } from './tb-condition-filter/tb-condition-filter.component';
import { TbFilterComponent } from './tb-filter.component';
import { TbSqlFilterComponent } from './tb-sql-filter/tb-sql-filter.component';
import { FormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { CodemirrorModule } from 'ng2-codemirror';

@NgModule({
  declarations: [TbFilterComponent, TbSqlFilterComponent, TbConditionFilterComponent],
  imports: [CommonModule, TbFilterListModule, FormsModule, SharedModule, CodemirrorModule],
  exports: [TbFilterComponent, TbSqlFilterComponent, TbConditionFilterComponent]
})
export class TbFilterModule {}
