<section class="tb-filter">
  <article class="tb-filter-content">
    <ng-container>
      <div class="tb-filter-type">
        <div class="tb-filter-type-title">过滤方式</div>
        <div class="tb-filter-type-radio">
          <label>
            <input [(ngModel)]="where.where_type" type="radio" class="hz-input" value="condition" />
            条件过滤
          </label>
          <label>
            <input [(ngModel)]="where.where_type" type="radio" class="hz-input" value="sql" />
            表达式过滤
          </label>
        </div>
      </div>
      <ng-container *ngIf="where.where_type === 'condition'">
        <div class="tb-filter-content-condition">
          <tb-condition-filter [where]="where" [fields]="fields"></tb-condition-filter>
        </div>
      </ng-container>
      <ng-container *ngIf="where.where_type === 'sql'">
        <div class="tb-filter-content-sql">
          <tb-sql-filter [where]="where" [fields]="fields"></tb-sql-filter>
        </div>
      </ng-container>
    </ng-container>
  </article>
</section>
