import { Component, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material';
import { BdpOfflineTaskComponent } from '../bdp-offline-task/bdp-offline-task.component';
import { OverlayRef } from '@angular/cdk/overlay';
import { HttpService } from '../../core/services/http.service';
import { BehaviorSubject } from 'rxjs';
@Component({
  selector: 'export-worksheet-tip',
  templateUrl: './export-worksheet-tip.component.html',
  styleUrls: ['./export-worksheet-tip.component.scss']
})
export class ExportWorksheetTipComponent implements OnInit {
  taskNoticeOverlayRef: OverlayRef;
  offlineTaskUnread$ = new BehaviorSubject<boolean>(false);
  constructor(public dialog: MatDialog, private http: HttpService) {}

  ngOnInit() {}

  /**
   * 弹出离线任务列表弹窗
   */
  offlineTask() {
    // 开启弹层
    const dialogRef = this.dialog.open(BdpOfflineTaskComponent, {
      width: '844px',
      disableClose: true,
      data: {}
    });
    dialogRef.afterClosed().subscribe(() => {
      if (this.taskNoticeOverlayRef && this.taskNoticeOverlayRef.hasAttached()) {
        this.hideNotice();
      }
      this.offlineTaskUnread$.next(false);
    });
  }
  // 隐藏提示窗, 并告诉后端
  hideNotice() {
    this.http.post('/api/export/hide_notification').subscribe(data => {
      if (data) {
        if (this.taskNoticeOverlayRef && this.taskNoticeOverlayRef.hasAttached()) {
          this.taskNoticeOverlayRef.detach();
        }
      }
    });
  }
}
