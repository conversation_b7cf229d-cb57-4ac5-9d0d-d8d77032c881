import { Component, Input, Output, EventEmitter, OnInit } from '@angular/core';
import { ProductListItem } from '../../core/models/product-list.model';
import { HttpService } from '../../core/services/http.service';
import { UserInfoService } from '../../core/services/user-info.service';

@Component({
  selector: 'product-list',
  templateUrl: './product-list.component.html',
  styleUrls: ['./product-list.component.scss']
})
export class ProductListComponent implements OnInit {
  curProduct: ProductListItem;
  productList: ProductListItem[];
  constructor(private httpService: HttpService, private userInfoService: UserInfoService) {}

  ngOnInit() {
    this.productList = this.userInfoService.productList;
    this.curProduct = this.userInfoService.curProduct;
  }

  // 跳转路由
  jumpUrl(item: ProductListItem) {
    if (this.curProduct.pro_id === item.pro_id) {
      return;
    }
    this.httpService.get<string>(`/api/check/gen_redirect_url?pro_name=${item.pro_name}`).subscribe(res => {
      window.location.href = res;
    });
  }
}
