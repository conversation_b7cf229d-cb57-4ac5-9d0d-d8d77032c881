@import '~@haizhi/ui/styles/themes/light.data';
.item-list {
  z-index: 3000;
}

$product-name-list: 'ucenter', 'bdp', 'dmc', 'etl', 'tupu', 'gis', 'apicenter', 'machine_learning';
.product-list {
  display: inline-block;
  position: relative;
  font-size: 14px;
  user-select: none;
  vertical-align: middle;
  .item-list {
    background: $mono-400-light-white;
    border-radius: 8px;
    box-shadow: $container-soft-03;
    z-index: 1001;
    padding: 16px 8px;
    width: auto;
    box-sizing: border-box;
    ul {
      display: grid;
      grid-template-columns: auto auto auto;
      li {
        cursor: pointer;
        display: inline-flex;
        align-items: center;
        line-height: 32px;
        border-radius: 8px;
        padding: 4px 12px;
        margin: 4px;
        position: relative;
        z-index: auto;
        i {
          height: 32px;
          width: 32px;
          display: inline-block;
          background-image: url('../../../assets/images/products/default.svg');
          background-repeat: no-repeat;
          background-size: 100%;
          @each $name in $product-name-list {
            &.product-name-#{$name} {
              background-image: url('../../../assets/images/products/#{$name}.svg');
            }
          }
        }
        a {
          margin-left: 8px;
          display: inline-block;
          overflow: hidden;
          white-space: nowrap;
          font-weight: 600;
          color: $type-700;
        }
        &.active,
        &:hover {
          color: $primary-900;
          &::after {
            transform: scale(1);
            opacity: 1;
          }
        }
        &:hover {
          color: $primary-900;
        }
        &::after {
          content: '';
          display: block;
          background: rgba(43, 121, 255, 0.1);
          position: absolute;
          left: 0;
          top: 0;
          z-index: 0;
          width: 100%;
          height: 100%;
          border-radius: 8px;
          transform: scale(0.8);
          opacity: 0;
          transition: all 0.4s cubic-bezier(0.2, 1.2, 0.5, 0.9);
        }
      }
    }
  }
}
