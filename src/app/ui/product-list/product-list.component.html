<div class="product-list">
  <div class="item-list">
    <ul>
      <ng-container *ngFor="let item of productList">
        <li
          [ngClass]="{ active: item.pro_name === curProduct.pro_name }"
          *ngIf="item.own_has || item.group_has || item.chat_has"
          (click)="jumpUrl(item)"
        >
          <i class="product-name-{{ item.pro_name }}"></i>
          <a class="product-item">{{ item.name }}</a>
        </li>
      </ng-container>
    </ul>
  </div>
</div>
