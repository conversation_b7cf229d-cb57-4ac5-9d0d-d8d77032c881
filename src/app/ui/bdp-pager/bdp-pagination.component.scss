.pagination {
  display: inline-block;
  padding-left: 0;
  margin: 20px 0;
  border-radius: 4px;
}

.pagination > li {
  display: inline;
}

.pagination > li > a,
.pagination > li > span {
  position: relative;
  float: left;
  border-radius: 4px;
  padding: 6px 12px;
  margin-left: 4px;
  line-height: 1.42857143;
  color: rgba(5, 16, 33, 0.42);
  text-decoration: none;
  background-color: #fff;
  box-shadow: 0px 0px 8px rgba(16, 48, 102, 0.03), 0px 8px 16px rgba(16, 48, 102, 0.08);
}

.pagination > li:first-child > a,
.pagination > li:first-child > span {
  margin-left: 0;
  border-top-left-radius: 4px;
  border-bottom-left-radius: 4px;
  margin-right: 10px;
  color: rgba(5, 16, 33, 0.72);
  background-color: rgba(24, 43, 77, 0.05);
}

.pagination > li:first-child.disabled > a,
.pagination > li:first-child.disabled > span {
  background-color: transparent;
  box-shadow: none;
}

.pagination > li:last-child > a,
.pagination > li:last-child > span {
  border-top-right-radius: 4px;
  border-bottom-right-radius: 4px;
  margin-left: 10px;
  color: rgba(5, 16, 33, 0.72);
  background-color: rgba(24, 43, 77, 0.05);
}

.pagination > li:last-child.disabled > a,
.pagination > li:last-child.disabled > span {
  background-color: transparent;
  box-shadow: none;
}

.pagination > li > a:hover,
.pagination > li > span:hover,
.pagination > li > a:focus,
.pagination > li > span:focus {
  z-index: 2;
  color: rgba(5, 16, 33, 0.72);
  background-color: rgba(24, 43, 77, 0.05);
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  z-index: 3;
  color: #fff;
  cursor: default;
  color: rgba(5, 16, 33, 0.72);
  background-color: rgba(24, 43, 77, 0.05);
}

.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus {
  color: #777;
  cursor: not-allowed;
  background-color: #fff;
  border-color: #ddd;
}
