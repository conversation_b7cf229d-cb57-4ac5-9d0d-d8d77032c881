import { finalize } from 'rxjs/operators';
import { Component, Inject, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { WebsocketService } from '../../core/services/websocket.service';
import { CommonDataService } from '../../core/services/common-data.service';
import { HttpService } from '../../core/services/http.service';

@Component({
  selector: 'bdp-offline-task',
  templateUrl: './bdp-offline-task.component.html',
  styleUrls: ['./bdp-offline-task.component.scss']
})
export class BdpOfflineTaskComponent implements OnInit {
  fieldId: any;
  folderList: Array<any>;
  activeTabIndex = 1;
  exportDataList: Array<any> = [];
  importDataList: Array<any> = [];
  dataList: Array<any> = [];
  loading = false;
  taskType: Array<any> = [];
  taskTypeMode: any;
  lastTypeMode: any; // 记录上一次 切换taskTypeMode
  // optList = false;
  websocketSubscription: any;
  pageInfo: any = {
    currentPage: 1,
    maxSize: 5,
    itemsPerPage: 5,
    totalItems: 0,
    totalPage: 2
  };
  taskTypeMap: { [index: string]: string } = {
    0: '全部',
    1: '图表导出',
    2: '数据表导出',
    3: '整体方案导出',
    5: '格式校验'
  };
  constructor(
    public dialogRef: MatDialogRef<BdpOfflineTaskComponent>,
    private commonData: CommonDataService,
    private http: HttpService,
    // private tableManageApi: workTableManageApiService,
    private websocket: WebsocketService,
    @Inject(MAT_DIALOG_DATA) public dialogData: any
  ) {
    this.websocketSubscription = this.websocket.listener.subscribe((value: any) => {
      if (value.type === 2) {
        this.getOfflineData(0);
      }
    });
  }

  ngOnInit(): void {
    this.taskType = [
      { type: 0, name: '全部' },
      { type: 1, name: '图表导出' },
      { type: 2, name: '数据表导出' },
      { type: 3, name: '整体方案导出' },
      { type: 5, name: '格式校验' }
    ];
    this.taskTypeMode = 0;
    this.lastTypeMode = this.taskTypeMode;
    this.fieldId = '';
    this.folderList = this.dialogData.fieldList;
    this.getOfflineData(0);
  }
  /**
   * 获取离线数据列表
   */
  async getOfflineData(param: any) {
    this.loading = true;
    const parm: any = {
      page: this.pageInfo.currentPage,
      limit: this.pageInfo.itemsPerPage
    };
    if (this.lastTypeMode !== this.taskTypeMode) {
      parm.page = 1;
      this.pageInfo.currentPage = 1;
      this.lastTypeMode = this.taskTypeMode;
    }
    if (this.taskTypeMode !== 0) {
      parm.task_type = this.taskTypeMode;
    } else {
      parm.is_import = param;
    }
    const self = this;
    await this.http
      .post('/api/export/file_list', parm)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(data => {
        if (param === 0) {
          self.activeTabIndex = 0;
          self.importDataList = self.importDataList.length > 0 ? self.importDataList : self.dataList;
          self.dataList = data.files;
          self.exportDataList = self.dataList;
          self.pageInfo.totalItems = data.total_pages * self.pageInfo.itemsPerPage;
        } else {
          self.activeTabIndex = 1;
          self.exportDataList = self.exportDataList.length > 0 ? self.exportDataList : self.dataList;
          self.dataList = data.files;
          self.importDataList = self.dataList;
          self.pageInfo.totalItems = data.total_pages * self.pageInfo.itemsPerPage;
        }
        // this.pageInfo.currentPage = 1; // 离线任务列表 切换page 时 自动跳转到第1页
      });
  }
  async pageChanged(event: any) {
    await this.getOfflineData(0);
  }
  /**
   * 改变任务类型
   */
  download(file: any) {
    const downloadUrl = file.link + '&task_id=' + file.task_id;
    window.location.href = downloadUrl;
  }
  /**
   * 全部删除
   */
  delAllTask(type: any) {
    let params = {};
    if (type instanceof Object) {
      params = {
        task_ids: JSON.stringify([type.task_id])
      };
    } else {
      params = {
        task_type: JSON.stringify(this.taskTypeMode)
      };
    }
    this.http.post('/api/export/delete', params).subscribe(data => {
      this.commonData.globalTip.set('删除成功');
      this.getOfflineData(this.activeTabIndex);
    });
  }
  onOkClick() {
    if (this.fieldId !== '') {
      this.dialogRef.close(this.fieldId);
    } else {
    }
  }
  onCancelClick() {
    this.dialogRef.close(0);
  }
}
