<div>
  <div mat-dialog-title class="top-content">
    <span>离线任务列表</span>
    <span><i hz-icon hzName="f-close" hzSize="20px" (click)="onCancelClick()"></i></span>
  </div>
  <mat-dialog-content>
    <div class="task-type">
      <span>任务类型</span>
      <hz-select
        [hzSize]="'small'"
        class="type-select"
        [(ngModel)]="taskTypeMode"
        hzPlaceholder="请选择"
        (ngModelChange)="getOfflineData($event)"
        style="width: 120px"
      >
        <hz-option
          *ngFor="let item of taskType; let i = index"
          [hzLabel]="item.name"
          [hzValue]="item.type"
        ></hz-option>
      </hz-select>
    </div>
    <div *ngIf="dataList.length > 0" class="table-content">
      <nz-table [nzData]="dataList" [nzShowPagination]="false">
        <thead>
          <tr>
            <th>任务时间</th>
            <th>文件名称</th>
            <th>任务类型</th>
            <th>文件大小</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let row of dataList">
            <td>{{ row.ctime }}</td>
            <td class="file-name">{{ row.name }}</td>
            <td>
              {{ taskTypeMap[row.task_type] }}
            </td>
            <td>{{ row.size }}</td>
            <td>
              <span *ngIf="row.status == '0'">成功</span>
              <span *ngIf="row.status == '1'">执行中...</span>
              <span *ngIf="row.status != 0 && row.status != 1">
                失败
                <i
                  class="ml4"
                  hz-icon
                  hzName="attention"
                  hz-tooltip
                  [hzTooltipTitle]="row.error"
                  [hzTooltipPlacement]="'top'"
                  [hzTooltipType]="'info'"
                ></i>
              </span>
            </td>
            <td class="button-content">
              <button hz-button hzType="action" *ngIf="row.link != ''" (click)="download(row)">
                <i hz-icon hzName="download"></i>
                <span>下载</span>
              </button>
            </td>
          </tr>
        </tbody>
      </nz-table>
      <div class="pagenation" *ngIf="pageInfo.totalItems > pageInfo.maxSize">
        <hz-pagination
          [hzTotal]="pageInfo.totalItems"
          [hzPageSize]="pageInfo.maxSize"
          [(hzPageIndex)]="pageInfo.currentPage"
          (hzPageIndexChange)="pageChanged($event)"
        ></hz-pagination>
      </div>
    </div>
    <div *ngIf="dataList.length == 0" class="offline-container">
      <div class="download-none-title">
        <i hz-icon hzName="info-sign" hzSize="106px"></i>
        <div>未触发离线下载任务</div>
      </div>
      <div class="export-none-tip">
        某些情况下，导出excel文件时，将自动采用离线方式导出，以便您有更快的导出速度
      </div>
    </div>
  </mat-dialog-content>
  <hz-loading-gif *ngIf="loading"></hz-loading-gif>
</div>
