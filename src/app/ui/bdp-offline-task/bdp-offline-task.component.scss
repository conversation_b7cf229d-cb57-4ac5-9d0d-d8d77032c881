.offline-container {
  height: 300px;
}
.top-content {
  display: flex;
  justify-content: space-between;
  span {
    cursor: pointer;
  }
}
.table-content {
  padding: 10px 0;
  .file-name {
    max-width: 200px;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;
  }
  .pagenation {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
  }
  .button-content {
    position: relative;
    .button {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
}
.task-type {
  display: flex;
  align-items: center;
  span {
    font-weight: 600;
    line-height: 20px;
    color: rgba(21, 22, 24, 0.36);
    margin-right: 16px;
  }
}
.download-none-title {
  padding-top: 40px;
  font-size: 14px;
  text-align: center;
}
.export-none-tip {
  text-align: center;
}
