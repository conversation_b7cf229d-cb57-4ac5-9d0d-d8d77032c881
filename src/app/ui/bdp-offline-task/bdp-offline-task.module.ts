import { SharedModule } from 'src/app/shared/shared.module';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BdpOfflineTaskComponent } from './bdp-offline-task.component';
import { MatDialogModule } from '@angular/material/dialog';
import { NzTableModule } from 'ng-zorro-antd/table';
import { FormsModule } from '@angular/forms';
import { IconModule } from '@haizhi/ng-hertz/icon';
@NgModule({
  declarations: [BdpOfflineTaskComponent],
  imports: [CommonModule, MatDialogModule, NzTableModule, SharedModule, FormsModule, IconModule],
  entryComponents: [BdpOfflineTaskComponent],
  exports: [BdpOfflineTaskComponent]
})
export class BdpOfflineTaskModule {}
