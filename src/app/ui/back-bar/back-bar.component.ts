import { ChangeDetectionStrategy, Component, EventEmitter, Input, OnInit, Output } from '@angular/core';

@Component({
  selector: 'back-bar',
  templateUrl: './back-bar.component.html',
  styleUrls: ['./back-bar.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class BackBarComponent implements OnInit {
  @Input() title = '';
  @Output() backIconClick = new EventEmitter();
  constructor() {}

  ngOnInit() {}

  goBack() {
    this.backIconClick.emit();
  }
}
