.layout-header {
  height: 56px;
  display: flex;
  align-items: center;
  background: linear-gradient(0deg, #06195c, #06195c), #0a2999;
  position: relative;
  font-size: 14px;
  box-shadow: 0px 4px 8px rgba(15, 34, 67, 0.03), 0px 1px 3px rgba(15, 34, 67, 0.08), 0px 0px 1px rgba(15, 34, 67, 0.16);
  box-shadow: var(--container-c200);
  .logo-link {
    margin-left: 22px;
    z-index: 10;
    img {
      height: 32px;
      object-fit: cover;
    }
  }
  .main-title {
    margin-left: 16px;
    font-size: 20px;
    font-weight: 600;
    color: #ffffff;
  }
  .breadcrumb-wrap {
    margin-left: 16px;
    z-index: 10;
    font-weight: 600;
    a {
      color: rgba(255, 255, 255, 0.7);
    }
    .midway-path,
    .out-path {
      display: inline-block;
      text-overflow: ellipsis;
      max-width: 160px;
      overflow: hidden;
      white-space: nowrap;
      line-height: 20px;
      vertical-align: middle;
    }
    ::ng-deep .hz-breadcrumb-separator {
      vertical-align: middle;
    }
    .out-path {
      max-width: 200px;
    }
  }
  &-video {
    height: 64px;
    width: 904px;
    object-fit: contain;
    position: absolute;
    left: 0;
    top: 0;
    opacity: 0.7;
  }
  .gradient-wrap {
    position: absolute;
    left: 760px;
    top: 0;
    height: 64px;
    width: 144px;
    background: linear-gradient(270deg, #003899 0%, rgba(0, 56, 153, 0) 100%);
  }
  &-change-theme {
    color: var(--general-100);
    position: absolute;
    right: 24px;
    top: 24px;
    cursor: pointer;
    user-select: none;
  }
  .product-list-entry {
    color: rgba(255, 255, 255, 0.7);
    font-size: 14px;
    font-weight: 600;
    margin-left: 18px;
    z-index: 10;
    display: none; // 元数据不支持产品跳转
    &.open {
      &:after {
        opacity: 1;
        transform: scale(1);
      }
    }
    &:after {
      background: rgba(43, 121, 255, 0.24);
    }
    i {
      transition: all ease-in-out 0.2s;
    }
  }
}
