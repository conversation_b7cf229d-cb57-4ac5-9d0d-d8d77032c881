import { Component, Input, On<PERSON><PERSON>roy, OnInit } from '@angular/core';
import { PersonalizationConfig, UserInfoPartial } from '../../core/models/user-info.model';
import { UserInfoService } from '../../core/services/user-info.service';
import { ProductListItem } from '../../core/models/product-list.model';
import { Subject } from 'rxjs';

@Component({
  selector: 'hz-layout-header',
  templateUrl: './layout-header.component.html',
  styleUrls: ['./layout-header.component.scss']
})
export class LayoutHeaderComponent implements OnInit, OnDestroy {
  config: PersonalizationConfig;
  defaultLogo = './assets/images/logo.png';
  videoUrl = './assets/video/dot_bg.mp4';
  theme = 'light';
  productList: ProductListItem[];
  curProductName: string;
  visible: boolean;
  userInfo: UserInfoPartial;
  breadcrumbPath: Array<{ name: string }>;
  destroy$ = new Subject();

  @Input('userInfo') set changeUserInfo(value: UserInfoPartial) {
    if (value) {
      this.userInfo = value;
      if (this.userInfo) {
        this.config = { ...this.userInfo.personalization_setting };
      }
    }
  }
  constructor(private userInfoService: UserInfoService) {}

  ngOnInit() {
    this.productList = this.userInfoService.productList || [];
    if (this.userInfoService.curProduct) {
      this.curProductName = this.userInfoService.curProduct.name;
    } else {
      this.curProductName = 'DMC';
    }
    this.userInfoService.breadcrumb$.subscribe(path => {
      this.breadcrumbPath = path;
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  changeLogo() {
    this.config.custom_logo_url = this.defaultLogo;
  }

  redirectTo() {
    if (this.config.redirect_permission) {
      window.location.href = this.config.logo_redirect;
    }
  }

  changeTheme() {
    const theme = document.documentElement.getAttribute('theme');
    this.theme = theme === 'light' ? 'dark' : 'light';
    localStorage.setItem('ng-hertz-doc-theme', this.theme);
    document.documentElement.setAttribute('theme', this.theme);
    // for (const key in this.dark) {
    //   if ( this.dark.hasOwnProperty(key)) {
    //     document.documentElement.style.setProperty(key, this.dark[key]);
    //   }
    // }
  }
}
