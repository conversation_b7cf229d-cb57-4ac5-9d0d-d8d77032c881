<div class="layout-header">
  <!-- <video class="layout-header-video" [src]="videoUrl" autoplay loop muted playsinline></video> -->
  <!-- <div class="gradient-wrap"></div> -->
  <a
    class="logo-link"
    *ngIf="config"
    [ngClass]="{ 'cursor-pointer': config.redirect_permission }"
    (click)="redirectTo()"
  >
    <img
      [src]="(config.custom_logo && config.custom_logo_url) || defaultLogo"
      (error)="changeLogo()"
    />
  </a>
  <div class="main-title">
    {{ curProductName }}
  </div>
  <ng-container *ngIf="productList?.length > 0">
    <button
      class="product-list-entry"
      hz-button
      hzType="action"
      hzSize="m"
      hz-popover
      hzPopoverTrigger="click"
      [(hzVisible)]="visible"
      [hzVisibleClass]="'open'"
      [hzPopoverPlacement]="'bottomLeft'"
      [hzPopoverTemplate]="popoverTemplate"
    >
      <span>{{ curProductName }}</span>
      <i
        hz-icon
        hzName="triangle"
        hzColor="#ffffff"
        [ngStyle]="{ transform: visible ? 'rotate(180deg)' : 'rotate(0deg)' }"
      ></i>
    </button>
  </ng-container>
  <ng-container *ngIf="productList?.length === 0">
    <div class="product-list-entry">
      <span>{{ curProductName }}</span>
    </div>
  </ng-container>
  <div class="breadcrumb-wrap" *ngIf="breadcrumbPath && breadcrumbPath.length > 0">
    <hz-breadcrumb hzSize="medium">
      <hz-breadcrumb-item
        *ngFor="let item of breadcrumbPath; let last = last"
        [hzColor]="last ? 'rgba(255,255,255, 1)' : 'rgba(255,255,255,0.7)'"
      >
        <span [title]="item.name" class="midway-path" *ngIf="!last && item.name">
          {{ item.name }}
        </span>
        <span [title]="item.name" class="out-path" *ngIf="last && item.name">{{ item.name }}</span>
      </hz-breadcrumb-item>
    </hz-breadcrumb>
  </div>
  <ng-content></ng-content>
</div>
<ng-template #popoverTemplate>
  <product-list></product-list>
</ng-template>
