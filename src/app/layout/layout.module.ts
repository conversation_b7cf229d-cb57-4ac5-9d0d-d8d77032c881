import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { LayoutComponent } from './layout.component';
import { LayoutHeaderComponent } from './layout-header/layout-header.component';
import { RouterModule } from '@angular/router';
import { IconModule } from '@haizhi/ng-hertz/icon';
import { FullScreenComponent } from './full-screen/full-screen.component';
import { PopoverModule } from '@haizhi/ng-hertz/popover';
import { ProductListModule } from '../ui/product-list/product-list.module';
import { ButtonModule } from '@haizhi/ng-hertz/button';
import { BreadcrumbModule } from '@haizhi/ng-hertz/breadcrumb';

@NgModule({
  declarations: [LayoutComponent, LayoutHeaderComponent, FullScreenComponent],
  imports: [CommonModule, RouterModule, IconModule, PopoverModule, ProductListModule, ButtonModule, BreadcrumbModule],
  exports: [LayoutComponent, FullScreenComponent]
})
export class LayoutModule {}
