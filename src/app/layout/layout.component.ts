import { Component, <PERSON><PERSON><PERSON>roy, OnInit } from '@angular/core';
import { UserInfoPartial } from '../core/models/user-info.model';
import { ProductListItem } from '../core/models/product-list.model';
import { Subject } from 'rxjs';
import { UserInfoService } from '../core/services/user-info.service';
import { takeUntil } from 'rxjs/operators';

@Component({
  selector: 'hz-layout',
  templateUrl: './layout.component.html',
  styleUrls: ['./layout.component.scss']
})
export class LayoutComponent implements OnInit, OnDestroy {
  userInfo: UserInfoPartial;
  destroy$ = new Subject<void>();
  constructor(private userInfoService: UserInfoService) {}

  ngOnInit() {
    this.userInfoService.userInfo$.pipe(takeUntil(this.destroy$)).subscribe(info => {
      this.userInfo = info;
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
