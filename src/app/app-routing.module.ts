import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { UserInfoResolverService } from './core/guards/user-info-resolver.service';
import { LayoutComponent } from './layout/layout.component';
import { FullScreenComponent } from './layout/full-screen/full-screen.component';
import { AuthGuard } from './core/guards/auth.guard';

const routes: Routes = [
  {
    path: '',
    resolve: { userInfo: UserInfoResolverService },
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        component: LayoutComponent,
        children: [
          {
            path: ':tbId',
            loadChildren: () => import('./pages/tb-details/tb-details.module').then(mod => mod.TbDetailsModule)
          }
        ]
      },
      {
        path: '',
        component: FullScreenComponent,
        children: [
          {
            path: 'version-compare/:tbId',
            loadChildren: () => import('./pages/tb-details/version-compare/version-compare.module').then(mod => mod.VersionCompareModule)
          }
        ]
      }
    ]
  }
];

@NgModule({
  imports: [RouterModule.forRoot(routes)],
  exports: [RouterModule]
})
export class AppRoutingModule {}
