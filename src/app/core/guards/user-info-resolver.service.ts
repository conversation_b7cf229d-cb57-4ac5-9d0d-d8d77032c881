import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, Resolve, RouterStateSnapshot } from '@angular/router';
import { Observable, of } from 'rxjs';
import { UserInfoService } from '../services/user-info.service';
import { HttpService } from '../services/http.service';

@Injectable({
  providedIn: 'root'
})
export class UserInfoResolverService implements Resolve<any> {
  constructor(private http: HttpService, private userInfoService: UserInfoService) {}

  resolve(route: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<any> {
    return new Observable(observer => {
      this.userInfoService.getUserInfo().subscribe(
        result => {
          observer.next(of(result));
          observer.complete();
        },
        () => {
          observer.next(of(null));
          observer.complete();
        }
      );
    });
  }
}
