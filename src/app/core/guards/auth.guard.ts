import { Injectable } from '@angular/core';
import { ActivatedRouteSnapshot, RouterStateSnapshot, CanActivate, Router } from '@angular/router';
import { Observable } from 'rxjs';
import { UserInfoService } from '../services/user-info.service';
import { HttpService } from '../services/http.service';
import { ProductListItem } from '../models/product-list.model';

@Injectable({
  providedIn: 'root'
})
export class AuthGuard implements CanActivate {
  constructor(private userInfoService: UserInfoService, private router: Router, private http: HttpService) {}

  canActivate(next: ActivatedRouteSnapshot, state: RouterStateSnapshot): Observable<boolean> {
    return new Observable(observer => {
      const productList$ = this.http.get('/api/ucenter/product/list');
      productList$.subscribe(
        result => {
          const productList = result;
          this.userInfoService.productList = result;
          const ucenterProduct = (productList as ProductListItem[]).find(e => e.pro_name === 'ucenter');
          const curProduct = (productList as ProductListItem[]).find(e => e.pro_name === 'dmc');
          this.userInfoService.curProduct = curProduct;
          if (curProduct) {
            if (curProduct.own_has || curProduct.chat_has || curProduct.group_has) {
              observer.next(true);
              observer.complete();
            } else {
              observer.next(true);
              observer.complete();
              if (ucenterProduct) {
                window.location.href = ucenterProduct.no_auth_page;
              }
            }
          } else {
            observer.next(true);
            observer.complete();
            if (ucenterProduct) {
              window.location.href = ucenterProduct.no_auth_page;
            }
          }
        },
        () => {
          observer.next(true);
          observer.complete();
        }
      );
    });
  }
}
