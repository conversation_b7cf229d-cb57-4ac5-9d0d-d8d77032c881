export interface UserInfo {
  name: string; // 超级管理员
  user_id: string; // 用户id
  role: UserRole; // 角色类型
  email: string; // 邮箱
  behe_role: number; // beHemous角色
  username: string; // 登陆用户名
  sex: '男' | '女'; // 性别
  data_source_manage: NumBool;
  domain: string; // 企业域
  server_time: number; // 时间戳
  share_outside_group: NumBool; // 组内共享开关
  share_inside_group: NumBool; // 组外分享开关
  dsh_manage: NumBool;
  tb_manage: NumBool;
  is_personalized: NumBool;
  sort_type: number;
  url: string;
  verify_email: NumBool; // 邮箱是否验证
  theme_id: number;
  machine_learning_permission: NumBool;
  enterprise_type: EnterpriseType;
  view_model_permission: NumBool;
  last_login_time: string; // "2019-12-18 19:53:34"
  has_new_file: boolean; // 是否显示有离线任务完成的小红点
  show_export_notification: boolean; // 是否显示文件导出完成的通知
  auth_info: any;
  tml_manage: NumBool;
  data_screen: NumBool;
  dsh_permission: NumBool;
  customize_permission: NumBool; // 1是有权限， 0是没权限（只有超管可能为1）
  security_info: UserInfoSecurityInfo;
  image_name: string;
  date_filter_permission: NumBool;
  push_setting: {
    warn?: {
      app: NumBool;
      email: NumBool;
    };
  };
  guide: number;
  data_permission: NumBool;
  new_guide: UserInfoNewGuide;
  should_complete_profile: NumBool;
  tb_permission: NumBool;
  data_to_tp: NumBool;
  photo_id: number;
  ctime: string; // "2019-07-17 15:17:18"
  obj_id: string;
  mobile: string; // "137****9317"
  industry: number;
  personalize_info: any;
  account_permission: number;
  is_extrace: NumBool; // 开启提取数据表权限
  contact: string;
  position: string;
  function_manage: NumBool;
  personalization_setting: PersonalizationConfig; // 个性化设置
}

type NumBool = 0 | 1;
type EnterpriseType = 0 | 1 | 2 | 3 | 4; // 0:正常账号 1：合作账号（畅捷通）2：合作账号（软通政企云）3：个人版4：渠道账号升级 5：商派合作账号
type UserRole = 0 | 1 | 2 | 3 | 4; // 0=>企业版超级管理员 1=>为个人版主账户 2=>为企业版普通管理员 3=>为企业版普通账号 4=>只读账号

interface UserInfoSecurityInfo {
  mob_screenshot_setting: any;
  export_data_setting: UserExportSetting;
  watermark_strategy: NumBool;
  share_outside_group: NumBool; // 组内共享开关
  share_inside_group: NumBool; // 组外分享开关
  export_data_to_tp_setting: UserExportSetting;
  close_browser_token_failure: number;
  tb_export_data_setting: UserExportSetting;
  select_data_setting: any;
  etl_tb_export: UserExportSetting;
  data_export_to_db: NumBool; // DMC-6960，自主建模-个人数据-导出任务 tab 开关
  chart_export_data_setting: UserExportSetting; // DMC-6960，多维可视图表导出开关
  dash_export_snapshot_setting: NumBool; // DMC-6960，多维可视仪表盘快照导出开关
}

interface UserInfoNewGuide {
  chartArgsSetDefaultGuide: NumBool;
  tableGuideDashColWidthChange: NumBool;
  __cell_layer_guide__: NumBool;
  customChartStoreGuide: NumBool;
  __width_change_guide__: NumBool;
  __wordwrap_guide__: NumBool;
  customChartFieldGuide: NumBool;
  noviceCloseGuide: NumBool;
  custom1step: NumBool;
  newCustomChartGuide: NumBool;
  chartArgsMainGuide: NumBool;
  tableCopyGuide: NumBool;
  chartArgsEditInnerGuide: NumBool;
  btnNewChartGuide: NumBool;
  copyDashTplGuide: NumBool;
  chartArgsNewField: NumBool;
  customChartTemplateGuide: NumBool;
  chartArgsDashboard: NumBool;
  chartArgsNew: NumBool;
  ds_limit_tip: NumBool; // 个人用户会员到期数据提示
  chartArgsUseGuide: NumBool;
}

export interface PersonalizationConfig extends WatermarkForm {
  custom_logo: 0 | 1;
  custom_logo_url: string;
  custom_icon: 0 | 1;
  custom_icon_url: string;
  redirect_permission: 0 | 1;
  logo_redirect: string;
  admin_mpop: 0 | 1;
  data_copy: 0 | 1;
  page_title: string;
  login_failure_setting: LoginFailureSetting;
}

export interface LoginFailureSetting {
  failure_enable: 0 | 1;
  verify_code_setting: {
    verify_code_enable: 0 | 1;
    verify_code_count: number;
  };
  temp_lock_setting: {
    temp_lock_enable: 0 | 1;
    temp_lock_count: number;
    temp_lock_time: {
      type: 0 | 1 | 2 | 3;
      value: number;
    };
  };
}

export interface WatermarkForm {
  watermark_open: 0 | 1;
  watermark_config: WatermarkConfig;
}

export interface WatermarkConfig {
  layout: 0 | 1;
  content: Array<Array<number>>;
  warning: string;
  content_space: number;
  x_gap: number;
  y_gap: number;
  font: {
    size: number;
    bold: 0 | 1;
  };
  color: string;
  alpha: number; // 透明度（百分比）
  rotate: {
    rotate_type: number;
    value: number;
  };
  ip: string;
}

export interface UserExportSetting {
  is_export: NumBool;
  export_max_num: number;
  export_type?: NumBool;
}

export type UserInfoPartial = Partial<UserInfo>;
