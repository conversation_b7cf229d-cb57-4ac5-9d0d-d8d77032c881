export interface ProductListItem {
  pro_des: string; // "用户中心旨在将BDP现有的产品模型超市、知识图谱、推送中心等帐号权限进行打通，所有产品共用一份用户体系，方便管理，同时进行各个子产品登陆权限的管控。"
  name: string; // "用户中心"
  url: string; // "http://dmc.haizhi.com/ucenter/index.html#/account/personal"
  sub_modules: Array<ProductListItemSubModule>;
  pro_id: string;
  own_has: 0 | 1; // 自己拥有
  chat_has: 0 | 1; //
  params: {
    set_token_url: string; // "http://dmc.haizhi.com/api/ucenter/check/valid_ec_url";
    logout_url: string;
  };
  has_modules: 0 | 1;
  roles_permission: Array<number>; // [3, 2]
  no_auth_page: string; // "http://dmc.haizhi.com/ucenter/index.html#/noauthority"
  pic_url: string; // "www.tupu.com"
  group_has: 0 | 1; // 组拥有
  pro_name: ProductNameType; // 项目名称
}

export enum ProductName {
  UserCenter = 'ucenter', // 用户中心
  Bdp = 'bdp', // 多维可视
  Dmc = 'dmc', // DMC
  Etl = 'etl', // 自主建模
  Tupu = 'tupu', // 知识图谱
  Gis = 'gis', // 时空分析
  ApiCenter = 'apicenter' // 用户中心
}

type ProductNameType = 'ucenter' | 'bdp' | 'dmc' | 'etl' | 'tupu' | 'gis' | 'apicenter';

interface ProductListItemSubModule {
  url: string;
  module_id: string;
  name: string;
  module_name: string;
}
