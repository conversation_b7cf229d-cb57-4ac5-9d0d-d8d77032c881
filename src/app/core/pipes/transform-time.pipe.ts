import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'transformTime'
})
export class TransformTimePipe implements PipeTransform {
  transform(value: number): string {
    const arr = [];
    if (value >= 0) {
      arr.unshift((value % 60) + '秒');
      value = Math.floor(value / 60);
      if (value > 0) {
        arr.unshift((value % 60) + '分');
        value = Math.floor(value / 60);
        if (value > 0) {
          arr.unshift((value % 24) + '小时');
          if (value > 24) {
            arr.unshift(Math.floor(value / 24) + '天');
          }
        }
      }
    }
    return arr.join('');
  }
}
