import { Pipe, PipeTransform } from '@angular/core';
import * as _ from 'lodash';

@Pipe({
  name: 'filterList',
  pure: false
})
export class FilterListPipe implements PipeTransform {
  constructor() {}
  transform(value: any, ...args: any[]): any[] {
    if (!args[0]) {
      return value;
    } else {
      const arr: any[] = [];
      const keyword = args[0].toLocaleLowerCase();
      const queryKey = args[1];
      let queryKey2 = '';
      if (args[2]) {
        queryKey2 = args[2];
      }
      if (args.length > 2) {
        _.forEach(value, val => {
          if (val[queryKey].toLocaleLowerCase().indexOf(keyword) > -1 || val[queryKey2].toLocaleLowerCase().indexOf(keyword) > -1) {
            arr.push(val);
          }
        });
      } else {
        _.forEach(value, val => {
          if (val[queryKey].toLocaleLowerCase().indexOf(keyword) > -1) {
            arr.push(val);
          }
        });
      }
      return arr;
    }
  }
}
