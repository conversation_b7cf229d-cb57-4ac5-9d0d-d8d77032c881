import { Pipe, PipeTransform } from '@angular/core';

@Pipe({
  name: 'transformData'
})
export class TransformDataPipe implements PipeTransform {
  transform(value: number): string {
    let arr = [];
    arr = (value + '').split('');
    arr.push('条');
    if (arr.length > 5) {
      arr.splice(-5, 0, '万');
      if (arr.length > 10) {
        arr.splice(-10, 0, '亿');
      }
    }
    return arr.join('');
  }
}
