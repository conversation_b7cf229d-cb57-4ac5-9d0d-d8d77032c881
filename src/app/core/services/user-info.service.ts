import { Injectable, Renderer2, RendererFactory2 } from '@angular/core';
import { BehaviorSubject, Observable, Subject } from 'rxjs';
import { HttpService, ObsRequestResponse } from './http.service';
import { Title } from '@angular/platform-browser';
import { tap } from 'rxjs/operators';
import { UserInfoPartial } from '../models/user-info.model';
import { Watermark } from 'hz-web-common';
import { ProductListItem } from '../models/product-list.model';

@Injectable({
  providedIn: 'root'
})
export class UserInfoService {
  userInfo: UserInfoPartial;
  userInfo$ = new BehaviorSubject<UserInfoPartial>(null);
  renderer: Renderer2;
  productList: ProductListItem[];
  curProduct: ProductListItem;
  breadcrumb$ = new Subject<Array<{ name: string }>>();
  constructor(private http: HttpService, private title: Title, private rendererFactory: RendererFactory2) {
    this.userInfo$.subscribe(info => {
      if (info) {
        this.userInfo = info;
        this.updatePage();
        this.watermarkInit(info);
      }
    });
  }

  getUserInfo(): Observable<UserInfoPartial> {
    return this.http.get('/api/ucenter/user/info').pipe(
      tap(data => {
        this.userInfo$.next(data);
      })
    );
  }

  logout() {
    this.http.get('/api/ucenter/user/logout').subscribe();
  }

  /**
   * 更新页面相关展示
   */
  updatePage() {
    // this.setAppTitle();
    this.setAppFavicon();
  }

  /**
   * 设置应用页面 title
   */
  setAppTitle() {
    if (this.userInfo.personalization_setting) {
      this.title.setTitle(this.userInfo.personalization_setting.page_title);
    }
  }

  /**
   * 设置应用页面 favicon
   */
  setAppFavicon() {
    if (this.userInfo.personalization_setting && this.userInfo.personalization_setting.custom_icon) {
      const url = this.userInfo.personalization_setting.custom_icon_url;
      document.getElementById('hz-favicon-ico').setAttribute('href', url);
    }
  }

  /**
   * 初始化水印
   * @param userInfo 用户信息
   */
  watermarkInit(userInfo: UserInfoPartial) {
    this.renderer = this.rendererFactory.createRenderer(null, null);
    if (userInfo.personalization_setting && userInfo.personalization_setting.watermark_open) {
      const contentInfoItems: Array<string> = [];
      const wConfig = userInfo.personalization_setting.watermark_config;
      contentInfoItems.push(userInfo.username || '');
      contentInfoItems.push(userInfo.contact || userInfo.name || '');
      contentInfoItems.push(userInfo.last_login_time || '');
      contentInfoItems.push(wConfig.ip || '');
      contentInfoItems.push(userInfo.domain || '');
      contentInfoItems.push(wConfig.warning || '');
      const content = wConfig.content.map((item: Array<number>) => item.map(e => contentInfoItems[e]));
      const params = Object.assign({}, wConfig, { content });
      const waterMark = new Watermark(params);
      const waterMarkElement = this.renderer.createElement('div');
      this.renderer.addClass(waterMarkElement, 'water-mark-dark');
      this.renderer.setAttribute(waterMarkElement, 'id', 'water-mark-dark');
      this.renderer.setStyle(waterMarkElement, 'pointer-events', 'none');
      this.renderer.setStyle(waterMarkElement, 'position', 'absolute');
      this.renderer.setStyle(waterMarkElement, 'z-index', '100000');
      this.renderer.setStyle(waterMarkElement, 'height', '100%');
      this.renderer.setStyle(waterMarkElement, 'width', '100%');
      this.renderer.setStyle(waterMarkElement, 'left', '0');
      this.renderer.setStyle(waterMarkElement, 'top', '0');
      this.renderer.appendChild(document.body, waterMarkElement);
    }
  }
}
