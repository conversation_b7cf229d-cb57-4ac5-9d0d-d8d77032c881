import { Injectable } from '@angular/core';
import 'rxjs/add/operator/map';
import { Socket, SocketIoConfig } from 'ng-socket-io';

export const config: SocketIoConfig = { url: '', options: { path: '', reconnectionDelayMax: 10000, reconnection: false } };

@Injectable({
  providedIn: 'root'
})
export class ChatService {
  constructor(private socket: Socket) {}

  sendMessage(msg: string) {
    this.socket.emit('message', msg);
  }

  getMessage() {
    return this.socket.fromEvent<any>('msg').map(data => data.msg);
  }

  close() {
    this.socket.disconnect();
  }
}
