import { Injectable } from '@angular/core';
import { HttpService } from './http.service';
import * as _ from 'lodash';

@Injectable()
export class FormulaListService {
  constructor(private http: HttpService) {}

  formulaList: any = {
    base: [],
    all: []
  };

  getFormulaList(): Promise<any> {
    return new Promise<any>((resolve, reject) => {
      if (this.formulaList.base.length > 0) {
        resolve(this.formulaList);
      } else {
        this.http.get('/api/function/list').subscribe(
          data => {
            this.formulaListHandle(data);
            resolve(this.formulaList);
          },
          error => {
            resolve(this.formulaList);
          }
        );
      }
    });
  }

  formulaListHandle(data: any) {
    const functionArr = data.classification;
    const functionMap = {};
    _.forEach(functionArr, (val, index) => {
      functionMap[index] = [];
      _.forEach(functionArr[index], (i, it) => {
        functionMap[index].push({
          usage: i.usage,
          description: i.desc,
          example: i.demo,
          type: i.name
        });
      });
    });
    this.formulaList.base = functionMap[2].concat(functionMap[3]).concat(functionMap[4]).concat(functionMap[5]).concat(functionMap[6]);
  }
}
