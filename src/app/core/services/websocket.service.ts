import { Injectable, EventEmitter } from '@angular/core';
import * as SockJS from 'sockjs-client';
import { environment } from '../../../environments/environment';

interface Config {
  host: string;
  headers?: object;
  heartbeatIn?: number;
  heartbeatOut?: number;
  debug?: boolean;
  recTimeout?: number;
  queue?: any;
  queueCheckTime?: number;
}

@Injectable({
  providedIn: 'root'
})
export class WebsocketService {
  config: Config = {
    host: '/api/chat'
  };
  isSuccess: boolean;
  socket: any;
  status = 'CLOSED';
  listener: EventEmitter<any> = new EventEmitter();
  initiativeDisconnect = false; // 是否是前端主动断开连接
  MAX_COUNT_RECONNECT = 5; // 每次重试最大连接数
  connectCount: number;
  timer = 1000;
  reConnectTimerHandle: any;
  heatcheckTimer = 1200000; // 120s后重连
  heatcheckTimerHandle: any;
  constructor() {
    this.connectCount = this.MAX_COUNT_RECONNECT;
  }

  // 开始连接websocket
  startConnect() {
    this.socket = new SockJS(this.config.host);
    this.status = 'CONNECTING';
    this.socket.onmessage = (e: any) => {
      if (e.data !== 'HeartBeat') {
        this.connectCount = this.MAX_COUNT_RECONNECT;
        this.status = 'SUCCESS';
        const data = typeof e.data === 'string' ? JSON.parse(e.data) : e.data;
        // 连接成功，服务端推送
        if (data.state === 0) {
          this.listener.emit(data);
        }
        if (data.state !== 0) {
          console.error('websocket api error:' + e);
          return;
        }
      }
    };

    this.socket.onclose = (e: any) => {
      this.status = 'CLOSED';
      this.heatCheckReset();
    };
    this.socket.onerror = (e: any) => {
      this.heatCheckReset();
    };

    this.socket.addEventListener('heartbeat', () => {
      if (environment.production) {
        this.send('HeartBeat');
      }
    });
  }

  // 断开websocket连接
  disconnect() {
    // this.initiativeDisconnect = true;
    if (this.socket != null) {
      this.socket.close();
      this.socket = null;
    }
  }
  // 重新连接websocket
  reConnect() {
    this.disconnect();
    this.reConnectTimerHandle = setTimeout(() => {
      this.connectCount--;
      this.startConnect();
      clearTimeout(this.reConnectTimerHandle);
    }, this.timer);
  }

  // 发送数据到服务端
  send(param: any) {
    this.socket.send(param);
  }

  heatCheck() {
    if (this.initiativeDisconnect || !environment.production) {
      return;
    } // 前端主动断开或者在开发环境下，不启动心跳机制
    if (this.connectCount >= 0) {
      this.reConnect();
    } else {
      this.disconnect();
      this.connectCount = this.MAX_COUNT_RECONNECT;
      this.heatcheckTimerHandle = setTimeout(() => {
        this.reConnect();
      }, this.heatcheckTimer);
    }
  }

  heatCheckReset() {
    clearTimeout(this.heatcheckTimerHandle);
    this.heatCheck();
  }
}

export interface OfflineTaskWsInfo {
  status?: number;
  state?: number;
  user_id?: string;
  type?: number;
  filename?: string;
}
