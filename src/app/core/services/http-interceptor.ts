import { Injectable } from '@angular/core';
import { HttpEvent, HttpInterceptor, HttpHandler, HttpRequest, HttpResponse, HttpErrorResponse } from '@angular/common/http';
import { Observable } from 'rxjs';
import { tap } from 'rxjs/operators';
import { MessageService } from '@haizhi/ng-hertz/message';
import { environment } from '../../../environments/environment';

@Injectable()
export class ApiInterceptor implements HttpInterceptor {
  get redirectLogin(): string {
    return '/login.html';
  }

  constructor(private messageService: MessageService) {}

  intercept(req: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    return next.handle(req).pipe(
      tap(
        success => {
          if (success instanceof HttpResponse) {
            const result = success.body;
            // 未登录
            if (result && result.status === '1') {
              if (environment.production) {
                location.href = this.redirectLogin;
              }
            }
          }
        },
        error => {
          if (error instanceof HttpErrorResponse && error.status === 401) {
            location.href = this.redirectLogin;
          } else if (error instanceof HttpErrorResponse && error.status === 500) {
            this.messageService.error('服务器内部错误');
          } else {
            this.messageService.error(error.statusText);
          }
        }
      )
    );
  }
}
