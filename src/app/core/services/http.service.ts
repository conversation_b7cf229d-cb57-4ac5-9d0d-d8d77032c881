import { Injectable } from '@angular/core';
import { Observable, of, throwError } from 'rxjs';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { MessageService } from '@haizhi/ng-hertz/message';
import { catchError, map, switchMap } from 'rxjs/operators';

export interface ObsRequestResponse<T> {
  status?: string; // 后端返回的status
  result?: T; // 后端返回的 result
  errstr?: string; // status 不为 0 时，返回的错误信息
}

const httpOptions = {
  headers: new HttpHeaders({
    'Content-Type': 'application/x-www-form-urlencoded;charset=utf-8',
    Accept: 'application/json'
  })
};

@Injectable({
  providedIn: 'root'
})
export class HttpService {
  constructor(private http: HttpClient, private messageService: MessageService) {}

  /**
   * get 请求
   * @param url 请求地址
   * @param params 请求参数
   * @param noTip 控制 status 不为 '0' 时，是否需要显示错误信息，noTip 默认 为 false，显示错误信息
   */
  get<T>(url: string, params?: { [param: string]: any }, noTip = false): Observable<T | any> {
    params = { ...params, dmc_request: '1', sys_pro_name: 'dmc' };
    const request$ = this.http.get<ObsRequestResponse<T>>(url, { params, ...httpOptions });
    return this.handleRequest(request$, noTip);
  }

  /**
   * post 请求
   * @param url 请求地址
   * @param params 请求参数
   * @param noTip 控制 status 不为 '0' 时，是否需要显示错误信息，noTip 默认 为 false，显示错误信息
   */
  post<T>(url: string, params?: { [param: string]: any }, noTip = false): Observable<T | any> {
    params = { ...params, dmc_request: '1', sys_pro_name: 'dmc' };
    const formData = this.transformRequest(params);
    const request$ = this.http.post<ObsRequestResponse<T>>(url, formData, { ...httpOptions });
    return this.handleRequest(request$, noTip);
  }

  /**
   * 统一处理请求
   * @param request$ 请求的 Observable
   * @param noTip 控制 status 不为 '0' 时，是否需要显示错误信息，noTip 默认 为 false，显示错误信息
   */
  private handleRequest<T>(request$: Observable<ObsRequestResponse<T>>, noTip: boolean): Observable<T | any> {
    return request$.pipe(
      switchMap(res => {
        const success = res.status === '0';
        if (success) {
          return of(res.result);
        } else {
          // 默认会提示错误
          if (!noTip) {
            this.messageService.error(res.errstr);
          }
          return throwError({ errInfo: res, errType: 0 });
        }
      }),
      catchError(err => throwError({ errInfo: err, errType: 1 }))
    );
    // return new Observable(observer => {
    //   request$.subscribe(
    //     res => {
    //       const success = res.status === '0';
    //       if (success) {
    //         observer.next(res.result);
    //         observer.complete();
    //       } else {
    //         // 默认会提示错误
    //         if (!noTip) {
    //           this.messageService.error(res.errstr);
    //         }
    //         observer.error({ errInfo: res, errType: 0 });
    //         observer.complete();
    //       }
    //     },
    //     error => {
    //       observer.error({ errInfo: error, errType: 1 });
    //       observer.complete();
    //     }
    //   );
    // });
  }

  /**
   * 将对象转换为 Form Data 方式
   * @param params 参数对象
   */
  private transformRequest(params?: { [param: string]: any }): string {
    const afterJsonParam = {};
    Object.keys(params).forEach(key => {
      afterJsonParam[key] = typeof params[key] === 'object' ? JSON.stringify(params[key]) : params[key];
    });
    return Object.keys(afterJsonParam)
      .map(key => {
        return `${key}=${encodeURIComponent(afterJsonParam[key])}`;
      })
      .join('&');
  }
}
