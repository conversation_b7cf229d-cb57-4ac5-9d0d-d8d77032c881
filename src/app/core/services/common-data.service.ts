import { Injectable, EventEmitter } from '@angular/core';
import { MessageService } from '@haizhi/ng-hertz/message';
@Injectable({
  providedIn: 'root'
})
export class CommonDataService {
  headerStatus = {
    hide: false,
    set: this.setHeaderStatus.bind(this),
    get: this.getHeaderStatus.bind(this)
  };
  globalTip = {
    set: (text: string) => {
      this.messageService.info(text);
    },
    setWarning: (text: string) => {
      this.messageService.warning(text);
    } // DMC-5728
  };
  headerStatusChange: EventEmitter<boolean>;
  constructor(private messageService: MessageService) {
    this.headerStatusChange = new EventEmitter();
  }

  setHeaderStatus(show: boolean): void {
    this.headerStatus.hide = !show;
    this.headerStatusChange.emit(!show);
  }
  getHeaderStatus(): boolean {
    return this.headerStatus.hide;
  }
}
