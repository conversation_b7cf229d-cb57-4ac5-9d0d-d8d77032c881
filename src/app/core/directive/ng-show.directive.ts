import { Directive, ElementRef, Input, Renderer2 } from '@angular/core';

@Directive({
  selector: '[ngShow]'
})
export class NgShowDirective {
  constructor(private render: Renderer2, private el: ElementRef) {}
  @Input('ngShow') set ngShow(value: any) {
    if (value) {
      this.render.removeClass(this.el.nativeElement, 'ng-hide');
    } else {
      this.render.addClass(this.el.nativeElement, 'ng-hide');
    }
  }
}
