import { Component, OnInit } from '@angular/core';
import { WebsocketService } from './core/services/websocket.service';

@Component({
  selector: 'hz-app',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.scss']
})
export class AppComponent implements OnInit {
  title = '元数据档案!';

  constructor(private websocket: WebsocketService) {}
  ngOnInit() {
    this.websocket.startConnect();
    const theme = localStorage.getItem('hz-metadata-theme') || 'light';
    document.documentElement.setAttribute('theme', theme);
  }
}
