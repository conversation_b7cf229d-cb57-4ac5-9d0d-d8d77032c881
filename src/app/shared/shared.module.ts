import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { IconModule } from '@haizhi/ng-hertz/icon';
import { SelectModule } from '@haizhi/ng-hertz/select';
import { DatePickerModule } from '@haizhi/ng-hertz/date-picker';
import { TooltipModule } from '@haizhi/ng-hertz/tooltip';
import { PopoverModule } from '@haizhi/ng-hertz/popover';
import { TagModule } from '@haizhi/ng-hertz/tag';
import { EmptyModule } from '@haizhi/ng-hertz/empty';
import { PaginationModule } from '@haizhi/ng-hertz/pagination';
import { TabsModule } from '@haizhi/ng-hertz/tabs';
import { ButtonModule } from '@haizhi/ng-hertz/button';
import { InputModule } from '@haizhi/ng-hertz/input';
import { MessageModule } from '@haizhi/ng-hertz/message';
import { LoadingGifModule } from './loading-gif/loading-gif.module';
import { FilterListPipe } from '../core/pipes/filter-list.pipe';
import { RadioModule } from '@haizhi/ng-hertz/radio';
import { MenuListModule } from '@haizhi/ng-hertz/menu-list';
import { CheckboxModule } from '@haizhi/ng-hertz/checkbox';
import { TransformDataPipe } from '../core/pipes/transform-data.pipe';
import { TransformTimePipe } from '../core/pipes/transform-time.pipe';
import { NgShowDirective } from '../core/directive/ng-show.directive';
@NgModule({
  declarations: [TransformDataPipe, TransformTimePipe, FilterListPipe, NgShowDirective],
  imports: [
    CommonModule,
    IconModule,
    SelectModule,
    DatePickerModule,
    IconModule,
    TooltipModule,
    PopoverModule,
    TagModule,
    EmptyModule,
    PaginationModule,
    TabsModule,
    ButtonModule,
    InputModule,
    MessageModule,
    LoadingGifModule,
    RadioModule,
    MenuListModule,
    CheckboxModule
  ],
  exports: [
    CommonModule,
    IconModule,
    SelectModule,
    DatePickerModule,
    IconModule,
    TooltipModule,
    PopoverModule,
    TagModule,
    EmptyModule,
    PaginationModule,
    TabsModule,
    ButtonModule,
    InputModule,
    MessageModule,
    TransformDataPipe,
    TransformTimePipe,
    LoadingGifModule,
    FilterListPipe,
    RadioModule,
    MenuListModule,
    CheckboxModule,
    NgShowDirective
  ]
})
export class SharedModule {}
