import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TbDetailsComponent } from './tb-details.component';
import { TbBaseInfoComponent } from './tb-base-info/tb-base-info.component';
import { RouterModule } from '@angular/router';
import { TbInfoTabsComponent } from './tb-info-tabs/tb-info-tabs.component';
import { TabsModule } from '@haizhi/ng-hertz/tabs';
import { CommonDialogModule } from '../../ui/common-dialog/common-dialog.module';
import { TbDataPreviewComponent } from './tb-info-tabs/components/tb-data-preview/tb-data-preview.component';
import { TbStructureComponent } from './tb-info-tabs/components/tb-structure/tb-structure.component';
import { TbBloodInfoComponent } from './tb-info-tabs/components/tb-blood-info/tb-blood-info.component';
import { TbLogComponent } from './tb-info-tabs/components/tb-log/tb-log.component';
import { SubregionInfo } from './subregion-info/subregion-info.component';
import { DataLineChartComponent } from './subregion-info/data-line-chart/data-line-chart.component';
import { SubregionListComponent } from './subregion-info/subregion-list/subregion-list.component';
import { TbAdvancedComponent } from './tb-info-tabs/components/tb-advanced/tb-advanced.component';
import { EtlTransformTbTypeComponent } from './tb-info-tabs/components/tb-advanced/etl-transform-tb-type/etl-transform-tb-type.component';
import { EtlTransformTbTypeDialogComponent } from './tb-info-tabs/components/tb-advanced/etl-transform-tb-type/transform-tb-type-dialog/etl-transform-tb-type-dialog.component';
import { TbFileManageComponent } from './tb-info-tabs/components/tb-file-manage/tb-file-manage.component';
import { TbConfigOverviewComponent } from './tb-info-tabs/components/tb-config-overview/tb-config-overview.component';
import { LogChangesComponent } from './tb-info-tabs/components/tb-log/log-changes/log-changes.component';
import { LogUpdateComponent } from './tb-info-tabs/components/tb-log/log-update/log-update.component';
import { LogUseComponent } from './tb-info-tabs/components/tb-log/log-use/log-use.component';
import { TagConfigOverviewComponent } from './tb-info-tabs/components/tb-config-overview/tag-config-overview/tag-config-overview.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { FormsModule } from '@angular/forms';
import { SharedModule } from '../../shared/shared.module';
import { MatDialogModule } from '@angular/material/dialog';
import { RelationConfigOverviewComponent } from './tb-info-tabs/components/tb-config-overview/relation-config-overview/relation-config-overview.component';
import { NotificationModule } from '@haizhi/ng-hertz/notification';
import { BatchUploadDialogComponent } from './tb-info-tabs/components/tb-file-manage/batch-upload-dialog/batch-upload.component';
import { FileUploadModule } from 'ng2-file-upload';
import { BdpOfflineTaskModule } from '../../ui/bdp-offline-task/bdp-offline-task.module';
import { BdpPaginationModule } from './../../ui/bdp-pager/bdp-pagination.module';
import { VersionRecordComponent } from './tb-info-tabs/components/version-record/version-record.component';
import { TbPreviewDialogComponent } from './tb-info-tabs/components/tb-preview-dialog/tb-preview-dialog.component';
import { SwitchModule } from '@haizhi/ng-hertz/switch';
import { BreadcrumbModule } from '@haizhi/ng-hertz/breadcrumb';
import { TbElementInfoComponent } from './tb-info-tabs/components/tb-element-info/tb-element-info.component';
const tabsComponent = [
  TbStructureComponent,
  TbDataPreviewComponent,
  TbBloodInfoComponent,
  TbLogComponent,
  SubregionInfo,
  DataLineChartComponent,
  SubregionListComponent,
  TbAdvancedComponent,
  TbFileManageComponent,
  TbConfigOverviewComponent,
  LogChangesComponent,
  LogUpdateComponent,
  LogUseComponent,
  TagConfigOverviewComponent,
  RelationConfigOverviewComponent,
  TbConfigOverviewComponent,
  EtlTransformTbTypeComponent,
  EtlTransformTbTypeDialogComponent,
  BatchUploadDialogComponent,
  VersionRecordComponent,
  TbPreviewDialogComponent,
  TbElementInfoComponent
];
@NgModule({
  declarations: [TbDetailsComponent, TbBaseInfoComponent, TbInfoTabsComponent, ...tabsComponent],
  imports: [
    CommonModule,
    BreadcrumbModule,
    RouterModule.forChild([
      {
        path: '',
        component: TbDetailsComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'data-preview'
          },
          {
            path: 'data-preview',
            loadChildren: () => import('./tb-data-preview/tb-data-preview.module').then(mod => mod.TbDataPreviewModule)
          },
          {
            path: 'structure',
            loadChildren: () => import('./structure-preview/structure-preview.module').then(mod => mod.StructurePreviewModule)
          },
          // { path: 'blood-info', component: TbBloodInfoComponent },
          {
            path: 'blood-info',
            loadChildren: () => import('./blood-graph/blood-graph.module').then(mod => mod.BloodGraphModule)
          },
          { path: 'log', component: TbLogComponent },
          { path: 'subregion', component: SubregionInfo },
          { path: 'advanced', component: TbAdvancedComponent },
          { path: 'file-manage', component: TbFileManageComponent },
          { path: 'version-record', component: VersionRecordComponent },
          { path: 'config-overview', component: TbConfigOverviewComponent },
          { path: 'element-info', component: TbElementInfoComponent },
          {
            path: 'auth-overview',
            loadChildren: () => import('./associate-auth/associate-auth.module').then(mod => mod.AssociateAuthModule)
          },
          {
            path: 'stream-table-task',
            loadChildren: () => import('./stream-table-task/stream-table-task.module').then(mod => mod.StreamTableTaskModule)
          }
        ]
      }
    ]),
    TabsModule,
    NzTableModule,
    CommonDialogModule,
    FormsModule,
    SharedModule,
    MatDialogModule,
    NotificationModule,
    FileUploadModule,
    BdpOfflineTaskModule,
    BdpPaginationModule,
    SwitchModule
  ],
  entryComponents: [EtlTransformTbTypeDialogComponent, BatchUploadDialogComponent, TbPreviewDialogComponent]
})
export class TbDetailsModule {}
