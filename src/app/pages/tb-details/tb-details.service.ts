import { from, Observable } from 'rxjs';
import { Injectable } from '@angular/core';
import { HttpService } from '../../core/services/http.service';
import {
  TableBaseInfo,
  TableSnapshot,
  ExcelFileResultConfig,
  APITbEditableSchemaResponse,
  APITbModifyParams,
  TableBaseInfoType,
  ElementMapListRes
} from './tb-details.model';
import { Requestparams, UpdateResult, ChangeResult, UseResult } from './tb-info-tabs/components/tb-log/tb-log.model';
import { TagConfig } from './tb-info-tabs/components/tb-config-overview/tb-config-overview.model';
import { Subject } from 'rxjs';

@Injectable({
  providedIn: 'root'
})
export class TbDetailsService {
  curTbId: string;
  curTbType: TableBaseInfoType;
  // 表基本信息
  tbBaseInfo: TableBaseInfo;

  subjectChartResize = new Subject<boolean>();

  constructor(private http: HttpService) {}

  // 元数据接口文档: https://wiki.haizhi.com/pages/viewpage.action?pageId=39390685
  // mock 接口地址: http://mock.dev.haizhi.com/project/82/interface/api

  /**
   * 获取表基本信息
   * @param tbId 表 id
   */
  getTbBaseInfo(tbId: string, type: string): Observable<TableBaseInfo> {
    return this.http.get('/api/tb/base/info', {
      tb_id: tbId,
      type
    });
  }
  /**
   *
   * 关系配置概况
   */
  getRelationRule(relationId: string, relationTypeId: string) {
    return this.http.get('/api/relation/table/relation', {
      relation_id: relationId,
      relation_type_id: relationTypeId
    });
  }

  /**
   *
   * 关系配置概况-聚合关系
   */
  getCombineInfo(relationId: string) {
    return this.http.get('/api/relation/combine/relation/info', { relation_id: relationId });
  }
  /**
   * 标签配置概况
   * @param tbId 表id
   */
  getTagRule(tbId: string): Observable<TagConfig> {
    return this.http.get('/api/tag/tag_result/table/info', {
      tb_id: tbId
    });
  }
  /**
   *  更新记录
   * @param params 请求字段
   */
  getTbLogUpdateInfo(params: Requestparams): Observable<UpdateResult> {
    return this.http.post('/api/tb/update_record', params);
  }
  /**
   * 变更记录
   * @param params 请求字段
   */
  getTbLogChangeInfo(params: Requestparams): Observable<ChangeResult> {
    return this.http.get('/api/tb/update/log/list', params);
  }
  /**
   * 使用记录
   * @param params 请求字段
   */
  getTbLogUseInfo(params: Requestparams): Observable<UseResult> {
    return this.http.get('/api/tb/used/log/list', params);
  }

  /**
   * 获取当前tb表结构
   * @param tbId 表ID
   */
  getTbCurrentVersionData(tbId: string): Observable<APITbEditableSchemaResponse> {
    return this.http.get('/api/tb/editable_schema', {
      tb_id: tbId
    });
  }
  /**
   * 保存编辑完的当前版本
   * @param data 保存的数据
   */
  setTbCurrentVersionData(data: Array<APITbModifyParams>) {
    return this.http.post('/api/tb/modify', {
      data
    });
  }

  /**
   * 获取历史版本列表
   * @param tbId 表ID
   */
  getTbSnapshotList(tbId: string): Observable<{
    list: Array<TableSnapshot>;
    total: number;
    utime: string;
  }> {
    return this.http.get('/api/tb/snapshot/list', {
      tb_id: tbId
    });
  }

  /**
   * 更改版本名
   * @param tbSnapshotId 版本id
   * @param tbSnapshotName 修改后的版本名
   */
  setTbSnapshotName(tbSnapshotId: string, tbSnapshotName: string) {
    return this.http.post('/api/tb/snapshot/modify', {
      tb_snapshot_id: tbSnapshotId,
      snapshot_name: tbSnapshotName
    });
  }

  /**
   * 版本比对
   * @param fromTbSnapshotId 左边版本ID
   * @param toTbSnapshotId 右边版本ID
   */
  getTbVersionCompare(
    fromTbSnapshotId: string,
    toTbSnapshotId: string
  ): Observable<{
    origin_snapshot: TableSnapshot;
    from_snapshot: TableSnapshot;
    to_snapshot: TableSnapshot;
  }> {
    return this.http.get('/api/tb/snapshot/compare', {
      from_tb_snapshot_id: fromTbSnapshotId,
      to_tb_snapshot_id: toTbSnapshotId
    });
  }

  /*
   * 获取原始库Excel文件列表
   * @param tb_id 表id
   */
  getExcelList(params: { tb_id: string }): Observable<ExcelFileResultConfig> {
    return this.http.post('/api/excel/list?time=' + new Date().getTime(), params);
  }

  /**
   * 删除原始库Excel文件
   */
  excelDelete(params: { map_id: string; tb_id: string }) {
    return this.http.post('/api/excel/delete', params);
  }

  // 获取版本记录配置
  getVersionConfigInfo(tb_id: string) {
    return this.http.post('/api/tb/version/config/info', { tb_id: tb_id });
  }

  // 修改版本记录周期等
  modifyVersionConfig(param: { tb_id: string; version_enabled: number; version_record_days?: number }) {
    return this.http.post('/api/tb/version/config/modify', param);
  }

  // 获取列表
  getVersionList(param: { tb_id: string }) {
    return this.http.post('/api/tb/version/list', param);
  }

  // 删除
  deleteVersion(param: { tb_id: string; version: string; tag: string }) {
    return this.http.post('/api/tb/version/delete', param);
  }

  // 导出
  exportVersion(param: any) {
    return this.http.post('/api/export/tb/version', param);
  }

  // 预览
  tbPreivew(param: { tb_id: string; version: string; tag: string }) {
    return this.http.post('/api/tb/version/data/preview', param);
  }
  // 要素信息
  getElementInfo(params: { filter_streaming_tb?: number; tb_ids: string[] }): Observable<ElementMapListRes[]> {
    return this.http.post('/api/element/config/list', params);
  }
}
