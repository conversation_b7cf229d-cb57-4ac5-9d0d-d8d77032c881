import { Component, Input, OnInit } from '@angular/core';
import { TbInfoTabsType } from '../tb-details.model';

@Component({
  selector: 'hz-tb-info-tabs',
  templateUrl: './tb-info-tabs.component.html',
  styleUrls: ['./tb-info-tabs.component.scss']
})
export class TbInfoTabsComponent implements OnInit {
  @Input() tabs: TbInfoTabsItem[];
  constructor() {}

  ngOnInit() {}
}

interface TbInfoTabsItem {
  route: TbInfoTabsType;
  title: string;
  show: boolean;
}
