<article class="tb-info-tabs">
  <hz-tabs [hzTabsRouteMode]="true">
    <ng-container *ngFor="let item of tabs">
      <ng-container *ngIf="item.show">
        <hz-tab [hzTabTitle]="tabTitleTemplate"></hz-tab>
        <ng-template #tabTitleTemplate>
          <a
            [routerLink]="'./' + item.route"
            queryParamsHandling="preserve"
            routerLinkActive="active"
          >
            <span>{{ item.title }}</span>
          </a>
        </ng-template>
      </ng-container>
    </ng-container>
  </hz-tabs>
  <div class="tab-content">
    <router-outlet></router-outlet>
  </div>
</article>
