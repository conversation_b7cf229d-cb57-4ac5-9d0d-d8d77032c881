@import '~node_modules/@haizhi/ui/styles/themes/light.data';
.element-info {
  display: flex;
  flex-direction: column;
  .element {
    box-shadow: $container-c200;
    border-radius: 8px;
    .title {
      padding: 8px 16px;
      font-weight: 600;
      font-size: 14px;
      line-height: 20px;
      color: rgba(21, 22, 24, 0.72);
    }
    .line {
      width: calc(100% - 16px);
      height: 1px;
      background: rgba(15, 34, 67, 0.2);
      opacity: 0.6;
      margin-left: 16px;
    }
    .content {
      padding: 16px 24px;
      display: flex;
      flex-direction: column;
      .content-item {
        display: flex;
        .tag-name {
          width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        &:first-child {
          margin-top: 0;
        }
      }
      .ele-info {
        p {
          height: 20px;
        }
      }
      .icon-reset {
        ::ng-deep i {
          font-size: 135px !important;
        }
      }
    }
  }
}
