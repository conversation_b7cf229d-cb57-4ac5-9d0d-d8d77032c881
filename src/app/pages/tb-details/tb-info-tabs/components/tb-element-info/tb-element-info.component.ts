import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { ElementConfig, ElementMapListRes, EventConfig } from '../../../tb-details.model';
import { TbDetailsService } from '../../../tb-details.service';

@Component({
  selector: 'app-tb-element-info',
  templateUrl: './tb-element-info.component.html',
  styleUrls: ['./tb-element-info.component.scss']
})
export class TbElementInfoComponent implements OnInit {
  tbId = '';
  loading = true;
  dataList: ElementMapListRes[] = [];
  elementConfig: ElementConfig[] = [];
  eventConfig: EventConfig = {
    dep_tb_field: [],
    show_fields: []
  };
  fieldMap: Map<string, string> = new Map();
  constructor(private tbDetailsService: TbDetailsService) {}

  ngOnInit() {
    const params = {
      tb_ids: [this.tbDetailsService.curTbId],
      filter_streaming_tb: 1
    };
    this.tbId = this.tbDetailsService.curTbId;
    this.loading = true;
    this.tbDetailsService
      .getElementInfo(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        if (res.length > 0) {
          this.dataList = res;
          this.elementConfig = this.dataList[0].config;
          this.eventConfig = this.dataList[0].event_config;
          this.dataList[0].fields.forEach(item => {
            this.fieldMap.set(item.field_id, item.title);
          });
        }
      });
  }
}
