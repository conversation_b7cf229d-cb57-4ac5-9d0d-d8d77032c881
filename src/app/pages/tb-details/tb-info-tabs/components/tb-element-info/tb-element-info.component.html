<div class="element-info">
  <div class="element">
    <div class="title">要素信息</div>
    <div class="line"></div>
    <div class="content">
      <ng-container *ngFor="let confItem of elementConfig">
        <div class="content-item mt8" *ngIf="!loading && elementConfig.length > 0">
          <div>
            <hz-tag
              hzType="solid-info"
              hzSize="tiny"
              class="tag-name"
              [hzReadonly]="true"
              [title]="confItem.element_info.title"
            >
              {{ confItem.element_info.title }}
            </hz-tag>
          </div>
          <div class="ele-info ml8">
            <p>
              <span class="name">要素ID：</span>
              <span class="desc">{{ fieldMap.get(confItem.identify_fields[0]) }}</span>
            </p>
            <p>
              <span class="name">要素标识：</span>
              <span *ngFor="let item of confItem.show_fields" class="desc">
                {{ fieldMap.get(item) }}
                <span *ngIf="confItem.show_fields.length > 1">；</span>
              </span>
            </p>
          </div>
        </div>
      </ng-container>
      <ng-container>
        <hz-empty
          *ngIf="!loading && elementConfig.length === 0"
          class="icon-reset"
          hzEmptyIcon="no-result-light"
          hzEmptyTitle="暂无数据"
        ></hz-empty>
      </ng-container>
    </div>
  </div>
  <div class="element mt16">
    <div class="title">事件信息</div>
    <div class="line"></div>
    <div class="content">
      <div class="ele-info" *ngIf="!loading && eventConfig.dep_tb_field.length > 0">
        <p>
          <span class="name">事件ID：</span>
          <span *ngFor="let item of eventConfig.dep_tb_field" class="desc">
            {{ fieldMap.get(item) }}
            <span *ngIf="eventConfig.dep_tb_field.length > 1">；</span>
          </span>
        </p>
        <p>
          <span class="name">事件标识：</span>
          <span *ngFor="let item of eventConfig.show_fields" class="desc">
            {{ fieldMap.get(item) }}
            <span *ngIf="eventConfig.show_fields.length > 1">；</span>
          </span>
        </p>
      </div>
      <ng-container>
        <hz-empty
          *ngIf="!loading && (dataList.length === 0 || eventConfig.dep_tb_field.length === 0)"
          class="icon-reset"
          hzEmptyIcon="no-result-light"
          hzEmptyTitle="暂无数据"
        ></hz-empty>
      </ng-container>
    </div>
  </div>
  <hz-loading-gif *ngIf="loading"></hz-loading-gif>
</div>
