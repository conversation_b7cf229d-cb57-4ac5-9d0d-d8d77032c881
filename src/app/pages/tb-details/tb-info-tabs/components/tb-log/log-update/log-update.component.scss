@import '~@haizhi/ui/styles/themes/light.data';
.update-record {
  width: 100%;
  height: calc(100% - 108px);
  position: relative;
  font-size: 12px;
  margin: 16px 0;
  .tb-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-count {
      line-height: 20px;
      color: $type-700;
      span {
        color: $primary-900;
      }
    }
    .right {
      display: flex;
      .right-content {
        display: flex;
        align-items: center;
        margin-right: 32px;
        span {
          font-weight: 600;
          line-height: 20px;
          color: $type-600;
          margin-right: 16px;
        }
      }
      div:last-child {
        margin-right: 0;
      }
    }
  }
  .tab-container {
    width: 100%;
    padding: 8px 16px;
    background: #ffffff;
    box-shadow: $container-c100;
    border-radius: 8px;
    margin-top: 8px;
    height: calc(100% - 35px);
    .err {
      color: #ff5266;
    }
    i {
      cursor: pointer;
      margin-left: 8px;
    }
    .sort-icon {
      transition: transform 0.4s;
    }
    .sort-asc {
      transform: rotate(-180deg);
    }
  }
}

:host ::ng-deep .hz-range-date-outlet {
  span {
    font-size: 12px;
    line-height: 20px;
    color: $type-800;
  }
}

.page-position {
  display: flex;
  flex-direction: row-reverse;
}
