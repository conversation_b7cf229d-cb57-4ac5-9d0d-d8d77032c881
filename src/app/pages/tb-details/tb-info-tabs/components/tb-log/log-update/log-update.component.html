<div class="update-record">
  <div class="tb-head">
    <div class="left-count">
      共
      <span>{{ totalCount }}</span>
      条
    </div>
    <div class="right">
      <div class="right-content">
        <span>触发类型</span>
        <hz-select
          hzSize="small"
          [(ngModel)]="itemIndex"
          (ngModelChange)="onModelChange($event)"
          [hzSearchable]="true"
          style="width: 120px"
        >
          <hz-option
            *ngFor="let item of selectItems"
            [hzLabel]="item.value"
            [hzValue]="item.id"
          ></hz-option>
        </hz-select>
      </div>
      <div class="right-content">
        <span>时间段</span>
        <div style="width: 360px">
          <hz-date-picker
            [isRange]="true"
            [showTime]="true"
            [(ngModel)]="dateArr"
            (ngModelChange)="timerChange()"
          ></hz-date-picker>
        </div>
      </div>
    </div>
  </div>
  <div class="tab-container" *ngIf="isRequestOver">
    <nz-table
      [nzData]="listInfo"
      [nzShowPagination]="false"
      [nzScroll]="{ x: '1400px', y: 'calc(100vh - 360px)' }"
      *ngIf="totalCount > 0"
    >
      <thead>
        <tr>
          <th nzAlign="left" nzWidth="150px">触发类型</th>
          <th nzAlign="left" nzWidth="300px">触发原因</th>
          <th nzAlign="left" nzWidth="200px">
            更新时间
            <i
              hz-icon
              hzName="desc-sort-arrow"
              hzSize="16px"
              (click)="resultReverse()"
              [ngClass]="{ 'sort-asc': !sortStatus }"
              class="sort-icon"
              hz-action-icon
            ></i>
          </th>
          <th nzAlign="left" nzWidth="200px">触发人</th>
          <th nzAlign="left" nzWidth="150px" *ngIf="!(tbType === 1 && type === 'RESULT')">
            更新状态
          </th>
          <th nzAlign="left" nzWidth="200px" *ngIf="!(tbType === 1 && type === 'RAW')">
            更新数量
            <i
              hz-icon
              hzName="info-sign-bold"
              hzSize="15px"
              hz-tooltip
              [hzTooltipTitle]="'从最后一次启动到现在的更新数据量'"
              [hzTooltipWidth]="'150px'"
              [hzTooltipPlacement]="'top'"
              hz-action-icon
              *ngIf="tbType !== 0"
            ></i>
          </th>
          <th nzAlign="left" nzWidth="200px" *ngIf="!(tbType === 1 && type === 'RAW')">
            耗时
            <i
              hz-icon
              hzName="info-sign-bold"
              hzSize="15px"
              hz-tooltip
              [hzTooltipTitle]="'从最后一次启动到现在的总耗时'"
              [hzTooltipWidth]="'150px'"
              [hzTooltipPlacement]="'top'"
              hz-action-icon
              *ngIf="tbType !== 0"
            ></i>
          </th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of listInfo">
          <td>{{ updateTriggerType.get(item.trigger_type) }}</td>
          <td nzBreakWord="true">{{ item.trigger_reason }}</td>
          <td>{{ item.update_time }}</td>
          <td nzBreakWord="true">{{ item.trigger }}</td>
          <td
            [ngClass]="{ err: item.update_status === '2' }"
            *ngIf="!(tbType === 1 && type === 'RESULT')"
          >
            {{ updateStatusMap[item.update_status] }}
            <ng-container *ngIf="item.update_status === '2'">
              <i
                hz-icon
                hzName="info-sign-bold"
                hzColor="#FF4672"
                hzSize="15px"
                hz-tooltip
                [hzTooltipTitle]="err_msg"
                [hzTooltipType]="'info'"
                [hzTooltipWidth]="'200px'"
                [hzTooltipPlacement]="'top'"
                hz-action-icon
              ></i>
              <ng-template #err_msg>
                <div>
                  <span
                    style="
                      max-width: 170px;
                      display: inline-block;
                      overflow: hidden;
                      white-space: nowrap;
                      text-overflow: ellipsis;
                    "
                  >
                    {{ item.error_msg }}
                  </span>
                  <button hz-button hzType="action" (click)="downloadError(item.task_id)">
                    <i hz-icon hzName="download" hzColor="blue"></i>
                    <span>下载</span>
                  </button>
                </div>
              </ng-template>
            </ng-container>
          </td>
          <td nzBreakWord="true" *ngIf="!(tbType === 1 && type === 'RAW')">
            {{ item.update_count | transformData }}
          </td>
          <td nzBreakWord="true" *ngIf="!(tbType === 1 && type === 'RAW')">
            {{ item.elapse | transformTime }}
          </td>
        </tr>
      </tbody>
    </nz-table>
    <hz-empty
      hzEmptyIcon="no-result-light"
      hzEmptyTitle="当前没有更新记录哦~~"
      *ngIf="isRequestOver && totalCount === 0"
    ></hz-empty>
  </div>
  <hz-loading-gif *ngIf="loading"></hz-loading-gif>
</div>
<div class="page-position" *ngIf="totalCount > 10">
  <hz-pagination
    [hzTotal]="totalCount"
    [(hzPageIndex)]="params.page_no"
    [(hzPageSize)]="params.page_size"
    [hzShowQuickJumper]="true"
    [hzShowPageSizeChanger]="true"
    (hzPageIndexChange)="pageIndexChange()"
    (hzPageSizeChange)="pageSizeChange()"
  ></hz-pagination>
</div>
