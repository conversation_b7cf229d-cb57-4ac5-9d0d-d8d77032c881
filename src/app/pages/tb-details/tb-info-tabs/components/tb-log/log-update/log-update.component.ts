import { finalize, takeUntil } from 'rxjs/operators';
import { Component, OnInit, AfterViewInit, OnDestroy } from '@angular/core';
import { ListInfo, Requestparams, Types } from '../tb-log.model';
import { TbDetailsService } from '../../../../tb-details.service';
import { Subject } from 'rxjs';
import * as moment from 'moment';
@Component({
  selector: 'log-update',
  templateUrl: './log-update.component.html',
  styleUrls: ['./log-update.component.scss']
})
export class LogUpdateComponent implements OnInit, AfterViewInit, OnDestroy {
  listInfo: ListInfo[] = [];
  totalCount = 0; // 对应total_elements总条数
  itemIndex: number = null;
  selectItems: Types[];
  updateTriggerType: Map<number, string> = new Map();
  dateArr: Date[] = [moment(new Date()).subtract(1, 'months').toDate(), new Date()];
  tbType = this.tbDetailsService.tbBaseInfo.tb_type;
  type = this.tbDetailsService.tbBaseInfo.type;
  sortStatus = true; // 排序默认降序
  isRequestOver = false;
  loading = false;
  destroy$ = new Subject<void>();
  params: Requestparams = {
    tb_id: this.tbDetailsService.curTbId,
    page_size: 10,
    page_no: 1,
    start_time: moment(this.dateArr[0]).format('YYYY-MM-DD HH:mm:ss'),
    end_time: moment(this.dateArr[1]).format('YYYY-MM-DD HH:mm:ss'),
    sort_type: 'desc',
    sort_field: 'update_time'
  };
  // 0: '数据表',
  // 1: '流式表'
  // updateStatusMap: { [index: string]: string } = {
  //   0: this.tbType === 1 && this.type === 'RESULT' ? '实时更新' : '更新中', // 流式表：实时更新 数据表：更新中
  //   1: this.tbType === 1 && this.type === 'RESULT' ? '暂停更新' : '更新成功', // 流式表：暂停更新 数据表：更新成功
  //   2: '更新失败',
  //   3: '暂停更新', // 后端新旺同学核对，3 由暂停更新改为更新中 ，后又改回去
  //   4: '暂停更新',
  //   5: '暂停更新',
  //   6: '跳过更新',
  //   7: '跳过更新-临时表跳过',
  //   8: '跳过更新-增量更新，父表数据未变化',
  //   9: '跳过更新-不在执行时间内',
  //   10: '跳过更新-等待父表更新',
  //   11: '跳过更新-暂停更新',
  //   12: '跳过更新-ads最大数据量限制',
  //   13: '等待更新',
  //   14: '等待更新-父表在队列中'
  // };
  updateStatusMap: { [index: string]: string } = {
    0: '更新中', // 流式表：实时更新 数据表：更新中
    1: '更新成功', // 流式表：暂停更新 数据表：更新成功
    2: '更新失败',
    3: '取消更新', // 后端新旺同学核对，3 由暂停更新改为更新中 ，后又改回去
    4: '更新中',
    5: '更新中',
    6: '更新中',
    7: '更新中',
    8: '更新中',
    9: '更新中',
    10: '更新中',
    11: '更新中',
    12: '更新中',
    13: '更新中',
    14: '更新中'
  };
  constructor(private tbDetailsService: TbDetailsService) {}

  /**
   * 选择类型回调
   *
   */
  onModelChange(event) {
    this.params.type = this.itemIndex;
    this.params.page_no = 1;
    this.getTbLogInfo();
  }
  /**
   * 排序回调
   */
  resultReverse() {
    this.sortStatus = !this.sortStatus;
    this.params.page_no = 1;
    this.params.sort_type === 'desc' ? (this.params.sort_type = 'asc') : (this.params.sort_type = 'desc');
    this.getTbLogInfo();
  }
  /**
   *
   * 选择时间回调
   */
  timerChange() {
    this.params.page_no = 1;
    if (this.dateArr.length > 0) {
      this.params.start_time = moment(this.dateArr[0]).format('YYYY-MM-DD HH:mm:ss');
      this.params.end_time = moment(this.dateArr[1]).format('YYYY-MM-DD HH:mm:ss');
    } else {
      this.params.start_time = '';
      this.params.end_time = '';
    }
    this.getTbLogInfo();
  }
  /**
   * 页数变化回调
   */
  pageIndexChange() {
    this.getTbLogInfo();
  }
  /**
   * 页展示数据量变化回调
   */
  pageSizeChange() {
    this.params.page_no = 1;
    this.getTbLogInfo();
  }
  /**
   * 请求数据函数
   */
  getTbLogInfo() {
    this.loading = true;
    this.tbDetailsService
      .getTbLogUpdateInfo(this.params)
      .pipe(
        finalize(() => {
          this.loading = false;
          this.isRequestOver = true;
        }),
        takeUntil(this.destroy$)
      )
      .subscribe(res => {
        this.listInfo = res.data;
        this.totalCount = res.total;
        this.selectItems = res.types;
        if (this.itemIndex === null) {
          this.itemIndex = this.selectItems[0].id;
        }
        for (const item of res.types) {
          this.updateTriggerType.set(item.id, item.value);
        }
      });
  }
  downloadError(taskId: string) {
    const $a = document.createElement('a');
    $a.href = '/api/dataflow/tb_error_message?task_id=' + taskId + '&dmc_request=1&type=1';
    if (typeof MouseEvent === 'function') {
      const evt = new MouseEvent('click', {
        view: window,
        bubbles: true,
        cancelable: false
      });
      $a.dispatchEvent(evt);
    }
  }

  ngOnInit(): void {
    this.getTbLogInfo();
  }
  ngAfterViewInit(): void {}
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
