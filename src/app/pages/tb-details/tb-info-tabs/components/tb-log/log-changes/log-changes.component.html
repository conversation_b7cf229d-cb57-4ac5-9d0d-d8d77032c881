<div class="update-record">
  <div class="tb-head">
    <div class="left-count">
      共
      <span>{{ totalCount }}</span>
      条
    </div>
    <div class="right">
      <div class="right-content">
        <span>变更类型</span>
        <hz-select
          hzSize="small"
          [(ngModel)]="itemIndex"
          (ngModelChange)="onModelChange($event)"
          [hzSearchable]="true"
          style="width: 120px"
        >
          <hz-option
            *ngFor="let item of selectItems"
            [hzLabel]="item.value"
            [hzValue]="item.id"
          ></hz-option>
        </hz-select>
      </div>
      <div class="right-content">
        <span>时间段</span>
        <div style="width: 360px">
          <hz-date-picker
            [isRange]="true"
            [showTime]="true"
            [(ngModel)]="dateArr"
            (ngModelChange)="timerChange()"
          ></hz-date-picker>
        </div>
      </div>
    </div>
  </div>
  <div class="tab-container" *ngIf="isRequestOver">
    <nz-table
      [nzData]="tableInfo"
      [nzShowPagination]="false"
      [nzScroll]="{ x: '1300px', y: 'calc(100vh - 360px)' }"
      *ngIf="totalCount > 0"
    >
      <thead>
        <tr>
          <th nzAlign="left" nzWidth="120px">变更类型</th>
          <th nzAlign="left" nzWidth="250px">
            变更时间
            <i
              hz-icon
              hzName="desc-sort-arrow"
              hzSize="16px"
              (click)="resultReverse()"
              [ngClass]="{ 'sort-asc': !sortStatus }"
              class="sort-icon"
              hz-action-icon
            ></i>
          </th>
          <th nzAlign="left" nzWidth="320px">变更人</th>
          <th nzAlign="left">变更内容</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of tableInfo">
          <td>{{ changeTypesMaps.get(item.op_type) }}</td>
          <td>{{ item.ctime }}</td>
          <td>{{ item.operator_name }}</td>
          <td nzBreakWord="true">
            <ng-container *ngIf="item.tb_update_log_dto.length > 0; else elseTemplate">
              <ng-container *ngFor="let detail of item.tb_update_log_dto; first as first">
                <span class="title" [ngClass]="{ 'title-first': !first }">
                  {{ operatorTypeMap[detail.op_type] }}：
                </span>
                <span class="des">
                  {{ detail.details }}
                </span>
              </ng-container>
            </ng-container>
            <ng-template #elseTemplate>
              <span>---</span>
            </ng-template>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <hz-empty
      hzEmptyIcon="no-result-light"
      hzEmptyTitle="当前没有变更记录哦~~"
      *ngIf="isRequestOver && totalCount === 0"
    ></hz-empty>
  </div>
  <hz-loading-gif *ngIf="loading"></hz-loading-gif>
</div>
<div class="page-position" *ngIf="totalCount > 10">
  <hz-pagination
    [hzTotal]="totalCount"
    [(hzPageIndex)]="params.page_no"
    [(hzPageSize)]="params.page_size"
    [hzShowQuickJumper]="true"
    [hzShowPageSizeChanger]="true"
    (hzPageIndexChange)="pageIndexChange()"
    (hzPageSizeChange)="pageSizeChange()"
  ></hz-pagination>
</div>
