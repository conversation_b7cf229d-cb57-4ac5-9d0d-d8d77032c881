import { finalize, takeUntil } from 'rxjs/operators';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { TbDetailsService } from '../../../../tb-details.service';
import { TableInfo, Requestparams, Types } from '../tb-log.model';
import { Subject } from 'rxjs';
import * as moment from 'moment';
@Component({
  selector: 'log-changes',
  templateUrl: './log-changes.component.html',
  styleUrls: ['./log-changes.component.scss']
})
export class LogChangesComponent implements OnInit, OnDestroy {
  tableInfo: TableInfo[] = []; // 表格数据
  totalCount = 0; // 对应total总条数
  itemIndex: number = null;
  selectItems: Types[] = [];
  changeTypesMaps: Map<number, string> = new Map();
  operatorTypeMap: { [index: number]: string } = {
    1: '修改',
    2: '新增',
    3: '删除'
  };
  dateArr: Date[] = [moment(new Date()).subtract(1, 'months').toDate(), new Date()];
  sortStatus = true; // 排序默认降序
  isRequestOver = false;
  loading = false;
  destroy$ = new Subject<void>();
  params: Requestparams = {
    tb_id: this.tbDetailsService.curTbId,
    page_size: 10,
    page_no: 1,
    start_time: moment(this.dateArr[0]).format('YYYY-MM-DD HH:mm:ss'),
    end_time: moment(this.dateArr[1]).format('YYYY-MM-DD HH:mm:ss'),
    sort_type: 'desc',
    sort_field: 'ctime'
  };

  constructor(private tbDetailsService: TbDetailsService) {}
  /**
   * 类型回调
   */
  onModelChange(event) {
    this.params.type = this.itemIndex;
    this.params.page_no = 1;
    this.getTbLogInfo();
  }
  /**
   * 排序回调
   */
  resultReverse() {
    this.sortStatus = !this.sortStatus;
    this.params.page_no = 1;
    this.params.sort_type === 'desc' ? (this.params.sort_type = 'asc') : (this.params.sort_type = 'desc');
    this.getTbLogInfo();
  }
  /**
   *
   * 时间选择回调
   */
  timerChange() {
    this.params.page_no = 1;
    if (this.dateArr.length > 0) {
      this.params.start_time = moment(this.dateArr[0]).format('YYYY-MM-DD HH:mm:ss');
      this.params.end_time = moment(this.dateArr[1]).format('YYYY-MM-DD HH:mm:ss');
    } else {
      this.params.start_time = '';
      this.params.end_time = '';
    }
    this.getTbLogInfo();
  }
  /**
   * 页数变化回调
   */
  pageIndexChange() {
    this.getTbLogInfo();
  }
  /**
   * 页展示数据量变化回调
   */
  pageSizeChange() {
    this.params.page_no = 1;
    this.getTbLogInfo();
  }
  /**
   * 请求数据函数
   */
  getTbLogInfo() {
    this.loading = true;
    this.tbDetailsService
      .getTbLogChangeInfo(this.params)
      .pipe(
        finalize(() => {
          this.loading = false;
          this.isRequestOver = true;
        }),
        takeUntil(this.destroy$)
      )
      .subscribe(res => {
        this.tableInfo = res.data;
        this.totalCount = res.total;
        this.selectItems = res.types;
        // if (this.tbDetailsService.curTbType === 'RAW') {
        //   // -1 全部类型 0 基础信息 1 表结构 2 更新设置 4 授权信息变更 5 主题映射 6 要素标识 7 关系配置 8 标签配置 9 高级设置
        //   this.selectItems = this.selectItems.filter(item => {
        //     // 原始表只显示全部类型 基础信息 表结构 高级设置
        //     if ([-1, 0, 1, 9].indexOf(item.id) > -1) {
        //       return true;
        //     }
        //   });
        // }
        if (this.itemIndex === null) {
          this.itemIndex = this.selectItems[0].id;
        }
        for (const item of res.types) {
          this.changeTypesMaps.set(item.id, item.value);
        }
      });
  }
  ngOnInit(): void {
    this.params.tb_type = this.tbDetailsService.curTbType;
    this.getTbLogInfo();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
