import { finalize, takeUntil } from 'rxjs/operators';
import { Component, OnInit, OnDestroy } from '@angular/core';
import { TableInfo, Requestparams, Types } from '../tb-log.model';
import { TbDetailsService } from '../../../../tb-details.service';
import { Subject } from 'rxjs';
import * as moment from 'moment';
@Component({
  selector: 'log-use',
  templateUrl: './log-use.component.html',
  styleUrls: ['./log-use.component.scss']
})
export class LogUseComponent implements OnInit, OnDestroy {
  tableInfo: TableInfo[] = [];
  totalCount = 0; // 对应total总条数
  itemIndex: number = null;
  selectItems: Types[] = [];
  useTypeMap: Map<number, string> = new Map();
  dateArr: Date[] = [moment(new Date()).subtract(1, 'months').toDate(), new Date()];
  sortStatus = true; // 排序默认降序
  isRequestOver = false;
  loading = false;
  destroy$ = new Subject<void>();
  params: Requestparams = {
    tb_id: this.tbDetailsService.curTbId,
    page_size: 10,
    page_no: 1,
    start_time: moment(this.dateArr[0]).format('YYYY-MM-DD HH:mm:ss'),
    end_time: moment(this.dateArr[1]).format('YYYY-MM-DD HH:mm:ss'),
    sort_type: 'desc',
    sort_field: 'ctime'
  };
  constructor(private tbDetailsService: TbDetailsService) {}
  /**
   *
   * 类型回调
   */
  onModelChange(event) {
    this.params.type = this.itemIndex;
    this.params.page_no = 1;
    this.getTbLogInfo();
  }
  /**
   * 排序回调
   */
  resultReverse() {
    this.sortStatus = !this.sortStatus;
    this.params.page_no = 1;
    this.params.sort_type === 'desc' ? (this.params.sort_type = 'asc') : (this.params.sort_type = 'desc');
    this.getTbLogInfo();
  }
  /**
   *
   * 选择时间回调
   */
  timerChange() {
    this.params.page_no = 1;
    if (this.dateArr.length > 0) {
      this.params.start_time = moment(this.dateArr[0]).format('YYYY-MM-DD HH:mm:ss');
      this.params.end_time = moment(this.dateArr[1]).format('YYYY-MM-DD HH:mm:ss');
    } else {
      this.params.start_time = '';
      this.params.end_time = '';
    }
    this.getTbLogInfo();
  }
  /**
   * 页数变化回调
   */
  pageIndexChange() {
    this.getTbLogInfo();
  }
  /**
   * 页展示数据量变化回调
   */
  pageSizeChange() {
    this.params.page_no = 1;
    this.getTbLogInfo();
  }
  /**
   * 请求数据函数
   */
  getTbLogInfo() {
    this.loading = true;
    this.tbDetailsService
      .getTbLogUseInfo(this.params)
      .pipe(
        finalize(() => {
          this.loading = false;
          this.isRequestOver = true;
        }),
        takeUntil(this.destroy$)
      )
      .subscribe(res => {
        this.totalCount = res.total;
        this.selectItems = res.types;
        if (this.itemIndex === null) {
          this.itemIndex = this.selectItems[0].id;
        }
        this.tableInfo = res.data;
        for (const item of res.types) {
          this.useTypeMap.set(item.id, item.value);
        }
      });
  }
  ngOnInit(): void {
    this.getTbLogInfo();
  }
  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
