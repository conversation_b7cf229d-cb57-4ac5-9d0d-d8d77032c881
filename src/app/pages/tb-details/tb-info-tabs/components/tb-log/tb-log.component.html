<div>
  <hz-tabs hzTabsType="auxiliary" [(hzCurIndex)]="currentIndex">
    <hz-tab hzTabTitle="更新记录"></hz-tab>
    <hz-tab hzTabTitle="变更记录"></hz-tab>
    <hz-tab
      hzTabTitle="使用记录"
      *ngIf="
        currentTableType !== 'RAW' &&
        currentTableType !== 'ELEMENT' &&
        currentTableType !== 'RESULT'
      "
    ></hz-tab>
  </hz-tabs>
</div>
<log-update *ngIf="currentIndex === 0"></log-update>
<log-changes *ngIf="currentIndex === 1"></log-changes>
<log-use *ngIf="currentIndex === 2"></log-use>
