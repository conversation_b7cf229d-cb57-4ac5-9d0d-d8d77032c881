/**
 * 更新记录接口
 */
export interface UpdateResult {
  page_size: number;
  total: number;
  page_no: number;
  sort_type: string;
  sort_field: string;
  types: Types[];
  data: ListInfo[];
}
export interface ListInfo {
  trigger_type: number;
  trigger_reason: string;
  trigger: string;
  update_time: string;
  update_status: string;
  update_count: number;
  elapse: number;
  task_id: string;
  error_msg: string;
}

/**
 * 变更记录接口
 */
export interface ChangeResult {
  page_size: number;
  total: number;
  page_no: number;
  total_page: number;
  data: TableInfo[];
  types: Types[];
}
export interface TableInfo {
  tb_id: string;
  id: number;
  operator: string;
  operator_name: string;
  operator_id?: any;
  type: number;
  ctime: string;
  details?: any;
  ent_id: string;
  op_type: number;
  tb_update_log_dto: Detail[];
}
export interface Detail {
  op_type: number;
  details: string;
}

/**
 * 使用记录接口
 */
export interface UseResult {
  page_size: number;
  total: number;
  page_no: number;
  total_page: number;
  data: TableInfo[];
  types: Types[];
  fields?: any;
}

export interface UseInfo {
  tb_id?: string;
  id: number;
  operator: string;
  operator_name: string;
  operator_id?: string;
  type: number;
  ctime: string;
  details?: string;
  ent_id: string;
  op_type?: number;
  detail_list?: Detail[];
}
/**
 * 公共接口
 */
export interface Requestparams {
  tb_id: string;
  tb_type?: string;
  type?: number;
  start_time?: string;
  end_time?: string;
  page_no: number;
  page_size: number;
  sort_type: string;
  sort_field: string;
}
export interface Types {
  id: number;
  value: string;
}
