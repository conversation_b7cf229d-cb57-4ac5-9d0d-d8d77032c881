import { Component, OnInit, Inject, AfterViewInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { FileUploader } from 'ng2-file-upload';
import * as _ from 'lodash';
import * as $ from 'jquery';
import { MessageService } from '@haizhi/ng-hertz/message';
const apiUpload = '/api/excel/append_batch';
@Component({
  selector: 'batch-upload',
  templateUrl: './batch-upload.component.html',
  styleUrls: ['./batch-upload.component.scss']
})
export class BatchUploadDialogComponent implements OnInit, AfterViewInit {
  id = '';
  name = '';
  formatHint = false;
  totalSize = 0;
  uploader: any = new FileUploader({
    url: apiUpload,
    queueLimit: 50,
    additionalParameter: {
      folder_id: 'folder_root',
      tb_id: '',
      total_size: 0
    }
  });
  status = 'init';
  isCompleteAll = false;
  tip = '';
  reload = false;
  public hasDropOver = false;
  constructor(
    public dialogRef: MatDialogRef<BatchUploadDialogComponent>,
    private messageService: MessageService,
    @Inject(MAT_DIALOG_DATA) public dialogData: any
  ) {
    this.uploader.onCompleteAll = this.onCompleteAll.bind(this);
    this.uploader.onAfterAddingAll = this.onAfterAddingAll.bind(this);
  }
  ngOnInit() {
    this.id = this.dialogData.tb_id;
    this.name = this.dialogData.tb_name;
    this.uploader.options.additionalParameter.tb_id = this.id;
  }

  ngAfterViewInit() {}

  public fileOverBase(e: any): void {
    this.hasDropOver = e;
  }

  deleteMultipleFiles(index: number) {
    this.uploader.queue.splice(index, 1);
    this.checkFileFormat();
  }

  checkFileFormat() {
    let format = true;
    const list: Array<any> = [];
    let f;
    _.each(this.uploader.queue, (queue, index) => {
      f = /(\..{3,4})?$/.exec(queue.file.name)[0].replace(/\./g, '');
      f = f === 'xls' ? 'xlsx' : f;
      if ($.inArray(f, list) === -1) {
        list.push(f);
      }
    });
    format = list.length > 1 ? false : true;
    this.formatHint = !format; // 是否需要提示暂不支持不同类型文件批量追加
    return format;
  }

  checkFilesSize() {
    const files = this.uploader.queue;
    for (let i = 0, l = files.length; i < l; i++) {
      if (files[i].fileSizeError) {
        return false;
      }
    }
    return true;
  }

  totalFileSize() {
    let size = 0;
    _.each(this.uploader.queue, queue => {
      size += queue.file.size;
    });
    return size;
  }

  async confirm_upload_batch() {
    if (this.uploader.queue.length === 0) {
      this.messageService.warning('请选择要上传的文件');
      return false;
    }
    if (!this.checkFileFormat()) {
      this.messageService.warning('批量追加仅支持相同类型的文件批量操作');
      return false;
    }
    if (!this.checkFilesSize()) {
      return false;
    }
    this.uploader.options.additionalParameter.total_size = this.totalFileSize();
    this.status = 'loading';
    this.uploader.uploadAll();
    _.each(this.uploader.queue, (queue: any) => {
      this.uploadProcess(queue);
    });
  }

  uploadProcess(queue: any) {
    queue.uploadStatus = 'loading';
    const timer = setInterval(() => {
      queue.progress += 10;
      if (queue.progress > 85) {
        queue.progress = 85;
      }
    }, 100);
    queue.onError = () => {
      queue.uploadStatus = 'uploadFail';
      queue.err_mesg = '上传失败';
      queue.progress = 0;
    };
    queue.onSuccess = (response: any, status: number) => {
      queue.uploadStatus = 'uploadComplete';
      if (status === 200) {
        const res = JSON.parse(response);
        switch (res.status) {
          case '20':
            queue.uploadStatus = 'formatError';
            break;
          case '14':
            queue.uploadStatus = 'analyzeFail';
            break;
          case '0':
            queue.uploadStatus = 'analyzeComplete';
            const row = res.result.info[0].row;
            res.errstr = '成功追加' + row + '条数据，上传的文件数据共' + row + '条,\n（如果存在完全一致的数据记录，会进行去重）';
            break;
          default:
            queue.uploadStatus = 'analyzeFail';
            break;
        }
        queue.err_mesg = res.errstr;
        queue.progress = 100;
        if (timer) {
          clearInterval(timer);
        }
      }
    };
  }

  onCompleteAll() {
    this.isCompleteAll = true;
  }

  fixProgress(progress: number) {
    return Math.min(progress, 100);
  }

  // 显示解析错误提示
  showTips = function (event: any, content: string) {
    const { left, top } = event.target.getBoundingClientRect();
    const tip = document.getElementById('upload-error-tip');
    setTimeout(() => {
      tip.style.display = 'block';
      const width = tip.clientWidth;
      tip.style.top = top - 8 - tip.clientHeight + 'px';
      tip.style.left = left + 'px';
    }, 0);
    this.tip = content;
  };

  // 隐藏解析错误提示
  hideTips = () => {
    const tip = document.getElementById('upload-error-tip');
    tip.style.display = 'none';
  };

  complete_batch_append() {
    if (!this.isCompleteAll) {
      return false;
    }
    this.closeDialog(true);
  }

  closeDialog(data?: any) {
    this.dialogRef.close(data);
  }

  onCancelClick(reload: boolean) {
    if (this.status === 'init') {
      this.closeDialog(reload);
    } else {
      this.complete_batch_append();
    }
  }

  onAfterAddingAll(fileItems: any) {
    this.checkSameName(fileItems);
    if (!this.checkFileFormat()) {
      this.messageService.warning('批量追加仅支持相同类型的文件批量操作');
      return;
    }
  }

  // 判断重名文件并去重
  checkSameName(fileItems: any) {
    const queueLen = this.uploader.queue.length;
    const fileLen = fileItems.length;
    const queues = _.cloneDeep(this.uploader.queue);
    queues.splice(queueLen - fileLen, fileLen);
    _.each(fileItems, (val, index: number) => {
      const commonQueue = _.filter(queues, queue => {
        return queue.file.name === val.file.name;
      });
      if (commonQueue.length > 0) {
        this.messageService.warning('上传的文件中存在重名文件,默认保留第一个文件');
        this.uploader.queue.splice(queueLen - fileLen + index, 1);
      }
    });
  }

  reload_append() {
    if (!this.isCompleteAll) {
      return false;
    }
    this.uploader.clearQueue();
    this.status = 'init';
    this.reload = true;
  }
}
