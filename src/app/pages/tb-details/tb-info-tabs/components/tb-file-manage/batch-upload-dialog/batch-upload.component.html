<div>
  <div class="ngdialog-title">
    <span>批量追加</span>
    <i
      hz-icon
      hzName="close"
      hz-action-icon
      (click)="onCancelClick(reload)"
      hzSize="16px"
      class="fr mr24 mt8"
    ></i>
  </div>
  <div class="ngdialog-message" style="padding: 0">
    <div id="upload-init">
      <div id="select-db">
        <p class="append-data-tips">
          <span>追加数据到</span>
          ：
          <span class="tb-name nowrap" title="{{ name }}">{{ name }}</span>
        </p>
        <div class="upload-wrap" *ngIf="status == 'init'">
          <div class="upload-table">
            <div
              class="J-upload-input"
              ng2FileDrop
              [ngClass]="{ acceptClass: hasDropOver }"
              [uploader]="uploader"
              (fileOver)="fileOverBase($event)"
            >
              <div class="upload-item">
                <div class="select-file-wrap">
                  <i hz-icon hzName="upload-1" class="upload-icon" hzSize="32px"></i>
                </div>
              </div>
              <input
                class="file-input"
                title=""
                type="file"
                ng2FileSelect
                [uploader]="uploader"
                [multiple]="true"
                accept="text/csv,*.xls,*.xlsx,.csv,application/vnd.ms-excel,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
              />
              <p class="tips-title mt8">点击或拖拽上传</p>
              <p class="unimportant-tips mt4">最多支持上传50个文件</p>
            </div>
          </div>
          <ul class="upload-warning-part mt16" *ngIf="uploader.queue.length <= 0">
            <li>批量追加默认解析每个文件的第一个sheet，如有多个，请拆分后上传</li>
            <li>上传的文件数据列数与字段必须和当前数据完全匹配</li>
            <li>上传的excel数据默认使用第一个数据表数据进行追加</li>
            <li>上传的csv数据默认使用你刚才统一设置的分隔符</li>
          </ul>
        </div>
        <div class="display-multiple-files" *ngIf="uploader.queue.length > 0 && status == 'init'">
          <ul>
            <li *ngFor="let item of uploader.queue; let i = index">
              <span class="nowrap multiple-files-name">
                <i hz-icon hzName="attachment" hzSize="16px" class="mr8"></i>
                {{ item.file.name }}
                <span *ngIf="item.fileSizeError" class="cr-hint ml8">
                  ({{ item.file.fileSizeError }})
                </span>
              </span>
              <span class="multiple-files-operator" (click)="deleteMultipleFiles(i)">
                <i hz-icon hzName="trash"></i>
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>
    <!-- analyzeFail:文件中的字段不匹配 -->
    <div
      id="upload-loading"
      class="upload-loading-batch"
      *ngIf="status === 'loading' || status === 'success'"
    >
      <div class="batch-append-progress-part">
        <ul class="files-progress-ul" *ngIf="uploader.queue.length != 0">
          <li *ngFor="let item of uploader.queue; let index = index" class="mb4">
            <div class="append-progress-message">
              <span class="nowrap multiple-files-name">{{ item.file.name }}</span>
              <span
                class="progress-result-tip uploading"
                *ngIf="['init', 'loading', 'uploadComplete'].indexOf(item.uploadStatus) > -1"
              >
                <i hz-icon hzName="loading" hzSize="16px" class="loading"></i>
              </span>
              <span
                class="progress-result-tip uploadFail"
                *ngIf="item.uploadStatus == 'uploadFail'"
              >
                <i
                  hz-icon
                  hzName="warning"
                  hzSize="16px"
                  class="warning"
                  (mouseover)="showTips($event, item.err_mesg)"
                  (mouseleave)="hideTips()"
                ></i>
              </span>
              <span
                class="progress-result-tip analyzeComplete"
                *ngIf="item.uploadStatus == 'analyzeComplete'"
              >
                <i
                  hz-icon
                  hzName="attachment"
                  hzSize="16px"
                  (mouseover)="showTips($event, item.err_mesg)"
                  (mouseleave)="hideTips()"
                ></i>
              </span>
              <span
                class="progress-result-tip analyzeFail"
                *ngIf="item.uploadStatus == 'analyzeFail'"
              >
                <i
                  hz-icon
                  hzName="warning"
                  hzSize="16px"
                  class="warning"
                  (mouseover)="showTips($event, item.err_mesg)"
                  (mouseleave)="hideTips()"
                ></i>
              </span>
              <span
                class="progress-result-tip formatError"
                *ngIf="item.uploadStatus == 'formatError'"
              >
                <i
                  hz-icon
                  hzName="warning"
                  hzSize="16px"
                  class="warning"
                  (mouseover)="showTips($event, item.err_mesg)"
                  (mouseleave)="hideTips()"
                ></i>
              </span>
            </div>
            <div class="append-progress-bar">
              <div
                class="append-progress-bar-top"
                [ngStyle]="{ width: fixProgress(item.progress) + '%' }"
                [ngClass]="{
                  transition: fixProgress(item.progress) < 85,
                  uploadFail: item.uploadStatus == 'uploadFail',
                  analyzeFail: item.uploadStatus == 'analyzeFail',
                  formatError: item.uploadStatus == 'formatError'
                }"
              ></div>
            </div>
          </li>
        </ul>
      </div>
      <div class="upload-error-tip" id="upload-error-tip">{{ tip }}</div>
    </div>
  </div>
  <div class="ngdialog-buttons" *ngIf="status === 'init'">
    <button type="button" class="hz-btn color-blue size-l" (click)="confirm_upload_batch()">
      上传
    </button>
    <button type="button" class="hz-btn color-blue size-l" (click)="closeDialog(reload)">
      取消
    </button>
  </div>
  <div class="ngdialog-buttons" *ngIf="status !== 'init'">
    <button type="button" class="hz-btn color-blue size-l" (click)="reload_append()">
      继续上传
    </button>
    <button type="button" class="hz-btn color-blue size-l" (click)="complete_batch_append()">
      完成
    </button>
  </div>
</div>
