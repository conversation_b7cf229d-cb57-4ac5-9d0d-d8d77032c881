@import '~@haizhi/ui/styles/themes/light.data';
.ngdialog-title {
  line-height: 32px;
  text-align: left;
  font-size: 14px;
  font-weight: 600;
  padding: 8px 0 16px 24px;
  span {
    color: $type-900;
  }
}

.ico-delete {
  float: right;
  margin-right: 24px;
  margin-top: 8px;
}

#select-db {
  padding: 0px 32px;
}

.append-data-tips {
  margin: 0;
  padding: 0;
  margin-bottom: 4px;
  line-height: 32px;
  height: 32px;
  .tb-name {
    width: 180px;
    font-weight: 600;
    display: inline-block;
    vertical-align: middle;
    color: $type-900;
  }
}

.select-batch-files-btn {
  cursor: pointer;
  font-size: 12px;
  line-height: 24px;
  text-align: left;
  font-weight: 400;
  color: rgba(81, 130, 228, 1);
}

.upload-warning-part {
  overflow: hidden;
}

.warning-part-left {
  color: rgba(10, 18, 32, 0.46);
  float: left;
  width: 50px;
  padding-top: 5px;
  color: #999;
}

.warning-part-right {
  color: rgba(10, 18, 32, 0.46);
  float: left;
  width: 550px;
}

.upload-warning-p {
  color: rgba(10, 18, 32, 0.46);
  padding-bottom: 0;
  margin-top: 5px;
}

.ngdialog-buttons {
  text-align: right;
  padding: 8px;
  margin-top: 16px;
}

.ngdialog-button {
  height: 32px;
  padding: 0 16px;
  cursor: pointer;
  text-transform: uppercase;
  font-size: 14px;
  margin: 0 0 0 8px;
  border: 0 none;
  font-weight: 700;
  background: transparent;
  color: rgba(81, 130, 228, 1);
}

.upload-wrap {
  position: relative;
  width: 100%;
  .upload-table {
    display: table;
    width: 100%;
    .J-upload-input {
      height: 200px;
      margin-bottom: 16px;
      border-radius: 4px;
      border: 2px dashed $mono-400;
      background: $mono-200;
      vertical-align: middle;
      text-align: center;
      display: table-cell;
      transition: heigit ease-in-out 5s;
    }
  }
  .upload-item {
    vertical-align: middle;
  }
  .select-file-wrap {
    position: relative;
    cursor: pointer;
    text-decoration: none;
    i {
      color: $chart-primary-blue;
    }
  }
  .file-input {
    position: absolute;
    cursor: pointer;
    left: 0;
    top: 0;
    width: 100%;
    padding-left: 100%;
    height: 100%;
    z-index: 10;
    opacity: 0;
  }
  .tips-title {
    color: $type-800;
    line-height: 20px;
    font-size: 14px;
  }
  .unimportant-tips {
    color: $type-700;
    line-height: 20px;
  }
  .upload-warning-part {
    list-style: inside;
    color: $type-800;
    line-height: 20px;
  }
}

.display-multiple-files {
  background-color: $mono-100;
  border-radius: 5px;
  position: relative;
  max-height: 150px;
  margin-top: 16px;
  overflow-x: hidden;
  overflow-y: auto;
  width: 100%;
  ul {
    li {
      list-style: none;
      line-height: 32px;
      height: 32px;
      overflow: hidden;
      color: $type-800;
      margin-bottom: 4px;
      .multiple-files-name {
        display: block;
        float: left;
      }
      .multiple-files-operator {
        display: block;
        float: right;
        margin-right: 0;
        cursor: pointer;
      }
    }
  }
}

.batch-append-progress-part {
  padding: 0 32px;
  ul {
    max-height: 240px;
    overflow-x: hidden;
    overflow-y: auto;
    margin-bottom: 32px;
    li {
      list-style: none;
      height: 33px;
      overflow: hidden;
      line-height: 32px;
      position: relative;
      .multiple-files-name {
        width: 450px;
        display: inline-block;
        font-size: 12px;
        line-height: 32px;
        color: $type-800;
      }
      .progress-result-tip {
        float: left;
        margin-right: 8px;
        display: inline-block;
        .warning {
          color: $chart-primary-error;
        }
      }
    }
  }
}

.append-progress-bar {
  width: 100%;
  height: 1px;
  background: $mono-400;
  position: absolute;
  left: 24px;
  bottom: 0;
  .append-progress-bar-top {
    width: 0;
    height: 1px;
    background: $primary-900;
    position: absolute;
    left: 0;
    bottom: 0;
    &.uploadComplete,
    &.analyzeComplete {
      width: 100%;
    }
    &.uploadFail {
      width: 100%;
      background-color: $chart-primary-error;
    }

    &.analyzeFail {
      width: 100%;
      background-color: $chart-primary-error;
    }

    &.formatError {
      background-color: $chart-primary-error;
      width: 0 !important;
    }
  }
}

.upload-error-tip {
  background: $mono-100;
  position: absolute;
  z-index: 10;
  line-height: 20px;
  top: 0;
  padding: 16px;
  width: auto;
  word-break: break-all;
  display: none;
  box-shadow: $container-soft-02;
  white-space: pre-line;
  border-radius: 4px;
  color: $type-800;
}

.cr {
  color: #e45151 !important;
}

.loading {
  animation: spin 1.2s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }

  100% {
    transform: rotate(360deg);
  }
}
