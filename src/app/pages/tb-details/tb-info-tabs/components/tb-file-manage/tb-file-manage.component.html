<div class="file-manage">
  <div class="title">
    <span>文件列表</span>
    <div [hidden]="tbType === 'STANDARD'">
      <button hz-button hzType="primary" class="add-data-btn" (click)="changeFileAll('append')">
        追加数据
      </button>
      <button hz-button hzType="primary" class="add-data-btn" (click)="batchAppend()">
        批量追加
      </button>
      <button hz-button hzType="primary" (click)="changeFileAll('replace')">全部替换</button>
    </div>
  </div>
  <div class="tab-update-record">
    <div class="update-record-wrap">
      <div class="update-record" *ngIf="updateRecord.op_log.content">
        <p class="text-hint">更新记录</p>
        <div class="update-table bdp-table-a">
          <table class="hz-table">
            <colgroup>
              <col width="400" />
              <col />
            </colgroup>
            <thead>
              <tr>
                <th>更新时间</th>
                <th>更新日志</th>
              </tr>
            </thead>
            <tbody>
              <tr *ngFor="let record of updateRecord.op_log.content">
                <td>{{ record.ctime }}</td>
                <td class="font-style" *ngIf="record.opt != 5">
                  <span class="normal">{{ operateMap[record.opt] }}</span>
                  &nbsp;
                  <span class="font-style">{{ record.excel_name }} - {{ record.sheet_name }}</span>
                </td>
                <td class="font-style" *ngIf="record.opt == 5">
                  <span class="font-style">{{ record.excel_name }} - {{ record.sheet_name }}</span>
                  &nbsp;
                  <span class="normal">{{ operateMap[record.opt] }}</span>
                  <span class="spanle">了&nbsp;</span>
                  <span class="font-style">{{ record.params.detail }}</span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <bdp-pagination
            [totalItems]="recordPage.total"
            [(ngModel)]="recordPage.currentPage"
            [maxSize]="maxSize"
            [itemsPerPage]="perPage"
            class="pagination-lg"
            [boundaryLinks]="true"
            [rotate]="false"
            previousText="上一页"
            nextText="下一页"
            firstText="首页"
            lastText="尾页"
            (pageChanged)="recordPageChanged($event)"
          ></bdp-pagination>
        </div>
      </div>
      <div class="file-list" *ngIf="updateRecord.file_list.content">
        <p class="text-hint">新文件列表</p>
        <div class="bdp-table-a">
          <table class="hz-table">
            <colgroup>
              <col width="400" />
              <col />
              <col width="300" />
            </colgroup>
            <thead>
              <tr>
                <th>更新时间</th>
                <th>文件名称</th>
                <th>&nbsp;</th>
              </tr>
            </thead>
            <tbody>
              <tr class="tr-inline" *ngFor="let file of updateRecord.file_list.content">
                <td>{{ file.utime }}</td>
                <td>{{ file.excel_name }}-{{ file.sheet_name }}</td>
                <td class="opt-btn">
                  <a
                    class="mr16"
                    (click)="toUploadFile(file.tb_id, file.map_id)"
                    *ngIf="tbType !== 'STANDARD'"
                  >
                    <button hz-button hzSize="m" hzType="action">
                      <i hz-icon hzName="tpl-replace" class="mr4"></i>
                      <span>替换</span>
                    </button>
                  </a>
                  <a
                    class="mr16"
                    *ngIf="file.is_download == 1"
                    href="/api/excel/download?excel_id={{ file.excel_id }}&tb_id={{
                      file.tb_id
                    }}&sys_pro_name=etl"
                  >
                    <button hz-button hzSize="m" hzType="action">
                      <i hz-icon hzName="download" class="mr4"></i>
                      <span>下载</span>
                    </button>
                  </a>
                  <a
                    [hidden]="
                      (updateRecord.file_list.content.length == 1 && filePage.currentPage == 1) ||
                      tbType === 'STANDARD'
                    "
                    (click)="delete(file.tb_id, file.map_id)"
                  >
                    <button hz-button hzSize="m" hzType="action">
                      <i hz-icon hzName="trash" class="mr4"></i>
                      <span>删除</span>
                    </button>
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <bdp-pagination
            [totalItems]="filePage.total"
            [(ngModel)]="filePage.currentPage"
            [itemsPerPage]="perPage"
            [maxSize]="maxSize"
            class="pagination-lg"
            [boundaryLinks]="true"
            [rotate]="false"
            previousText="上一页"
            nextText="下一页"
            firstText="首页"
            lastText="尾页"
            (pageChanged)="filePageChanged($event)"
          ></bdp-pagination>
        </div>
      </div>
      <div class="file-list" *ngIf="updateRecord.history_file?.content">
        <p class="text-hint">历史文件列表</p>
        <div class="bdp-table-a">
          <table class="hz-table">
            <colgroup>
              <col width="400" />
              <col />
              <col width="300" />
            </colgroup>
            <thead>
              <tr>
                <th>更新时间</th>
                <th>文件名称</th>
                <th>&nbsp;</th>
              </tr>
            </thead>
            <tbody>
              <tr class="tr-inline" *ngFor="let file of updateRecord.history_file.content">
                <td>{{ file.utime }}</td>
                <td>{{ file.excel_name }}-{{ file.sheet_name }}</td>
                <td class="opt-btn">
                  <a
                    class="mr16"
                    *ngIf="file.is_download == 1"
                    href="/api/excel/download?excel_id={{ file.excel_id }}&tb_id={{
                      file.tb_id
                    }}&sys_pro_name=etl"
                  >
                    <button hz-button hzSize="m" hzType="action">
                      <i hz-icon hzName="download" class="mr4"></i>
                      <span>下载</span>
                    </button>
                  </a>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div class="pagination">
          <bdp-pagination
            [totalItems]="historyPage.total"
            [(ngModel)]="historyPage.currentPage"
            [itemsPerPage]="perPage"
            [maxSize]="maxSize"
            class="pagination-lg"
            [boundaryLinks]="true"
            [rotate]="false"
            previousText="上一页"
            nextText="下一页"
            firstText="首页"
            lastText="尾页"
            (pageChanged)="historyPageChanged($event)"
          ></bdp-pagination>
        </div>
      </div>
    </div>
  </div>
  <hz-loading-gif *ngIf="loading"></hz-loading-gif>
</div>
