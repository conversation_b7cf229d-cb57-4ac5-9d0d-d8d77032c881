@import '~@haizhi/ui/styles/themes/light.data';
.file-manage {
  height: 100%;
  .title {
    line-height: 32px;
    font-weight: 600;
    font-size: 14px;
    color: $type-800;
    margin-bottom: 8px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .add-data-btn {
      margin-right: 8px;
    }
  }
}
.tab-update-record {
  height: calc(100% - 40px);
  .update-record-wrap {
    // height: 100%;
  }
  .text-hint {
    line-height: 32px;
    padding: 0 8px;
    color: $type-700;
    font-weight: bold;
  }
  .bdp-table-a {
    max-height: 382px;
    background-color: #fff;
    box-shadow: $container-soft-01;
    border-radius: 8px;
    tbody tr:hover {
      background-color: rgb(243, 244, 245);
    }
  }
  .normal {
    color: $type-800;
  }
  table {
    width: 100%;
    text-align: left;
    table-layout: fixed;
  }
  .bdp-table-a thead::before {
    content: '';
    display: block;
    height: 8px;
    width: 100%;
  }
  .bdp-table-a tbody::after {
    content: '';
    display: block;
    height: 8px;
    width: 100%;
  }
  .update-record table thead tr th:first-child,
  .file-list table thead tr th:first-child,
  .update-record table thead tr th:last-child,
  .file-list table thead tr th:last-child {
    width: 240px;
  }
  .head {
    line-height: 20px;
    margin-top: 8px;
    margin-bottom: -8px;
    padding: 6px 0 6px 8px;
  }
  .list-worpper {
    .bdp-table-a thead::before {
      content: '';
      display: none;
      height: 8px;
      width: 100%;
    }
  }
  .font-style {
    .normal {
      font-weight: normal;
    }
    .spanle {
      color: $type-800;
    }
    .font-style {
      font-weight: 900;
    }
  }
}

a {
  cursor: pointer;
  color: $type-800;
  // text-decoration: underline;
}
.btn-style {
  line-height: 32px;
  padding-top: 0;
  padding-bottom: 0;
  background: none;
}

.pagination {
  text-align: center;
}
.font-style {
  color: $type-900;
  font-weight: 600;
}
.update-table {
  width: 100%;
  height: 100%;
  padding: 8px 16px;
}
.tr-inline {
  .opt-btn {
    display: none;
    cursor: pointer;
  }
  &:hover {
    .opt-btn {
      display: block;
    }
  }
}
.file-list {
  // [DMC-4677] UI走查 记录在钉钉
  .bdp-table-a {
    padding: 8px 16px;
    .hz-table {
      .tr-inline {
        box-shadow: 0 -1px 0 0 rgba(15, 34, 67, 0.08) inset;
      }
      tbody tr:last-child {
        box-shadow: none;
      }
      tbody td {
        box-shadow: none;
      }
    }
  }
}
