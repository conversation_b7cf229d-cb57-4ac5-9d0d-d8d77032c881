import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { ExcelFileList } from '../../../tb-details.model';
import { TbDetailsService } from '../../../tb-details.service';
import { Router } from '@angular/router';
import { ConfirmDialogComponent } from '../../../../../ui/common-dialog/dialog-confirm.component';
import { MatDialog } from '@angular/material';
import { BatchUploadDialogComponent } from './batch-upload-dialog/batch-upload.component';
import * as _ from 'lodash';
@Component({
  selector: 'tb-file-manage',
  templateUrl: './tb-file-manage.component.html',
  styleUrls: ['./tb-file-manage.component.scss']
})
export class TbFileManageComponent implements OnInit {
  result: { total: number; list: ExcelFileList[] } = {
    total: 0,
    list: []
  };
  loading = false;
  recordPage = {
    currentPage: 1,
    total: 0
  };
  filePage = {
    currentPage: 1,
    total: 0
  };
  historyPage = {
    currentPage: 1,
    total: 0
  };
  perPage: number = 8;
  maxSize: number = 8;
  updateRecord: any = {
    op_log: {},
    file_list: {},
    history_file: {}
  };
  operateMap: object = {
    '1': '新增',
    '2': '追加',
    '3': '替换',
    '4': '删除',
    '5': '替换'
  };
  tbType = '';
  constructor(private tbDetailsService: TbDetailsService, private router: Router, private dialog: MatDialog) {
    this.tbType = this.tbDetailsService.curTbType;
  }

  ngOnInit() {
    this.updateExcel();
  }

  updateExcel(pageParam?: any) {
    this.loading = true;
    let param = {
      tb_id: this.tbDetailsService.curTbId
    };
    if (!!pageParam) {
      param = pageParam.param;
    }

    this.tbDetailsService
      .getExcelList(param)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(data => {
        if (data) {
          if (!pageParam) {
            this.recordPage.total = data.op_log.pages_count * 8;
            this.filePage.total = data.file_list.pages_count * 8;
            if (_.keys(data.history_file).length > 0) {
              this.historyPage.total = data.history_file.pages_count * 8;
            }
            this.updateRecord = data;
          } else {
            this.updateRecord[pageParam.type].content = data[pageParam.type].content;
          }
          this.loading = false;
        }
      });
  }

  // 替换全部文件
  changeFileAll(type: string) {
    const preUrl = encodeURIComponent(`/tb-details${this.router.url}`);
    window.location.href = `/dmc/#/wtb-upload-file?type=${type}&id=${this.tbDetailsService.curTbId}&preUrl=${preUrl}`;
  }

  batchAppend() {
    const dialogRef = this.dialog.open(BatchUploadDialogComponent, {
      width: '640px',
      disableClose: true,
      data: {
        tb_id: this.tbDetailsService.curTbId,
        tb_name: this.tbDetailsService.tbBaseInfo.title
      }
    });
    dialogRef.afterClosed().subscribe(res => {
      if (res) {
        this.updateExcel();
      }
    });
  }

  // 文件下载
  download(excelId = '', tbId = '') {
    const element = document.createElement('a');
    element.setAttribute('href', `/api/excel/download?excel_id=${excelId}&tb_id=${tbId}&dmc_request=1&sys_pro_name=dmc`);
    element.style.display = 'none';
    document.body.appendChild(element);
    element.click();
    setTimeout(() => {
      document.body.removeChild(element);
    }, 20);
  }

  // 文件删除
  delete(tbId = '', mapid = '') {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      disableClose: true,
      width: '300px',
      data: {
        title: '确认',
        content: '你确定要删除吗？'
      }
    });
    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.loading = true;
        const param = {
          map_id: mapid,
          tb_id: tbId
        };
        this.tbDetailsService.excelDelete(param).subscribe(
          data => {
            this.updateExcel();
          },
          err => {
            this.loading = false;
          }
        );
      }
    });
  }

  recordPageChanged(event: any) {
    this.updateExcel({
      type: 'op_log',
      param: {
        page: event.page,
        query_type: 'op_log',
        tb_id: this.tbDetailsService.curTbId
      }
    });
    this.recordPage.currentPage = event.page;
  }

  filePageChanged(event: any) {
    this.updateExcel({
      type: 'file_list',
      param: {
        page: event.page,
        query_type: 'file_list',
        tb_id: this.tbDetailsService.curTbId
      }
    });
    this.filePage.currentPage = event.page;
  }

  historyPageChanged(event: any) {
    this.updateExcel({
      type: 'history_file',
      param: {
        page: event.page,
        query_type: 'history_file',
        tb_id: this.tbDetailsService.curTbId
      }
    });
    this.historyPage.currentPage = event.page;
  }

  toUploadFile(tbId: string, map_id: string) {
    const preUrl = encodeURIComponent(`/tb-details${this.router.url}`);
    window.location.href = `/dmc/#/wtb-upload-file?type=singleReplace&id=${tbId}&mapId=${map_id}&preUrl=${preUrl}`;
  }
}
