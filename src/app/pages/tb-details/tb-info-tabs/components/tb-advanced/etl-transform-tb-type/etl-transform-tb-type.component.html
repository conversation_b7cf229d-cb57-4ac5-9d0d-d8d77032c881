<div class="pannel_normal trans-tbtype">
  <div class="pannel__header header">
    <div class="header__title_normal title-tail-btn">
      <span>数据表类型切换</span>
      (
      <span>当前表数据量</span>
      <span class="active-font ml-4">{{ tb.count }}</span>
      )
      <div class="bdp-tooltip bottom ml5">
        <i
          hz-icon
          hzName="info-sign"
          hz-tooltip
          hzTooltipType="info"
          [hzTooltipTitle]="
            '高性能表适用于1000万条数据量以上的数据表，支持增量合表，可大幅提升多表关联的运行速度，缩短处理时间'
          "
          [hzTooltipWidth]="'200px'"
          hzTooltipPlacement="top"
        ></i>
      </div>
    </div>
    <div class="header__operate_normal" *ngIf="tb.origin_type != 'flow'">
      <button
        hz-button
        hzSize="s"
        *ngIf="!transData.editing"
        (click)="transData.editing = true"
        [disabled]="curTbType === 'RESULT' || curTbType === 'PROCESS' || storage_type === 3"
      >
        编辑
      </button>
      <button
        hz-button
        hzSize="s"
        hzType="outline"
        *ngIf="transData.editing"
        (click)="transData.editing = false; ngOnInit()"
      >
        取消
      </button>
      <button
        hz-button
        hzSize="s"
        *ngIf="transData.editing"
        (click)="checkSelected()"
        class="btn-last"
      >
        保存
      </button>
    </div>
  </div>
  <div class="pannel__body">
    <p class="mt-10">
      你可以对原始数据表设置不同的作用类型，不同类型的数据表在更新合表机制上都各有特点
    </p>
    <p class="mute-font" *ngIf="tb.origin_type == 'adv_view'">
      当前数据表为合表，如需修改，请对原始表进行操作
    </p>
    <div class="check-content">
      <p class="label-tip">请选择切换的数据表类型</p>
      <ul class="option-list">
        <li>
          <div class="option-list-title">
            <label
              hz-radio
              hzValue="0"
              [(ngModel)]="transData.targetType"
              hzName="tb_type"
              [hzDisabled]="!transData.editing"
              class="mr-12"
            ></label>
            <i hz-icon hzName="file-normal" hzColor="rgba(31,113,255,1)"></i>
            普通数据表
          </div>
          <p class="description">适用于1000万条数据量以下的数据表，数据更新频次最高为1小时</p>
        </li>
        <li>
          <div class="option-list-title">
            <label
              hz-radio
              hzValue="1"
              [(ngModel)]="transData.targetType"
              hzName="tb_type"
              [hzDisabled]="!transData.editing"
              class="mr-12"
            ></label>
            <i hz-icon hzName="file-data" hzColor="rgba(31,113,255,1)"></i>
            高性能表
          </div>
          <p class="description">
            适用于1000万条数据量以上的数据表，支持增量合表，可大幅提升多表关联的运行速度，缩短处理时间
          </p>
          <p class="description">
            <span class="highlight">*</span>
            注意：多表关联时只能作为主表，需设置分区字段，数据更新频次最高为1小时
          </p>
        </li>
        <li>
          <div class="option-list-title">
            <label
              hz-radio
              hzValue="2"
              [(ngModel)]="transData.targetType"
              hzName="tb_type"
              [hzDisabled]="!transData.editing"
              class="mr-12"
            ></label>
            <i hz-icon hzName="file-speed" hzColor="rgba(31,113,255,1)" class="mr4"></i>
            高频更新表
          </div>
          <p class="description">适用于需要分钟级更新的实时监控数据表，支持数据的追加和清除操作</p>
          <p class="description">
            <span class="highlight">*</span>
            注意：不支持对数据的修改操作，数据量不超过10万条
          </p>
        </li>
      </ul>
    </div>
  </div>
</div>

<div
  class="pannel_normal partition-set-dialog"
  *ngIf="
    (partitionData.set || (!!tb.permission && tb.permission.enableSetPartition)) &&
    transData.targetType == 1
  "
>
  <div class="pannel__header header">
    <!-- *ngIf="selected.permission.isNotLogType" -->
    <div
      class="bdp-tooltip bottom OOCSS cursor-pointer header__title_normal"
      (mouseover)="partitionData.tipShow = true"
      (mouseout)="partitionData.tipShow = false"
      [ngClass]="{ left: tb.origin_type != 'excel' }"
    >
      <span>设置分区字段</span>

      <i
        class="bdp-icon ml4"
        hz-icon
        hzName="info-sign"
        hz-tooltip
        hzTooltipType="info"
        [hzTooltipTitle]="
          partitionData.set ? '已设置分区字段加速' : '数据表数据过大，建议设置分区字段加速'
        "
        hzTooltipPlacement="top"
      ></i>
    </div>
    <div class="header__operate_normal">
      <button
        hz-button
        hzSize="s"
        (click)="beginSetPartition(); partitionData.editing = true"
        *ngIf="setPartitionView == 'preview'"
      >
        立即设置
      </button>
      <button
        hz-button
        hzSize="s"
        *ngIf="partitionData.set && !partitionData.readonly && !partitionData.editing"
        (click)="delSetPartition()"
      >
        删除分区设置
      </button>
      <button
        hz-button
        hzSize="s"
        (click)="partitionData.editing = true"
        *ngIf="
          setPartitionView == 'edit' &&
          !partitionData.match_error &&
          !partitionData.readonly &&
          !partitionData.editing
        "
        class="btn-last"
      >
        编辑
      </button>
      <button
        hz-button
        hzSize="s"
        (click)="saveSetPartition()"
        *ngIf="
          setPartitionView == 'edit' &&
          !partitionData.match_error &&
          !partitionData.readonly &&
          partitionData.editing &&
          !partitionData.empty_field
        "
      >
        确定
      </button>
      <button
        hz-button
        hzSize="s"
        (click)="preparePartitionData(); partitionData.editing = false"
        *ngIf="setPartitionView == 'edit' && partitionData.editing"
        class="btn-last"
      >
        取消
      </button>
    </div>
  </div>
  <div class="pannel__body box_highlight">
    <div class="pre-set-partition" *ngIf="setPartitionView == 'preview'">
      当前数据表数据过大，建议将某个常用字段设置为分区字段，当分区字段作为图表筛选器或是筛选条件时，可以大幅提升查询效率。如果想要提升合表效率，可以将此表类型切换为高性能表。
    </div>
    <div class="edit-set-partition" *ngIf="setPartitionView == 'edit'">
      <p *ngIf="partitionData.empty_field" class="cr-999">无可用分区字段</p>
      <div *ngIf="!partitionData.empty_field" class="mb16">
        <span class="title">分区字段</span>
        <div class="select-box">
          <hz-select
            [hzSize]="'small'"
            [(ngModel)]="partitionData.field_id"
            (ngModelChange)="changePartitionField($event, partitionData.field_id)"
            [hzDisabled]="partitionData.readonly || !partitionData.editing"
          >
            <hz-option
              *ngFor="let item of partitionData.field_list"
              [hzLabel]="item.name"
              [hzValue]="item.field_id"
            ></hz-option>
          </hz-select>
        </div>
      </div>
      <div *ngIf="!partitionData.empty_field && partitionData.data_type == 'date'" class="mb16">
        <span class="title">分区粒度</span>
        <div class="select-box">
          <hz-select
            [hzSize]="'small'"
            [(ngModel)]="partitionData.granularity"
            [hzDisabled]="partitionData.readonly || !partitionData.editing"
          >
            <hz-option [hzLabel]="'年'" [hzValue]="'year'"></hz-option>
            <hz-option [hzLabel]="'月'" [hzValue]="'month'"></hz-option>
            <hz-option [hzLabel]="'日'" [hzValue]="'day'"></hz-option>
          </hz-select>
        </div>
      </div>
      <div *ngIf="!partitionData.empty_field && partitionData.data_type != 'date'">
        <div class="mb16">
          <span class="title">分区方式</span>
          <div class="select-box">
            <hz-select
              [hzSize]="'small'"
              [(ngModel)]="partitionData.mode"
              (ngModelChange)="changePartitionMode()"
              hzNoMatchPlaceholder="请选择"
              [hzDisabled]="partitionData.readonly || !partitionData.editing"
            >
              <hz-option [hzLabel]="'字段值分区'" hzValue="0"></hz-option>
              <hz-option [hzLabel]="'hash值分区'" hzValue="1"></hz-option>
            </hz-select>
          </div>
        </div>
        <p *ngIf="partitionData.mode === '1'" class="input-box">
          <span class="title">分区数量</span>
          <input
            type="number"
            hz-input
            [(ngModel)]="partitionData.granularity"
            [disabled]="partitionData.readonly || !partitionData.editing"
          />
        </p>
      </div>
      <p *ngIf="partitionData.match_error" class="cr-hint match-error">
        原分区字段已被修改，修改后的字段不支持分区，请重新设置
      </p>
      <p *ngIf="partitionData.readonly" class="cr-hint">此表分区设置不可编辑</p>
    </div>
  </div>
</div>
<hz-loading-gif *ngIf="loading"></hz-loading-gif>
