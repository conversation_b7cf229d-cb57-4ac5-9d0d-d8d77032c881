import { MessageService } from '@haizhi/ng-hertz/message';
import { finalize } from 'rxjs/operators';
import { Component, OnInit, Input } from '@angular/core';
import { CommonDataService } from '../../../../../../core/services/common-data.service';
import { TbAdvancedService } from '../tb-advanced.service';
import { MatDialog } from '@angular/material';
import { EtlTransformTbTypeDialogComponent } from './transform-tb-type-dialog/etl-transform-tb-type-dialog.component';
import { TbDetailsService } from '../../../../tb-details.service';
import * as _ from 'lodash';

@Component({
  selector: 'etl-transform-tb-type',
  templateUrl: './etl-transform-tb-type.component.html',
  styleUrls: ['./etl-transform-tb-type.component.scss']
})
export class EtlTransformTbTypeComponent implements OnInit {
  // @Input() tb: any;
  curTbType = this.tbDetailsService.curTbType;
  tb = {
    tb_id: this.tbDetailsService.curTbId,
    data_count: 0,
    update_mode: 0,
    permission: null,
    type: this.tbDetailsService.curTbType,
    count: 0,
    origin_type: this.tbDetailsService.tbBaseInfo.origin_type
  };
  transData: any;
  initialState: object = {
    currentStep: 'selecting',
    checkResult: '', // 检查结果 文案; 依此判断是否允许提交转换请求
    showLoading: false // 控制loading显示
  };
  UPDATE_MODE = 'update_mode';
  NORMAL_UPDATE_MODE = '0';
  INCREMENT_UPDATE_MODE = '1';
  REALTIME_UPDATE_MODE = '2';
  typesMap = {
    0: ['ico-file-normal', 'General'],
    1: ['ico-file-data', 'Incremental'],
    2: ['ico-file-speed', 'Frequently']
  };
  fieldTypeMap: object = {
    0: 'number',
    1: 'number',
    3: 'date',
    2: 'string'
  };
  partitionAll: any;
  partitionData: any = {
    data_type: '',
    editing: false,
    empty_field: false,
    field_id: undefined,
    field_list: [],
    granularity: '',
    match_error: false,
    mode: '0',
    readonly: undefined,
    set: false,
    tipShow: false,
    vfield_partition: false
  };
  setPartitionView = 'preview';
  partitionDataOriginal: any;
  setPartitionDone = true;
  originSchema: any;
  loading = false;
  storage_type = -1; // 是否为分区表 3为分区表，分区表不能编辑
  constructor(
    private commonData: CommonDataService,
    public dialog: MatDialog,
    private tbAdvancedService: TbAdvancedService,
    private tbDetailsService: TbDetailsService,
    private message: MessageService
  ) {
    this.commonData.setHeaderStatus(false);
  }

  ngOnInit() {
    this.transData = {
      editing: false
    };
    this.initData();
    this.storage_type = this.tbDetailsService.tbBaseInfo.storage_type;
  }

  initData() {
    const previewParams: any = {
      tb_id: this.tb.tb_id
    };
    this.loading = true;
    this.tbAdvancedService.tbs
      .preview(previewParams)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(data => {
        data = data;
        const id = this.tb.tb_id;
        _.forEach(data.fields, val => {
          val.data_type = this.fieldTypeMap[val.type];
        });
        _.forEach(data.schema, field => {
          field.editable = true;
          field.modifyField = true;
          if (field.hasOwnProperty('param') && !!field.param) {
            const param = JSON.parse(field.param);
            if (param && param.type && param.type === 'ds') {
              field.editable = false;
            }
            // general => 通过常用编码解析添加的计算字段，不可编辑
            if (param && param.type && param.type === 'general') {
              field.modifyField = false;
            }
          }
        });
        this.originSchema = data.fields;
        this.tb = data;
        this.tb.tb_id = id;
        if (data.hasOwnProperty('partition')) {
          this.partitionAll = this.tbAdvancedService.updatePartitionData(data.partition, data);
        }
        this.tb.update_mode = data.update_mode;
        this.tb.permission = this.tbAdvancedService.setTbPermission(this.tb, data);
        this.partitionData = this.partitionAll.partitionData;
        this.partitionDataOriginal = this.partitionAll.partitionDataOriginal;
        this.showTransformDialog();
        this.preparePartitionData();
      });
  }

  showTransformDialog() {
    // 重置界面交互、状态
    // $.extend(this.transData, this.initialState);
    this.transData = Object.assign(this.initialState);
    this.resetPartitionData();
    this.transData.targetType = this.transData.currentType = this.tb.update_mode.toString(); // 默认选中当前类型
  }

  // 检查选中的目标类型，跳到下一步
  // 如果target是「高频表」realtime 需要服务端验证
  checkSelected() {
    this.transData.checkResult = '';
    // 类型没有改变
    if (this.transData.currentType === this.transData.targetType) {
      this.message.error('类型没有改变');
      return;
    }

    if (this.transData.targetType === this.INCREMENT_UPDATE_MODE) {
      // 调用worktableCtrl的方法准备分区数据
      // 无分区字段 不可转
      this.beginSetPartition();
      if (this.partitionData.empty_field) {
        this.transData.checkResult = '无可用分区字段'; // 文案：无可用分区字段
      }
    } else if (this.transData.targetType === this.REALTIME_UPDATE_MODE) {
      // 高频表要到服务器检查
      // 规则1：数据量小于10W
      if (this.tb.data_count > 100000) {
        this.transData.checkResult = '当前数据表数据量超过10W';
      }
    } else {
      // this.transData.currentStep = 'submiting';
    }
    // 开启弹层
    this.dialog
      .open(EtlTransformTbTypeDialogComponent, {
        data: {
          transData: this.transData,
          partitionData: this.partitionData,
          tbId: this.tb.tb_id
        }
      })
      .afterClosed()
      .subscribe(result => {
        if (result !== false) {
          this.tb.update_mode = result.data.mode;
          this.transData.editing = false;
          if (result.data.param) {
            const obj = this.tbAdvancedService.updatePartitionData(result.data.param);
            this.partitionData = obj.partitionData;
            this.partitionDataOriginal = obj.partitionDataOriginal;
          }
          this.showTransformDialog();
          this.preparePartitionData();
        }
      });
  }
  // 为编辑分区字段，准备数据
  preparePartitionData() {
    this.resetPartitionData();
    const calcFields: any = []; // 非数值类型计算字段
    const vfield_partition = this.partitionData.vfield_partition;
    this.partitionData.field_list = [];
    // 定义用于分区的字段列表
    this.originSchema.forEach((field: any) => {
      if (field.hasOwnProperty('flag') && field.flag === 1) {
        // 如果是计算字段，仅提供给高性能表转换使用
        if (field.type === 2 || field.type === 3) {
          calcFields.push({
            is_calc: true,
            field_id: field.field_id,
            name: field.name,
            data_type: field.type === 2 ? 'string' : 'date'
          });
        }
      } else {
        // 如果是基础字段（一般情况下数值类型不可以用作分区字段）
        if (field.type === 2 || field.type === 3) {
          this.partitionData.field_list.push({
            field_id: field.field_id,
            name: field.name,
            data_type: field.type === 2 ? 'string' : 'date'
          });
          // 例外：在增量合表（高性能表）中，有可能服务端设置了type=1的分区字段，并且是只读的
        } else if (field.field_id === this.partitionData.field_id && this.partitionData.readonly) {
          this.partitionData.field_list.push({
            field_id: field.field_id,
            name: field.name,
            data_type: 'number'
          });
        }
      }
    });
    let hasExisted = false;
    // BDP-22107 数据表类型切换 使用字段： 可分区字段 + 非数值类型计算字段
    if (!!vfield_partition) {
      this.partitionData.field_list = this.partitionData.field_list.concat(calcFields);
    }
    // 存在分区字段设置，直接编辑，反之提示
    this.partitionData.match_error = false;
    if (this.partitionData.set) {
      this.setPartitionView = 'edit';
      // 检查配置字段是否存在于可配列表
      hasExisted = this.checkFieldInList(hasExisted);
      this.partitionData.match_error = !hasExisted;
    } else {
      this.setPartitionView = 'preview';
    }
  }

  checkFieldInList(hasExisted: boolean): boolean {
    let flag = hasExisted;
    this.partitionData.field_list.forEach((item: any) => {
      if (item.field_id === this.partitionData.field_id) {
        flag = true;
        if (this.partitionData.data_type !== 'hash' && item.data_type !== this.partitionData.data_type) {
          this.partitionData.data_type = item.data_type;
          this.partitionData.granularity = item.data_type === 'string' ? '' : 'month';
        }
      }
    });
    return flag;
  }

  resetPartitionData() {
    // this.partitionData = $.extend(true,{},this.partitionDataOriginal)
    this.partitionData = _.cloneDeep(this.partitionDataOriginal);
  }

  // 设置高性能合表，分区字段获取hash值
  changePartitionMode() {
    const mode = this.partitionData.mode;

    this.partitionData.data_type = mode === '1' ? 'hash' : 'string';
    if (mode === '0') {
      return;
    }
    if (this.partitionData.granularity && !isNaN(Number(this.partitionData.granularity))) {
      return;
    }
    const param = {
      tb_id: this.tb.tb_id
    };
    this.tbAdvancedService.tbs.partitionNum(param).subscribe((data: any) => {
      this.partitionData.granularity = data.partitions;
    });
  }

  beginSetPartition() {
    this.setPartitionView = 'edit';
    // 初始化默认显示字段
    if (this.partitionData.field_list.length > 0) {
      this.partitionData.empty_field = false;
      // 如果没有设置分区字段，默认选中列表中的第一个
      if (!this.partitionData.field_id) {
        const defaultField = this.partitionData.field_list[0];
        this.partitionData.field_id = defaultField.field_id;
        this.partitionData.data_type = defaultField.data_type;
        if (defaultField.data_type === 'date') {
          this.partitionData.granularity = 'month';
        }
      }
    } else {
      this.partitionData.empty_field = true;
    }
  }

  delSetPartition() {
    const param = {
      tb_id: this.tb.tb_id
    };
    this.tbAdvancedService.tbs.partitionRemove(param).subscribe(
      data => {
        this.partitionData.set = false;
        this.message.success('删除成功');
        this.partitionDataOriginal = {
          tipShow: false,
          field_list: [],
          field_id: '',
          data_type: 'string',
          granularity: 'month',
          mode: '0',
          empty_field: false,
          set: false,
          match_error: false,
          readonly: false // 服务端提供
        };
        this.preparePartitionData();
      },
      error => {
        if (error.errInfo && error.errInfo.errInfo) {
          if (error.errInfo.errInfo.status === '20006') {
            this.message.error('高性能表不能删除分区字段');
          } else {
            this.message.error(error.errInfo.errInfo.errstr);
          }
        }
      }
    );
  }

  saveSetPartition() {
    if (!this.setPartitionDone) {
      return;
    }
    if (this.partitionData.data_type === 'hash' && !/^[1-9][0-9]*$/.test(this.partitionData.granularity)) {
      this.message.error('分区数量必须是正整数');
      return;
    }
    let option = '';
    if (this.partitionData.data_type !== 'string') {
      option = this.partitionData.granularity;
    }
    const fieldData = _.find(this.partitionData.field_list, field => {
      return !!(field.field_id === this.partitionData.field_id);
    });
    const data: any = {
      tb_id: this.tb.tb_id,
      base_field: this.partitionData.field_id,
      param: {
        type: this.partitionData.data_type,
        option
      },
      is_vfield: fieldData && fieldData.is_calc ? 1 : 0 // 表示设置分区的字段是否是计算字段
    };
    // this.partitionData.loading = true;
    data.param = JSON.stringify(data.param);
    this.setPartitionDone = false;
    this.tbAdvancedService.tbs
      .partitionSet({
        tb_id: data.tb_id,
        base_field: data.base_field,
        param: data.param,
        is_vfield: data.is_vfield
      })
      .subscribe(() => {
        this.setPartitionDone = true;
        this.message.success('保存成功');
        this.partitionData.editing = false;
        // $.extend(true, this.partitionDataOriginal, this.partitionData);
        this.partitionDataOriginal = _.cloneDeep(this.partitionData);
      });
  }

  changePartitionField(event: any, fieldId: any) {
    const fieldList = _.cloneDeep(this.partitionData.field_list);
    for (let i = 0, len = fieldList.length; i < len; i++) {
      if (fieldList[i].field_id === fieldId) {
        if (fieldList[i].data_type === 'date' || fieldList[i].data_type === 'string') {
          if (this.partitionData.data_type !== 'hash' || fieldList[i].data_type !== 'string') {
            this.partitionData.data_type = fieldList[i].data_type === 'string' ? 'string' : 'date';
            this.partitionData.granularity = fieldList[i].data_type === 'string' ? '' : 'month';
            this.partitionData.mode = '0';
          }
        }
        break;
      }
    }
    this.partitionData.match_error = false;
  }
}
