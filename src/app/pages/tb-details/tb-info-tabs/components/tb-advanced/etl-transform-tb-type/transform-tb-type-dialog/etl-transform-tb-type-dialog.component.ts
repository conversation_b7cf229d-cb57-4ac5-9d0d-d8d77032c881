import { finalize } from 'rxjs/operators';
import { Component, OnInit, Inject } from '@angular/core';
import { CommonDataService } from '../../../../../../../core/services/common-data.service';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { TbAdvancedService } from '../../tb-advanced.service';
import * as _ from 'lodash';
import { MessageService } from '@haizhi/ng-hertz/message';

@Component({
  selector: 'etl-transform-tb-type-dialog',
  templateUrl: './etl-transform-tb-type-dialog.html',
  styleUrls: ['./etl-transform-tb-type-dialog.component.scss']
})
export class EtlTransformTbTypeDialogComponent implements OnInit {
  typesMap: object;
  transData: any;
  description: any;
  partitionData: any;
  tbId: any;
  constructor(
    private message: MessageService,
    public dialogRef: MatDialogRef<EtlTransformTbTypeDialogComponent>,
    private commonData: CommonDataService,
    private tbAdvancedService: TbAdvancedService,
    private dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public dialogData: any
  ) {}

  ngOnInit(): void {
    this.typesMap = {
      0: ['file-normal', '普通数据表'],
      1: ['file-data', '高性能表'],
      2: ['file-speed', '高频更新表']
    };
    this.description = {
      0: ['1.不支持多表关联的增量更新，数据量过大时，合表时间可能较长', '2.数据更新频次最高为1小时1次'],
      1: ['1.高性能表在进行多表关联时作为主表才能进行增量更新', '2.数据更新频次最高为1小时1次'],
      2: ['1.不支持对数据的修改操作，支持数据的追加和清除操作', '2.数据量不超过10万条']
    };
    this.transData = this.dialogData.transData;
    this.partitionData = this.dialogData.partitionData;
    this.tbId = this.dialogData.tbId;
  }

  changePartitionField(event: any, fieldId: any) {
    const fieldList = _.cloneDeep(this.partitionData.field_list);
    for (let i = 0, len = fieldList.length; i < len; i++) {
      if (fieldList[i].field_id === fieldId) {
        if (fieldList[i].data_type === 'date' || fieldList[i].data_type === 'string') {
          if (this.partitionData.data_type !== 'hash' || fieldList[i].data_type !== 'string') {
            this.partitionData.data_type = fieldList[i].data_type === 'string' ? 'string' : 'date';
            this.partitionData.granularity = fieldList[i].data_type === 'string' ? '' : 'month';
            this.partitionData.mode = '0';
          }
        }
        break;
      }
    }
    this.partitionData.match_error = false;
  }
  // 设置高性能合表，分区字段获取hash值
  changePartitionMode() {
    const mode = this.partitionData.mode;
    this.partitionData.data_type = mode === 1 ? 'hash' : 'string';
    if (mode === '0') {
      return;
    }
    if (this.partitionData.granularity && !isNaN(Number(this.partitionData.granularity))) {
      return;
    }
    const param = {
      tb_id: this.tbId
    };
    this.tbAdvancedService.tbs.partitionNum(param).subscribe((data: any) => {
      this.partitionData.granularity = data.partitions;
    });
  }

  // 提交转换请求到服务器
  submitTransform() {
    if (this.transData.checkResult) {
      this.message.error(this.transData.checkResult);
      this.dialogRef.close();
      return;
    }
    let param: any;
    const postData: any = {
      tb_id: this.tbId,
      mode: this.transData.targetType
    };
    if (postData.mode === '1') {
      if (this.partitionData.data_type === 'hash' && !/^[1-9][0-9]*$/.test(this.partitionData.granularity)) {
        this.commonData.globalTip.set('dada');
        return;
      }
      const fieldData = _.find(this.partitionData.field_list, field => {
        return !!(field.field_id === this.partitionData.field_id);
      });
      // 构建分区参数
      param = {
        base_field: this.partitionData.field_id,
        param: {
          type: this.partitionData.data_type,
          option: this.partitionData.data_type !== 'string' ? this.partitionData.granularity : ''
        }
      };
      param.param = JSON.stringify(param.param);
      postData.param = JSON.stringify(param); // str,json 高性能的分区新新,只需转换高性能表时传此参数
      postData.is_vfield = fieldData && fieldData.is_calc ? 1 : 0; // 表示设置分区的字段是否是计算字段
    }
    this.transData.showLoading = true;
    this.tbAdvancedService.tbs
      .updateModeModify(postData)
      .pipe(
        finalize(() => {
          this.transData.showLoading = false;
        })
      )
      .subscribe(data => {
        this.message.success('保存成功');
        this.dialogRef.close({
          data: {
            mode: postData.mode,
            param
          }
        });
      });
  }
}
