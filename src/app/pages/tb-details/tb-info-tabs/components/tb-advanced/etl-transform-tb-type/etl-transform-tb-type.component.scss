@import '~@haizhi/ui/styles/themes/light.data';
.pannel_normal {
  margin-bottom: 16px;
  .pannel__header {
    padding: 0 8px;
    margin-bottom: 8px;
    color: rgba(15, 34, 67, 0.48);
    padding-left: 0;
  }
  .header__title_normal {
    float: left;
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: rgba(21, 22, 24, 0.72);
    display: inline-block;
    height: 100%;
    .active-font {
      color: #5182e4;
    }
    .bdp-tooltip {
      display: inline-block;
      line-height: 1em;
      .bdp-tooltip-content {
        width: 240px;
        padding: 14px 16px;
        line-height: 19px;
        top: 1.6em;
        box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.3), 0px 10px 24px 0px rgba(0, 0, 0, 0.3);
      }
      i.bdp-icon {
        opacity: 0.7;
      }
    }
  }
  .header__operate_normal {
    float: right;
    height: 32px;
    line-height: 32px;
  }
  .pannel__body {
    padding: 16px 0;
    line-height: 32px;
    background-color: unset;
    padding-top: 0;
    .mt-10 {
      margin-top: -22px;
      margin-bottom: 8px;
    }
    .check-content {
      background-color: #ffffff;
      padding: 16px 24px;
      box-shadow: $container-c100;
      border-radius: 8px;
    }
    p {
      line-height: 1.6em;
      padding: 0.55em 0;
    }
    .label-tip {
      font-weight: 600;
      font-size: 14px;
      line-height: 20px;
      color: rgba(21, 22, 24, 0.36);
    }
    ul.option-list {
      li {
        padding-right: 20px;
        padding-bottom: 8px;
        margin-top: 8px;
      }
      .option-list-title {
        display: flex;
        align-items: center;
        ::ng-deep .hz-radio-wrapper .hz-radio input[type='radio'].hz-radio-input:disabled {
          opacity: 1;
        }
      }
      label {
        color: $type-800;
      }
      // label {
      //   display: block;
      //   position: relative;
      //   margin-bottom: 4px;
      //   color: rgba(15, 34, 67, 0.72);
      //   font-weight: 600;
      // }
      .leading-symbol {
        position: absolute;
        left: -23px;
        top: 9px;
      }
    }
    p.description {
      line-height: 20px;
      padding: 0;
      color: $type-700;
      .highlight {
        color: $chart-primary-error;
      }
    }
  }
}
.btn-last {
  margin-left: 8px;
}
.header {
  zoom: 1;
}

.header:before,
.header:after {
  display: table;
  content: '';
}

.header:after {
  clear: both;
}

input[disabled] {
  opacity: 0.24;
}

.partition-set-dialog select {
  max-width: 140px;
}

.partition-set-dialog .edit-set-partition .title {
  width: 60px;
  display: inline-block;
  margin-right: 4px;
}
.partition-set-dialog .edit-set-partition input[disabled='disabled'],
.partition-set-dialog .edit-set-partition select[disabled='disabled'] {
  opacity: initial;
  background: 0 0;
}

.bdp-tooltip-content {
  top: 2.5em;
  left: 0;
  line-height: 1.4;
  position: absolute;
  z-index: 2;
  padding: 9px 16px;
  white-space: normal;
  text-align: left;
  background-color: #fff;
  box-shadow: 0px 0px 6px 0px rgba(0, 0, 0, 0.1), 0px 10px 12px 0px rgba(170, 182, 206, 0.36);
}

.edit-set-partition {
  padding: 24px;
  box-shadow: $container-c100;
  box-sizing: border-box;
  background-color: #fff;
  border-radius: 8px;

  .input-box {
    padding: 0 !important;
  }

  .title {
    line-height: 28px;
    margin-right: 16px;
    vertical-align: middle;
  }

  .select-box {
    width: 212px;
    display: inline-block;
    vertical-align: middle;
  }
}
