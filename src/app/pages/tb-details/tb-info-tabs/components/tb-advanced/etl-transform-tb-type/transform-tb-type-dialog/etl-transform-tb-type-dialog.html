<div class="ngDialog-width-552">
  <div mat-dialog-title>
    <span>数据表类型切换</span>
    (
    <span>当前表数据量</span>
    <span class="active-font">100</span>
    )
  </div>
  <mat-dialog-content>
    <div class="ngdialog-message">
      <div class="visual-diagram">
        <div class="col cube">
          <i
            hz-icon
            hzName="{{typesMap[transData.currentType][0]}}"
            hzColor="rgba(31,113,255,1)"
            class="mr4"
          ></i>
          {{typesMap[transData.currentType][1]}}
        </div>
        <div><img src="assets/images/advanced/dot-arrow.svg" alt="" /></div>
        <div class="col cube">
          <i
            hz-icon
            hzName="{{typesMap[transData.targetType][0]}}"
            hzColor="rgba(31,113,255,1)"
            class="mr4"
          ></i>
          {{typesMap[transData.targetType][1]}}
        </div>
      </div>
      <div
        class="partition-selector"
        *ngIf="transData.targetType == 1 && !partitionData.empty_field"
      >
        <p>请选择高性能表的分区字段（建议使用日期字段）</p>
        <p *ngIf="!partitionData.empty_field">
          <hz-select
            [hzSize]="'small'"
            [(ngModel)]="partitionData.field_id"
            hzPlaceholder="请选择"
            (ngModelChange)="changePartitionField($event,partitionData.field_id)"
          >
            <hz-option
              *ngFor="let item of partitionData.field_list"
              [hzLabel]="item.name"
              [hzValue]="item.field_id"
            ></hz-option>
          </hz-select>
        </p>
        <div *ngIf="!partitionData.empty_field && partitionData.data_type == 'date'" class="mt16">
          <p class="title">分区粒度</p>
          <hz-select
            [hzSize]="'small'"
            [(ngModel)]="partitionData.granularity"
            hzPlaceholder="请选择"
          >
            <hz-option [hzLabel]="'年'" [hzValue]="'year'"></hz-option>
            <hz-option [hzLabel]="'月'" [hzValue]="'month'"></hz-option>
            <hz-option [hzLabel]="'日'" [hzValue]="'day'"></hz-option>
          </hz-select>
        </div>
        <div *ngIf="!partitionData.empty_field && partitionData.data_type != 'date'" class="mt16">
          <div>
            <p class="title">分区方式</p>
            <hz-select
              [hzSize]="'small'"
              [(ngModel)]="partitionData.mode"
              hzNoMatchPlaceholder="请选择"
              (ngModelChange)="changePartitionMode()"
            >
              <hz-option [hzLabel]="'字段值分区'" [hzValue]="0"></hz-option>
              <hz-option [hzLabel]="'hash值分区'" [hzValue]="1"></hz-option>
            </hz-select>
          </div>
          <div *ngIf="partitionData.mode == 1" class="mt16">
            <p class="title">分区数量</p>
            <input type="number" class="w100" hz-input [(ngModel)]="partitionData.granularity" />
          </div>
        </div>
        <p *ngIf="partitionData.match_error" class="cr-hint match-error">
          原分区字段已被修改，修改后的字段不支持分区，请重新设置
        </p>
      </div>

      <div class="mt16">
        <p class="mute-font">注意事项:</p>
        <p class="description">{{description[transData.targetType][0]}}</p>
        <p class="description">{{description[transData.targetType][1]}}</p>
      </div>
      <p class="mt16 highlight" *ngIf="transData.checkResult">
        <span>检查结果</span>
        ：{{transData.checkResult}}
      </p>
    </div>
  </mat-dialog-content>

  <mat-dialog-actions>
    <button mat-button class="mat-button" (click)="submitTransform()">确定</button>
    <button mat-button class="mat-button" [mat-dialog-close]="false">取消</button>
  </mat-dialog-actions>
  <hz-loading-gif *ngIf="transData.showLoading"></hz-loading-gif>
</div>
