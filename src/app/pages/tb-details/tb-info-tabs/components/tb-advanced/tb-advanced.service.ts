import { Injectable } from '@angular/core';
import { HttpService } from '../../../../../core/services/http.service';
import * as _ from 'lodash';
@Injectable({
  providedIn: 'root'
})
export class TbAdvancedService {
  partitionData: any = {
    tipShow: false,
    field_list: [],
    field_id: '',
    data_type: 'string',
    granularity: 'month',
    mode: '0',
    empty_field: false,
    set: false,
    match_error: false,
    readonly: false // 服务端提供
  };
  partitionDataOriginal: any;
  tbs = {
    partitionNum: (param: any = {}) => {
      return this.http.post('/api/tb/partition/num', param);
    },
    preview: (param: any = {}) => {
      return this.http.post('/api/tb/preview', param);
    },
    partitionRemove: (param: any = {}) => {
      return this.http.post('/api/tb/partition/remove', param, true);
    },
    partitionSet: (param: any = {}) => {
      return this.http.post('/api/tb/partition/set', param);
    },
    updateModeModify: (param: any = {}) => {
      return this.http.post('/api/tb/update_mode/modify', param);
    }
  };
  constructor(private http: HttpService) {}

  /**
   *
   * 高级设置
   * 唯一更新partitionData的方法
   * @param partitionData JSON/Object
   */
  updatePartitionData(partitionData: any, previewData?: any): any {
    if (typeof partitionData === 'string') {
      partitionData = JSON.parse(partitionData);
    }
    partitionData = partitionData || {};
    if (typeof partitionData.param === 'string') {
      partitionData.param = JSON.parse(partitionData.param);
    }
    partitionData.param = partitionData.param || {};
    const vfieldPartition = this.partitionData.vfieldPartition || (previewData && previewData.vfieldPartition);
    this.partitionData = {
      tipShow: false,
      field_list: [],
      field_id: partitionData.base_field,
      data_type: partitionData.param.type || '',
      granularity: partitionData.param.option || '',
      empty_field: false,
      mode: partitionData.param.type === 'hash' ? '1' : '0', // 高性能表选择非日期类型分区方式
      set: !!partitionData.base_field,
      match_error: false,
      readonly: partitionData.readonly,
      vfieldPartition,
      editing: false // 是否处在编辑状态
    };
    this.partitionDataOriginal = _.cloneDeep(this.partitionData);
    const obj = {
      partitionData: this.partitionData,
      partitionDataOriginal: this.partitionDataOriginal
    };
    return obj;
  }
  /**
   *
   *
   */
  setTbPermission(selectObj: any, previewData: any): any {
    const tb = selectObj.tb;
    const data = previewData;
    let result = {};
    let isNotLogType;
    let enableSetPartition;
    if (!tb) {
      // console.error("已选择的tb对象不能为空！");
      return {};
    }
    if (data) {
      isNotLogType = data.type !== 6; // 日志埋点图表类型
      enableSetPartition = getEnableSetPartition();
    } else {
      isNotLogType = tb.type !== 6; // 日志埋点图表类型
      enableSetPartition = false;
    }
    const enableTransformTbType = getEnableTransformTbType();
    const enableUpdateRecord = getEnableRecordTab();

    // 除了下面这些意外，还会常用的并且直接用的是 tb.type、tb.tb_type、tb.permission_type
    result = {
      isNotLogType, // 是否是日志类型表（目前只对haizhi域开放）--- preview时才有效
      // tab里的具体模块
      enableSetField: !(tb.tb_type === 'access_tb' || !isNotLogType), // 是否可以有字段设置的编辑字段按钮
      enableEditField: tb.tb_type === 'self' && tb.permission_type !== 2, // 是否可以在预览界面编辑字段
      enableAddField: tb.tb_type === 'self' && tb.permission_type !== 2, // 是否可以增加计算字段
      enableReplace: tb.tb_type === 'self' && tb.permission_type !== 2 && tb.type === 'excel', // 是否可以替换或追加
      enableFullUpdate: getEnableFullUpdate(), // 是否可以触发增量合表全量更新
      enableTransformTbType, // 是否可见数据表性能类型
      enableChangeTbType: getEnableChangeTbType(), // 是否可以切换数据表性能类型
      enableSetPartition, // 是否可以设置分区 --- preview时才有效

      enableEditModelStruct: undefined, // 是否可以编辑模型结构 --- 在发模型结构接口时赋值
      enableUpdateRecord, // 是否能看到更新记录
      enableHistorySecTab: enableUpdateRecord, // 是否能看到历史记录里的二级tab，只有2个都显示的时候才看到2级tab
      enableDelInRecord: tb.permission_type !== 2, // 是否能在更新记录中的文件列表中删除文件
      // tab的权限
      enableRecordTab: enableUpdateRecord, // 历史记录（更新记录 + 操作日志）
      enableEditFieldTab: tb.tb_type === 'self' || tb.tb_type === 'access_tb', // 字段设置
      enableModelStructTab: getEnableModelStruct(), // 模型结构
      enableAdvanceSetTab: enableSetPartition || enableTransformTbType // 高级设置（数据表性能类型 + 分区设置）
    };
    if (!selectObj.permission) {
      selectObj.permission = {};
    }
    selectObj.permission = result;
    // $.extend(true, selectObj.permission, result);
    return result;

    function getEnableFullUpdate() {
      return tb.tb_type === 'self' && tb.update_mode === 1 && (tb.type === 'join' || tb.type === 'union' || tb.type === 'script');
    }
    function getEnableRecordTab() {
      return (
        isNotLogType &&
        tb.type !== 'ds' &&
        tb.type !== 'opends' &&
        tb.type !== 'public' &&
        tb.type !== 'extrahive' &&
        tb.tb_type !== 'access_tb'
      );
    }

    // 这里比较复杂，没能全部统在一起
    function getEnableSetPartition() {
      // 当前用户&当前表 是否可以用分区功能
      const canUseFeaturePartition = tb.type !== 'union' && tb.type !== 'public' && tb.type !== 'allot' && tb.tb_type !== 'access_tb';
      // 依tb_id前缀判断「分享表」更靠谱，与服务端逻辑一致。比如：public的表也不能编辑
      let partitionData = data.partition;
      if (typeof partitionData === 'string') {
        partitionData = JSON.parse(partitionData);
      }
      partitionData = partitionData || {};
      // 分区字段设置
      return canUseFeaturePartition && data.hasOwnProperty('can_partition') && !!data.can_partition;
    }

    function getEnableModelStruct() {
      return ['aggr', 'join', 'union', 'script', 'transpose'].indexOf(tb.type) > -1 && tb.tb_type !== 'access_tb';
    }

    function getEnableTransformTbType() {
      return tb.update_mode !== undefined;
    }

    function getEnableChangeTbType() {
      const canUseFeaturePartition = ['union', 'public', 'allot'].indexOf(tb.type) < 0 && tb.tb_type !== 'access_tb';
      // 分享表&合表 不能转换update_mode
      if (!canUseFeaturePartition || ['join', 'script', 'aggr', 'transpose', 'adv_model'].indexOf(tb.type) >= 0 || !isNotLogType) {
        return false;
      } else {
        return true;
      }
    }
  }
}
