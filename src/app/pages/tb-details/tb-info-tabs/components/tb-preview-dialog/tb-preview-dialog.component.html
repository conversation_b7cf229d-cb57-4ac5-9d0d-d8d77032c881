<div mat-dialog-title>
  查看数据
  <i hz-icon hzName="close" class="fr opacity8-i" (click)="cancel()"></i>
</div>
<mat-dialog-content>
  <div class="table-desc-tip">
    <span>显示最新</span>
    <em class="num ml-4 mr-4">{{ data.length }}</em>
    <span>条数据</span>
    ，
    <span>共</span>
    <em class="num ml-4 mr-4">{{ version.data_count }}</em>
    <span>条数据</span>
    <!-- <a class="ml-4" (click)="export()">
      <i hz-icon hzName="export-data"></i>
    </a> -->
    <span>• 最近更新时间：</span>
    {{ version.data_utime }}
  </div>
  <div class="hz-table-wrap" *ngIf="!previewLoading">
    <table
      class="hz-table"
      border="0"
      cellpadding="0"
      cellspacing="0"
      [ngClass]="{ 'no-data': data.length <= 0 }"
    >
      <thead>
        <tr>
          <th *ngFor="let head of schema">{{ head.name }}</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of data; let index = index">
          <td *ngFor="let v of item">
            {{ v }}
          </td>
        </tr>
        <tr *ngIf="data.length <= 0">
          <td :colspan="item.length">
            <hz-empty hzEmptyIcon="worksheet-empty-light" [hzEmptyTitle]="'暂无数据'"></hz-empty>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
  <hz-loading-gif *ngIf="previewLoading"></hz-loading-gif>
</mat-dialog-content>
<div class="mat-dialog-actions">
  <button class="mat-button" (click)="cancel()">关闭</button>
</div>
