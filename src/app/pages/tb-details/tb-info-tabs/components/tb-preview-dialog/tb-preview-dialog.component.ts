import { Component, OnInit, Input, Output, EventEmitter, ElementRef, ViewChild, Inject } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import * as _ from 'lodash';
import { MessageService } from '@haizhi/ng-hertz/message';
import { TbDetailsService } from '../../../tb-details.service';
import { finalize } from 'rxjs/operators';
interface VersionInfo {
  tb_id: string;
  tag: string;
  version: string;
  belong_dataflow_name: string;
  task_batch: string;
  data_utime: string;
  data_count: number;
}
@Component({
  selector: 'tb-preview-dialog',
  templateUrl: './tb-preview-dialog.component.html',
  styleUrls: ['./tb-preview-dialog.component.scss']
})
export class TbPreviewDialogComponent implements OnInit {
  constructor(
    public dialogRef: MatDialogRef<TbPreviewDialogComponent>,
    private messageService: MessageService,
    private tbDetailsService: TbDetailsService,
    @Inject(MAT_DIALOG_DATA) public dialogData: any
  ) {}
  mapIcon = new Map([
    ['number', 'type-number'],
    ['init', 'type-number'],
    ['double', 'type-number'],
    ['string', 'type-string'],
    ['datetime', 'type-date'],
    ['date', 'type-date']
  ]);
  tbId: string = '';
  version: VersionInfo = {
    tb_id: '',
    tag: '',
    version: '',
    belong_dataflow_name: '',
    task_batch: '',
    data_utime: '',
    data_count: 0
  };
  tbName = '';
  schema: Array<{ data_type: string; name: string; origin_name: string; title: string }> = [];
  data: Array<string> = [];
  previewLoading = true;
  ngOnInit(): void {
    this.tbId = this.dialogData.tbId;
    this.version = this.dialogData.version;
    this.tbName = this.dialogData.tbName;
    this.tbPreview();
  }

  cancel() {
    this.dialogRef.close();
  }

  tbPreview() {
    this.previewLoading = true;
    this.tbDetailsService
      .tbPreivew({
        tb_id: this.tbId,
        version: this.version.version,
        tag: this.version.tag
      })
      .pipe(finalize(() => (this.previewLoading = false)))
      .subscribe(data => {
        if (data) {
          this.schema = data.schema;
          this.data = data.data;
        }
      });
  }
}
