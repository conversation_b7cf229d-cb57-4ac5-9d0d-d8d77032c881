@import '@haizhi/ui/styles/themes/light.data.scss';
.mat-dialog-title .mapping-close {
  display: inline-block;
  float: right;
  margin-top: 8px;
  cursor: pointer;
  opacity: 0.8;
  &:hover {
    opacity: 1;
  }
}

.mat-dialog-actions {
  position: relative;
  line-height: 20px !important;
  margin-top: 0 !important;
  padding-top: 24px !important;
  .line-height-32 {
    position: absolute;
    height: 32px;
    top: 8px;
    input {
      margin-top: 0;
    }
  }
}
.mat-dialog-content {
  padding: 0 32px;
  height: 351px;
  .table-desc-tip {
    height: 32px;
    line-height: 32px;
    margin-bottom: 8px;
    color: $type-700;
    float: right;
    .num {
      color: $primary-900;
    }
  }
  .hz-table-wrap {
    box-shadow: 0px 8px 64px rgba(15, 34, 67, 0.1), 0px 0px 1px rgba(15, 34, 67, 0.16);
    border-radius: 8px;
    padding: 8px 16px;
    height: 303px;
    overflow: auto;
    width: 100%;
    .no-data {
      height: 100%;
      tr {
        &:hover {
          background: transparent;
        }
        td {
          box-shadow: none;
        }
      }
    }
  }
}

.mat-dialog-actions {
  height: 64px !important;
}
