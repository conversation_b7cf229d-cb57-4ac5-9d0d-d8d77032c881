export interface TagConfig {
  map_tb_id: string;
  scheduler_conf: any;
  code: string;
  name: string;
  have_tag_value: number;
  category_tree: string;
  rule_from: string;
  update_time: string;
  tag_id: string;
  tag_value_list: string[];
  update_status: number;
  tag_name: string;
  tb_id: string;
  dataflow_id: string;
  tag_value_count: number;
  tag_value_type: number;
  map_tb_name: string;
  tb_list: any;
  desc: string;
}
