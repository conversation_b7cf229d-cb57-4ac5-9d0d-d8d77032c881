<div class="config-survey">
  <div>
    <div class="tag-head"><p class="tag-title">标签信息</p></div>
    <div class="tag-content" *ngIf="isRequestOver">
      <ul>
        <li>
          <span class="title">是否含有标签值</span>
          <span class="des">{{ tagValueMap[haveTagValue] }}</span>
        </li>
        <ng-container *ngIf="haveTagValue === 1">
          <li>
            <span class="title">标签值类型</span>
            <span class="des">{{ tagValueTypeMap[tagValueType] }}</span>
          </li>
          <li>
            <span class="title">关联字典表</span>
            <ng-container *ngIf="mapTbName !== ''; else noTbName">
              <span class="des">{{ mapTbName }}</span>
            </ng-container>
            <ng-template #noTbName>
              <span>--</span>
            </ng-template>
          </li>
        </ng-container>
      </ul>
    </div>
  </div>
  <div *ngIf="haveTagValue === 1 && isRequestOver">
    <div class="tag-value">
      <p class="tag-title">标签值</p>
      <p class="tag-value-title-right">
        共
        <span>{{ tagValueCount }}</span>
        个标签值
      </p>
    </div>
    <div class="tag-body">
      <ul [ngClass]="{ empty: tagValueList.length === 0 }">
        <ng-container *ngIf="tagValueList.length > 0; else showEmpty">
          <li *ngFor="let item of tagValueList">
            {{ item }}
          </li>
        </ng-container>
        <ng-template #showEmpty>
          <hz-empty hzEmptyIcon="no-result-light" hzEmptyTitle="当前没有标签值哦~~"></hz-empty>
        </ng-template>
      </ul>
    </div>
  </div>
  <hz-loading-gif *ngIf="loading"></hz-loading-gif>
</div>
