import { finalize } from 'rxjs/operators';
import { Component, OnInit } from '@angular/core';
import { TbDetailsService } from '../../../../tb-details.service';
@Component({
  selector: 'tag-config-overview',
  templateUrl: './tag-config-overview.component.html',
  styleUrls: ['./tag-config-overview.component.scss']
})
export class TagConfigOverviewComponent implements OnInit {
  loading = false;
  isRequestOver = false;
  tbId = this.tbDetailsService.curTbId;
  haveTagValue = 0;
  tagValueType = 0;
  mapTbName = '';
  tagValueCount = 0;
  tagValueList: string[] = [];
  tagValueMap: { [index: number]: string } = {
    0: '否',
    1: '是'
  };
  tagValueTypeMap: { [index: number]: string } = {
    0: '属性类',
    1: '统计类'
  };
  constructor(private tbDetailsService: TbDetailsService) {}
  /**
   * 数据请求
   */
  getTagRule() {
    this.loading = true;
    this.tbDetailsService
      .getTagRule(this.tbId)
      .pipe(
        finalize(() => {
          this.loading = false;
          this.isRequestOver = true;
        })
      )
      .subscribe(res => {
        console.log(res);
        this.haveTagValue = res.have_tag_value;
        this.tagValueType = res.tag_value_type;
        this.mapTbName = res.map_tb_name;
        this.tagValueCount = res.tag_value_count;
        this.tagValueList = res.tag_value_list;
      });
  }
  ngOnInit(): void {
    this.getTagRule();
  }
}
