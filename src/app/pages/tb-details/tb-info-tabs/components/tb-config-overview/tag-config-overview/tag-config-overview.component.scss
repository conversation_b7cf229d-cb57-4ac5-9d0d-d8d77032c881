@import '~@haizhi/ui/styles/themes/light.data';
.config-survey {
  font-size: 12px;
  .tag-head {
    padding: 6px 0;
    .tag-title {
      font-weight: 600;
      line-height: 20px;
      color: $type-800;
    }
  }
  .tag-content {
    margin-top: 8px;
    background-color: white;
    box-shadow: $container-c100;
    border-radius: 8px;
    ul {
      padding: 16px;
      display: flex;
      li {
        list-style: none;
        height: 20px;
        margin: 5px 56px 5px 0;
        .title {
          font-weight: 600;
          line-height: 20px;
          color: $type-600;
          margin-right: 8px;
        }
        .des {
          line-height: 20px;
          color: $type-800;
        }
      }
      li:last-child {
        margin-right: 0;
      }
    }
  }
  .tag-value {
    display: flex;
    justify-content: space-between;
    margin: 24px 0 8px 0;
    .tag-title {
      font-weight: 600;
      line-height: 20px;
      color: $type-800;
      margin: 0;
    }
    .tag-value-title-right {
      line-height: 20px;
      color: $type-700;
      margin: 6px 0;
      span {
        color: $primary-900;
        font-weight: 600;
      }
    }
  }
  .tag-body {
    ul {
      padding: 18px 18px 10px 18px;
      display: flex;
      flex-wrap: wrap;
      background: #ffffff;
      box-shadow: $container-c100;
      border-radius: 8px;
      li {
        line-height: 20px;
        padding: 4px 10px;
        color: $type-800;
        background: $mono-a200;
        border-radius: 16px;
        margin: 0 8px 8px 0;
      }
    }
    .empty {
      justify-content: center;
    }
  }
}
