import { finalize } from 'rxjs/operators';
import { Component, OnInit, Input, EventEmitter, Output } from '@angular/core';
import { TbDetailsService } from '../../../../tb-details.service';
import { MessageService } from '@haizhi/ng-hertz/message';
import * as _ from 'lodash';

@Component({
  selector: 'relation-config-overview',
  templateUrl: './relation-config-overview.component.html',
  styleUrls: ['./relation-config-overview.component.scss']
})
export class RelationConfigOverviewComponent implements OnInit {
  tbId = this.tbDetailsService.curTbId;
  relationTypeId = this.tbDetailsService.tbBaseInfo.relation_type_id;
  relationRules: Array<any> = [];
  relationType = 0;
  viewType = this.tbDetailsService.tbBaseInfo.config_type; // 4、数据关系 5、聚合关系
  loading = false;
  relationData = {
    desc: '',
    code: ''
  };
  operatorMap = new Map([
    [0, '等于'],
    [1, '不等于'],
    [2, '大于'],
    [3, '小于'],
    [4, '大于等于'],
    [5, '小于等于'],
    [6, '包含'],
    [7, '不包含'],
    [8, '为空'],
    [9, '不为空'],
    [10, '选择日期范围'],
    [19, '不相同'],
    [20, '相同'],
    [21, '偏移'],
    [22, '区间交集']
  ]);
  transTime: { [index: string]: number } = {
    0: 1,
    1: 60,
    2: 3600
  };
  fromFieldEntityName = '';
  toFieldEntityName = '';
  relationsList = []; // 聚合关系列表
  @Output() relationDataCallback = new EventEmitter();
  constructor(private tbDetailsService: TbDetailsService, private messageService: MessageService) {}

  ngOnInit() {
    if (this.viewType === 4) {
      this.getRuleInfo();
    } else {
      this.getCombineInfo();
    }
  }

  getRuleInfo() {
    this.loading = true;
    this.tbDetailsService
      .getRelationRule(this.tbId, this.relationTypeId)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe((data: any) => {
        this.relationRules = data.relation_rule;
        this.relationType = data.type;
        if (this.relationType === 0) {
          this.fromFieldEntityName = data.from_field_entity_name;
          this.toFieldEntityName = data.to_field_entity_name;
        }
        _.forEach(this.relationRules, rule => {
          rule.rule = JSON.parse(rule.rule);
        });
        this.relationDataCallback.emit({
          relation_desc: data.relation_type_desc,
          code: data.code
        });
      });
  }

  getCombineInfo() {
    this.loading = true;
    this.tbDetailsService
      .getCombineInfo(this.tbId)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(data => {
        this.relationsList = data.ori_relations;
      });
  }
}
