<div class="relation-rule-wrap" *ngIf="viewType === 4">
  <div class="relation-rule">
    <ul *ngIf="relationRules.length > 0">
      <ng-container *ngFor="let rule of relationRules">
        <ng-container *ngIf="rule.enable">
          <li class="rule-item mb24">
            <div class="rule-item-title">
              <ng-container *ngIf="relationType == 0">
                <p class="mr24 mb8">
                  <span class="weight">起点实体</span>
                  <span class="name ml16">
                    【{{ fromFieldEntityName }}】{{ rule.from_field_name }}
                  </span>
                </p>
                <p class="mr24 mb8">
                  <span class="weight">终点实体</span>
                  <span class="name ml16">【{{ toFieldEntityName }}】{{ rule.to_field_name }}</span>
                </p>
                <p class="mr24 mb8">
                  <span class="weight">关系时间</span>
                  <span class="name ml16">{{ rule.start_date_field_name }}</span>
                </p>
                <!-- <p class="mr24 mb8">
                                    <span class="weight">关系结束时间</span>
                                    <span class="name ml16">{{rule.end_date_field_name}}</span>
                                </p> -->
              </ng-container>
              <ng-container *ngIf="relationType == 1">
                <p class="mr24 mb8">
                  <span class="weight">关系实体</span>
                  <span class="name ml16">{{ rule.from_field_name }}</span>
                </p>
                <p class="mr24 mb8">
                  <span class="weight">关系时间</span>
                  <span class="name ml16">{{ rule.start_date_field_name }}</span>
                </p>
              </ng-container>
              <div class="rule" *ngIf="rule.rule && rule.rule.length > 0">
                <span class="tip mb4">配置规则</span>
                <ul>
                  <ng-container *ngFor="let item of rule.rule">
                    <li>
                      <ng-container *ngIf="item.operator == 22">
                        <span>
                          {{ item.join.title[0] }}
                        </span>
                        <span>区间交集</span>
                        <span>
                          {{ item.join.title[1] }}
                        </span>
                        <span>
                          大于等于
                          {{ item.join.range_value / transTime[item.join.range_value_view] }}
                          {{
                            item.join.range_value_view == '0'
                              ? '秒'
                              : item.join.range_value_view == '1'
                              ? '分'
                              : '时'
                          }}
                        </span>
                      </ng-container>
                      <ng-container *ngIf="item.operator == 19">
                        <span>
                          {{ item.join.title[0] }}
                        </span>
                        <span>不相同</span>
                      </ng-container>
                      <ng-container *ngIf="item.operator == 20">
                        <span>
                          {{ item.join.title[0] }}
                        </span>
                        <span>相同</span>
                      </ng-container>
                      <ng-container *ngIf="item.operator == 21">
                        <span>
                          {{ item.join.title[0] }}
                        </span>
                        <span>偏移</span>
                        <span>
                          {{ item.join.range_value / transTime[item.join.range_value_view] }}
                          {{
                            item.join.range_value_view == '0'
                              ? '秒'
                              : item.join.range_value_view == '1'
                              ? '分'
                              : '时'
                          }}
                        </span>
                      </ng-container>
                      <ng-container *ngIf="[19, 20, 21, 22].indexOf(item.operator) <= -1">
                        <span>
                          {{ item.where.field_name }}
                        </span>
                        <span>
                          {{
                            operatorMap.has(item.operator) ? operatorMap.get(item.operator) : '等于'
                          }}
                        </span>
                        <span>
                          {{ item.operator == 10 ? item.where.start_date : item.where.value }}
                        </span>
                        <span *ngIf="item.operator == 10">
                          {{ item.where.end_date }}
                        </span>
                      </ng-container>
                    </li>
                  </ng-container>
                </ul>
              </div>
            </div>
          </li>
        </ng-container>
      </ng-container>
    </ul>
    <hz-empty
      hzEmptyIcon="no-result-light"
      [hzEmptyTitle]="'暂无内容'"
      *ngIf="relationRules.length === 0"
    ></hz-empty>
  </div>
</div>
<div *ngIf="viewType === 5" class="combine-source">
  <nz-table
    [nzData]="relationsList"
    [nzShowPagination]="false"
    [nzScroll]="{ x: '1140px', y: 'calc(100vh - 200px)' }"
    *ngIf="relationsList.length > 0"
  >
    <thead>
      <tr>
        <th nzWidth="360px">关系表名称</th>
        <th nzWidth="300px" nzBreakWord="true">编码</th>
        <th nzWidth="142px">关系描述分类</th>
        <!-- <th>关系描述</th> -->
        <th nzWidth="95px">关系方向</th>
        <th nzWidth="116px">更新状态</th>
        <th nzWidth="126px">关系数据条数</th>
      </tr>
    </thead>
    <tbody>
      <ng-container *ngFor="let relation of relationsList">
        <tr>
          <td>
            <i hz-icon hzName="conect-table" hzColor="rgba(255,82,102,1)" class="mr4"></i>
            <span class="relation-name nowrap" title="{{ relation.relation_name }}">
              {{ relation.relation_name }}
            </span>
          </td>
          <td class="code">
            <div title="{{ relation.code }}">
              {{ relation.code }}
            </div>
          </td>
          <td class="relation-type">
            {{ relation.relation_type }}
          </td>
          <td>
            {{ relation.direction == 0 ? '双向' : '单向' }}
          </td>
          <td [ngClass]="{ fail: relation.update_status == 2 }">
            {{
              relation.update_status == 1
                ? '更新成功'
                : relation.update_status == 2
                ? '更新失败'
                : '更新中'
            }}
            <i
              class="ml4"
              hz-icon
              hzName="attention"
              hzColor="rgba(255,82,102,1)"
              *ngIf="relation.update_status == 2"
              hz-tooltip
              [hzTooltipTitle]="relation.update_failed_desc"
              [hzTooltipType]="'info'"
              [hzTooltipPlacement]="'top'"
            ></i>
          </td>
          <td>
            {{ relation.total_count | transformData }}
          </td>
        </tr>
      </ng-container>
    </tbody>
  </nz-table>
  <hz-empty
    hzEmptyIcon="no-result-light"
    hzEmptyTitle="暂无聚合来源"
    *ngIf="!loading && relationsList.length === 0"
  ></hz-empty>
</div>
<hz-loading-gif *ngIf="loading"></hz-loading-gif>
