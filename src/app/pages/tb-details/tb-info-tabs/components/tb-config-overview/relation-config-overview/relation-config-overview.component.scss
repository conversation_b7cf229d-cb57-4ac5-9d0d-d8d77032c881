@import '~@haizhi/ui/styles/themes/light.data';
.relation-rule-wrap {
  padding: 16px 24px 24px 24px;
  height: 100%;
  box-sizing: border-box;
  .relation-rule {
    height: 100%;
  }
}

.rule-item-title {
  font-size: 12px;
  p {
    display: inline-block;
    line-height: 32px;
    .weight {
      color: $type-600;
      font-weight: 600;
      line-height: 20px;
      margin-right: 8px;
    }
    .name {
      color: $type-800;
      line-height: 20px;
    }
  }
  .rule {
    box-shadow: $container-c100;
    background: $mono-100;
    border-radius: 8px;
    padding: 16px;
    span.tip {
      line-height: 20px;
      color: $type-600;
      font-weight: 600;
      display: inline-block;
    }
    ul {
      width: 800px;
      line-height: 40px;
      li {
        width: 100%;
        box-shadow: 0 -1px 0 0 $mono-a300 inset;
        padding-left: 16px;
        span {
          width: 25%;
          display: inline-block;
        }
        &:hover {
          background-color: $mono-a200;
        }
      }
    }
  }
}
// 数据聚合
.combine-source {
  padding: 8px 16px;
  box-shadow: $container-c100;
  border-radius: 8px;
  height: 100%;
  .relation-name {
    max-width: 300px;
  }
  tr {
    td {
      &.code {
        white-space: normal;
        height: 60px;
        div {
          line-height: 16px;
          overflow: hidden;
          text-overflow: ellipsis;
          word-break: break-all; /* 内容自动换行 */
          -webkit-box-orient: vertical; /* 垂直排列 */
          display: -webkit-box;
          -webkit-line-clamp: 2;
        }
      }
    }
    .fail {
      color: #ff5266;
    }
  }
}

.nowrap {
  display: inline-block;
  vertical-align: top;
}

@media screen and (min-width: 1680px) {
  table {
    td.code {
      width: 400px !important;
    }
    .nowrap {
      max-width: 400px !important;
    }
    .relation-type {
      max-width: 200px !important;
    }
  }
}

@media screen and (max-width: 1680px) {
  table {
    td.code {
      width: 300px !important;
    }
    .nowrap {
      max-width: 300px !important;
    }
    .relation-type {
      max-width: 200px !important;
    }
  }
}
