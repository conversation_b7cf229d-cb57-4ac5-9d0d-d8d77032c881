<div class="tab-version-record" *ngIf="!relationLoding">
  <div
    class="switch-container"
    [ngClass]="{
      'switch-active-container': versionEnabled,
      'switch-bg-white': tbType === 'STANDARD'
    }"
  >
    <article class="waring-tip" *ngIf="versionEnabled && tbType !== 'STANDARD'">
      <i hz-icon hz-action-icon hzName="info" hzColor="#1F71FF"></i>
      <span class="ml-4">
        开启版本记录后，每次模型运行结果都将保存（但会影响性能），您可以进行历史数据查看
      </span>
    </article>
    <article class="operator-tip">
      <div class="version-record-days mr-16" *ngIf="versionEnabled">
        <span>记录周期</span>
        <div class="ml-16 version-record-days-select">
          <hz-select
            [(ngModel)]="versionRecordDays"
            [hzBeforeSelect]="beforeSelect"
            #selectComponent
            [hzDisabled]="tbType === 'STANDARD'"
          >
            <hz-option
              *ngFor="let RecordTime of versionRecordTimeArray; let i = index"
              [hzValue]="RecordTime.value"
              [hzLabel]="RecordTime.name"
            ></hz-option>
          </hz-select>
        </div>
      </div>
      <div class="switch-btn">
        <hz-switch
          [(ngModel)]="versionEnabled"
          [hzChangeOnClick]="false"
          [hzBeforeSelect]="switchBeforeSelect"
          [hzDisabled]="tbType === 'STANDARD'"
        ></hz-switch>
        <span class="ml-4">{{ versionEnabled ? '关闭版本记录' : '开启版本记录' }}</span>
      </div>
    </article>
  </div>
  <div class="hz-table-wrap">
    <div class="hz-table-scroll">
      <table
        class="hz-table"
        border="0"
        cellpadding="0"
        cellspacing="0"
        [ngClass]="{ 'no-data': tbVersions.length <= 0 }"
      >
        <thead>
          <tr>
            <th>更新时间</th>
            <th>版本号</th>
            <th>模型运行批次</th>
            <th>操作</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let version of tbVersions; let index = index">
            <td>
              {{ version.data_utime }}
            </td>
            <td>
              {{ version.version }}
            </td>
            <td>
              {{ version.task_batch }}
            </td>
            <td class="field-sort">
              <button hz-button hzSize="m" hzType="action" class="mr-8" (click)="viewData(version)">
                <i hz-icon hzName="visible-true"></i>
                查看数据
              </button>
              <!-- <button hz-button hzSize="m" hzType="action" class="mr-8" (click)="export(version)">
                            <i hz-icon hzName="move-to"></i>
                            导出数据
                            </button> -->
              <button
                hz-button
                hzSize="m"
                hzType="action"
                (click)="deleteVersion(version, index)"
                *ngIf="tbType !== 'STANDARD'"
              >
                <i hz-icon hzName="trash"></i>
                删除
              </button>
            </td>
          </tr>
          <tr *ngIf="tbVersions.length <= 0">
            <td colspan="5">
              <hz-empty
                hzEmptyIcon="worksheet-empty-light"
                [hzEmptyTitle]="versionEnabled ? '暂无数据' : '未开启版本记录'"
              ></hz-empty>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
<hz-loading-gif *ngIf="relationLoding || localLoading"></hz-loading-gif>
