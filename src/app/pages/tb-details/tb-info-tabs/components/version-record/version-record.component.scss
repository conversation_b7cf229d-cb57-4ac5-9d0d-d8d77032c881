@import '~@haizhi/ui/styles/themes/light.data';
.tab-version-record {
  height: 100%;
}
.switch-container {
  height: 40px;
  line-height: 40px;
  padding: 0 16px;
  background: transparent;
  transition: all 0.4s cubic-bezier(0.2, 1.2, 0.5, 0.9);
  margin-bottom: 8px;
  &.switch-active-container {
    background: $primary-a100;
    border-radius: 8px;
  }
  &.switch-bg-white {
    background: transparent;
  }
  .waring-tip {
    display: inline-block;
    color: $type-800;
  }
  .operator-tip {
    display: inline-block;
    color: $type-600;
    font-weight: 600;
    .version-record-days {
      display: inline-block;
      .version-record-days-select {
        width: 136px;
        display: inline-block;
        vertical-align: middle;
      }
    }
    float: right;
    display: inline-block;
    .switch-btn {
      position: relative;
      padding-left: 20px;
      display: inline-block;
      hz-switch {
        position: absolute;
        right: 76px;
        top: -3px;
      }
    }
    span {
      display: inline-block;
      line-height: 40px;
      height: 40px;
    }
  }
}

.hz-table-wrap {
  box-shadow: 0px 8px 64px rgba(15, 34, 67, 0.1), 0px 0px 1px rgba(15, 34, 67, 0.16);
  border-radius: 8px;
  padding: 8px 16px;
  height: calc(100% - 48px);
  box-sizing: border-box;
  .hz-table-scroll {
    height: 100%;
    overflow-y: auto;
    th {
      position: sticky;
      top: 0;
      z-index: 1;
      background: $mono-100;
    }
  }
  .no-data {
    height: 100%;
    tr {
      &:hover {
        background: transparent;
      }
      td {
        box-shadow: none;
      }
    }
  }
}
