import { Observable, Observer } from 'rxjs';
import { Component, OnInit, Input, Output, EventEmitter, ViewChild } from '@angular/core';
import { TbDetailsService } from '../../../tb-details.service';
import { Router } from '@angular/router';
import { ConfirmDialogComponent } from '../../../../../ui/common-dialog/dialog-confirm.component';
import { SelectComponent } from '@haizhi/ng-hertz/select';
import { TbPreviewDialogComponent } from './../tb-preview-dialog/tb-preview-dialog.component';
import { MatDialog } from '@angular/material';
import * as _ from 'lodash';
import { version } from 'moment';
import { MessageService } from '@haizhi/ng-hertz/message';
import { finalize } from 'rxjs/operators';
interface VersionInfo {
  tb_id: string;
  tag: string;
  version: string;
  belong_dataflow_name: string;
  task_batch: string;
  data_utime: string;
  data_count: number;
}
@Component({
  selector: 'version-record',
  templateUrl: './version-record.component.html',
  styleUrls: ['./version-record.component.scss']
})
export class VersionRecordComponent implements OnInit {
  relationLoding = true;
  localLoading = false;
  versionEnabled = false;
  versionRecordDays = 7;
  versionRecordTimeArray = [
    {
      name: '近一个月',
      value: 30
    },
    {
      name: '近两周',
      value: 14
    },
    {
      name: '近一周',
      value: 7
    },
    {
      name: '近三天',
      value: 3
    }
  ];
  tbVersions: Array<VersionInfo> = [];
  @ViewChild('selectComponent', { static: false }) selectComponent: SelectComponent;
  tbType = '';
  constructor(
    private router: Router,
    private dialog: MatDialog,
    private messageService: MessageService,
    private tbDetailsService: TbDetailsService
  ) {
    this.tbType = this.tbDetailsService.curTbType;
  }

  ngOnInit() {
    this.getConfigInfo();
  }

  getConfigInfo() {
    this.tbDetailsService.getVersionConfigInfo(this.tbDetailsService.curTbId).subscribe(
      data => {
        this.versionEnabled = !!data.version_enabled;
        this.versionRecordDays = data.version_record_days === 0 ? 7 : data.version_record_days;
        if (this.versionEnabled) {
          this.getVersions();
        } else {
          this.relationLoding = false;
        }
      },
      err => {
        this.relationLoding = false;
      }
    );
  }

  deleteVersion(version: VersionInfo, index: number) {
    const confrimRef = this.dialog.open(ConfirmDialogComponent, {
      disableClose: true,
      width: '300px',
      data: {
        title: '确认',
        content: '你确定要删除吗？'
      }
    });
    confrimRef.afterClosed().subscribe(async result => {
      if (result) {
        this.localLoading = true;
        const param: any = {
          version: version.version,
          tb_id: this.tbDetailsService.curTbId,
          tag: version.tag
        };
        this.tbDetailsService
          .deleteVersion(param)
          .pipe(finalize(() => (this.localLoading = false)))
          .subscribe(data => {
            this.messageService.success('删除成功');
            this.tbVersions.splice(index, 1);
          });
      }
    });
  }

  beforeSelect = (item: any) => {
    let content = '时间范围增大，从下次模型运行开始按照最新周期记录数据，是否继续？';
    if (item.hzValue < this.versionRecordDays) {
      content = '时间范围缩小，会清除范围外的历史数据，此操作不可恢复，是否继续？';
    }
    this.openConfirm(content, '记录周期', item.hzValue);
    return false;
  };

  switchBeforeSelect = () => {
    this.versionEnabled
      ? this.openConfirm('关闭版本记录后，则清除所有的历史数据，此操作不可恢复，是否继续？', '关闭版本记录')
      : this.modifyVersionConfig();
    return false;
  };

  openConfirm(content: string, title: string, value?: number) {
    const confirmRef = this.dialog.open(ConfirmDialogComponent, {
      disableClose: true,
      width: '300px',
      data: {
        title,
        content,
        sureText: '继续'
      }
    });
    confirmRef.afterClosed().subscribe(result => {
      if (result) {
        this.modifyVersionConfig(value ? value : null);
      }
      if (value) {
        this.selectComponent.hzVisible = false;
      }
    });
  }

  modifyVersionConfig(value?: number) {
    this.localLoading = true;
    const param = {
      tb_id: this.tbDetailsService.curTbId,
      version_enabled: value ? (this.versionEnabled ? 1 : 0) : !this.versionEnabled ? 1 : 0,
      version_record_days: value ? value : 7
    };
    this.tbDetailsService
      .modifyVersionConfig(param)
      .pipe(finalize(() => (this.localLoading = false)))
      .subscribe(data => {
        if (value) {
          this.versionRecordDays = value;
        } else {
          this.versionEnabled = !this.versionEnabled;
          this.versionRecordDays = this.versionEnabled ? this.versionRecordDays : 7;
        }
        if (this.versionEnabled) {
          this.getVersions();
        } else {
          this.tbVersions = [];
        }
      });
  }

  getVersions() {
    this.relationLoding = true;
    this.tbDetailsService
      .getVersionList({ tb_id: this.tbDetailsService.curTbId })
      .pipe(finalize(() => (this.relationLoding = false)))
      .subscribe(data => {
        this.tbVersions = data.tb_versions;
      });
  }

  viewData(version: VersionInfo) {
    const dialogRef = this.dialog.open(TbPreviewDialogComponent, {
      width: '874px',
      disableClose: true,
      data: {
        tbId: this.tbDetailsService.curTbId,
        version,
        tag: version.tag,
        tbName: this.tbDetailsService.tbBaseInfo.title
      }
    });
    dialogRef.afterClosed().subscribe((result: any) => {});
  }

  export(version: VersionInfo) {
    this.tbDetailsService
      .exportVersion({
        tb_id: this.tbDetailsService.curTbId,
        version: version.version,
        tb_title: this.tbDetailsService.tbBaseInfo.title,
        tag: version.tag
      })
      .pipe(finalize(() => (this.relationLoding = false)))
      .subscribe(data => {
        this.messageService.success('导出任务已启动');
      });
  }
}
