@import '~@haizhi/ui/styles/themes/light.data';

.hide-tb-details-left {
  .tb-details {
    &-left {
      transform: translateX(calc(-100% + 16px));
    }

    &-right {
      width: calc(100% - 16px);
      margin-left: 16px;

      .tb-details-left-hide-button {
        i {
          transform: rotate(0);
        }
      }
    }
  }
}

.show-tb-details-left {
  .tb-details {
    &-left {
      transform: translateX(0);
    }

    &-right {
      width: calc(100% - 284px);
      margin-left: 284px;

      .tb-details-left-hide-button {
        i {
          transform: rotate(180deg);
        }
      }
    }
  }
}

.tb-details {
  height: 100%;
  width: 100%;
  position: relative;

  &-left {
    transition: transform 0.3s;
    width: 284px;
    height: 100%;
    background-color: #f7f8fa;
    background-color: var(--mono-200);
    box-shadow: 0px 4px 8px rgba(15, 34, 67, 0.03), 0px 1px 3px rgba(15, 34, 67, 0.08), 0px 0px 1px rgba(15, 34, 67, 0.16);
    box-shadow: var(--container-c200);
    position: absolute;
    left: 0;
    top: 0;
  }

  &-right {
    transition: width 0.3s, margin-left 0.3s;
    background-color: #ffffff;
    background-color: var(--background-100);
    height: 100%;

    .tb-details-left-hide-button {
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #ffffff;
      position: absolute;
      top: 64px;
      transform: translateX(-50%);
      width: 24px;
      height: 24px;
      border-radius: 50%;
      box-sizing: border-box;
      box-shadow: $container-c200;
      border: 1px solid #fff;
      transition: border 0.3s;
      z-index: 999;

      &:hover {
        border-color: #1f71ff;

        i {
          color: #3d85ff;
        }
      }

      i {
        width: 16px;
        height: 16px;
        color: $type-700;
        transition: border 0.3s, transform 0.3s;
      }
    }
  }
}
