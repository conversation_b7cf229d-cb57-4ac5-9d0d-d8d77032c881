<div class="data-line-chart" [ngClass]="{ 'no-data-height': data.length === 0 }">
  <!-- <div class="title-filter">
    <span class="title">新增数据量变化</span>
    <hz-tabs hzTabsType="auxiliary" (hzCurIndexChange)="dateRangeChange($event)">
      <hz-tab hzTabTitle="7日"></hz-tab>
      <hz-tab hzTabTitle="30日"></hz-tab>
    </hz-tabs>
  </div> -->
  <div class="content">
    <div class="chart" [ngClass]="{ 'hide-chart': data.length === 0 }" #chartEle></div>
    <hz-empty
      class="no-data"
      hzEmptyIcon="no-result-light"
      hzEmptyTitle="暂无分区数据"
      *ngIf="data.length === 0 && !loading"
    ></hz-empty>
    <hz-loading-gif *ngIf="loading"></hz-loading-gif>
  </div>
</div>
