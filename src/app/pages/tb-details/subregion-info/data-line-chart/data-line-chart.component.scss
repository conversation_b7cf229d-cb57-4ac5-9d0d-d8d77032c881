@import '@haizhi/ui/styles/themes/light.data.scss';
.data-line-chart {
  // padding: 20px 16px 0;
  display: flex;
  flex-direction: column;
  height: 100%;
  position: relative;
  .title-filter {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    .title {
      font-weight: 600;
      font-size: 14px;
      line-height: 16px;
      color: $type-900;
    }
  }
  .content {
    flex: 1;
    .no-data {
      position: absolute;
      // transform: translate(-50%, -50%);
      top: 20px;
      left: calc(50% - 96px);
      // ::ng-deep .hz-empty-wrap {
      //   padding-bottom: 16px;
      // }
    }
  }
  .chart {
    height: 100%;
  }
  .hide-chart {
    visibility: hidden;
  }
}

.no-data-height {
  height: unset;
}
