import { AfterViewInit, Component, ElementRef, OnInit, ViewChild, OnDestroy, Input, EventEmitter, Output } from '@angular/core';
import * as echarts from 'echarts/core';
import { LineChart } from 'echarts/charts';
import { GridComponent, TooltipComponent, VisualMapComponent, DataZoomComponent } from 'echarts/components';
import { CanvasRenderer } from 'echarts/renderers';
import { from, fromEvent, Subject, Subscription } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
import { TbDetailsService } from './../../tb-details.service';

// 注册必须的组件
echarts.use([LineChart, TooltipComponent, GridComponent, CanvasRenderer, VisualMapComponent, DataZoomComponent]);
@Component({
  selector: 'data-line-chart',
  templateUrl: './data-line-chart.component.html',
  styleUrls: ['./data-line-chart.component.scss']
})
export class DataLineChartComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('chartEle', { static: false }) chartEle: ElementRef;
  destroy$ = new Subject();
  subMsgResize: Subscription;
  chart = null;
  data: Array<Array<string>> = [[]];
  @Input() set chartData(value: Array<Array<string>>) {
    this.data = value;
    if (this.chart && this.data && this.data.length > 0) {
      this.chart.clear();
      this.initChart();
    }
  }
  @Input() loading;
  dateRangeIndex = 0;
  @Output() dateChange: EventEmitter<number> = new EventEmitter();
  maxY = 0;

  constructor(private tbDetailsService: TbDetailsService) {}

  ngOnInit() {
    this.subMsgResize = this.tbDetailsService.subjectChartResize.pipe(debounceTime(500)).subscribe(() => {
      this.chartResize();
    });
  }

  ngAfterViewInit() {
    this.chart = echarts.init(this.chartEle.nativeElement);
    // 如果数据存在就绘制图表
    if (!(!this.data || this.data.length === 0)) {
      this.initChart();
    }
    fromEvent(window, 'resize')
      .pipe(debounceTime(500), takeUntil(this.destroy$))
      .subscribe(() => {
        this.chart.resize();
      });
  }

  initChart() {
    // 绘制图表
    const options = this.getOption();
    this.chart.setOption(options, true);
  }

  // 重绘图表
  chartResize() {
    this.chart.resize();
  }

  getOption() {
    const dateList = this.data.map(item => {
      return item[0];
    });
    const valueArr = [];
    const valueList = this.data.map(item => {
      valueArr.push(item[1]);
      return {
        value: Number(item[1]),
        visualMap: false
      };
    });
    this.maxY = Math.max(...valueArr);
    let dataZoomSize = 30;
    const showSize = 100;
    switch (true) {
      case this.data.length <= showSize: {
        dataZoomSize = 100;
        break;
      }
      case this.data.length > showSize: {
        dataZoomSize = Math.floor((showSize / this.data.length) * 100);
        if (dataZoomSize <= 0) {
          dataZoomSize = 10;
        }
        break;
      }
      default:
        break;
    }
    const option = {
      // Make gradient line here
      visualMap: {
        show: false,
        type: 'continuous',
        seriesIndex: 0,
        min: 0,
        max: this.maxY,
        inRange: {
          color: ['#1FAEFF', '#1F71FF', '#4200FF']
        }
      },
      tooltip: {
        trigger: 'axis',
        padding: [8, 16],
        axisPointer: {
          type: 'line',
          lineStyle: {
            color: 'rgba(43, 121, 255, 0.24)',
            type: 'solid'
          }
        },
        textStyle: {
          fontSize: 12,
          color: 'rgba(21, 22, 24, 0.48)',
          lineHeight: 20
        },
        formatter(params) {
          const item = params[0];
          return `
            <div>
                <p>${item.name}</p>
                <p><span style="margin-right: 8px;">存储量</span><span style="color: rgba(21, 22, 24, 0.92); font-weight: 600">${item.value.toLocaleString()}</span></p>
            </div>
          `;
        },
        extraCssText:
          'border-radius: 8px; box-shadow: 0px 16px 32px -6px rgba(15, 34, 67, 0.18), 0px 3px 8px -2px rgba(15, 34, 67, 0.16), 0px 0px 1px rgba(15, 34, 67, 0.16)'
      },
      toolbox: {
        feature: {
          dataZoom: {
            yAxisIndex: 'none'
          },
          restore: {},
          saveAsImage: {}
        }
      },
      dataZoom: [
        {
          type: 'inside',
          start: 0,
          end: dataZoomSize
        },
        {
          height: 12,
          bottom: 8,
          start: 0,
          end: dataZoomSize,
          textStyle: {
            color: '#00000000' // 隐藏滚动条两端 字
          }
        }
      ],
      grid: {
        left: 38,
        right: 8,
        bottom: 32,
        containLabel: true
      },
      xAxis: {
        data: dateList,
        axisLine: {
          lineStyle: {
            color: 'rgba(15, 34, 67, 0.11)' // 坐标线的颜色
          }
        },
        axisLabel: {
          textStyle: {
            color: 'rgba(21, 22, 24, 0.48)' // 坐标值颜色
          }
        },
        axisTick: {
          show: false // 隐藏轴刻度线
        }
      },
      yAxis: {
        type: 'value',
        name: '存储量(条)',
        nameTextStyle: {
          color: 'rgba(21, 22, 24, 0.48)',
          padding: this.maxY >= 1000 ? [0, 0, 0, -36] : 0
        },
        nameGap: 20,
        axisLine: {
          lineStyle: {
            color: 'rgba(15, 34, 67, 0.11)' // 坐标线的颜色
          }
        },
        axisLabel: {
          textStyle: {
            color: 'rgba(21, 22, 24, 0.48)' // 坐标值颜色
          }
        },
        splitLine: {
          // 改变轴线颜色
          lineStyle: {
            color: 'rgba(15, 34, 67, 0.11)'
          }
        }
      },
      series: {
        data: valueList,
        type: 'line',
        symbolSize: 8,
        smooth: true,
        symbol: 'circle',
        itemStyle: {
          normal: {
            color: '#fff', // 折线点的颜色
            borderWidth: 3,
            borderColor: 'rgba(31, 113, 255, 0.64)',
            lineStyle: {
              width: 4 // 设置线条粗细
            }
          }
        },
        emphasis: {
          // 鼠标hover上去的时候，拐点的颜色和样式
          itemStyle: {
            color: '#1F71FF', // 颜色
            borderColor: 'rgba(255, 255, 255, 0.64)'
          }
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(59, 22, 255, 0.32)' },
            { offset: 1, color: 'rgba(31, 113, 255, 0.01)' }
          ])
        }
      }
    };
    return option;
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.chart.dispose();
    this.subMsgResize.unsubscribe();
  }

  dateRangeChange(tabIndex: number) {
    if (tabIndex === this.dateRangeIndex) {
      return false;
    }
    this.dateRangeIndex = tabIndex;
    this.dateChange.emit(tabIndex);
  }
}
