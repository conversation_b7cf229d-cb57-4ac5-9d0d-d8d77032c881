@import '~@haizhi/ui/styles/themes/light.data';
.update-record {
  width: 100%;
  //   height: calc(100% - 108px);
  position: relative;
  font-size: 12px;
  // margin: 16px 0;
  .tb-head {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left-count {
      line-height: 20px;
      color: $type-700;
      span {
        color: $primary-900;
      }
    }
    .right {
      display: flex;
      .right-content {
        display: flex;
        align-items: center;
        margin-right: 32px;
        span {
          font-weight: 600;
          line-height: 20px;
          color: $type-600;
          margin-right: 16px;
        }
      }
      div:last-child {
        margin-right: 0;
      }
    }
  }
  .tab-container {
    width: 100%;
    padding: 8px 16px;
    background: #ffffff;
    box-shadow: $container-c100;
    border-radius: 8px;
    margin-top: 8px;
    ::ng-deep .ant-table-scroll {
      overflow: hidden;
    }
    i {
      cursor: pointer;
      margin-left: 8px;
    }
    .sort-icon {
      display: none;
      transition: transform 0.4s;
    }
    .sort-asc {
      transform: rotate(-180deg);
    }
    .title {
      font-weight: 600;
      line-height: 16px;
      color: $type-600;
    }
    .title-first {
      margin-left: 6px;
    }
    .des {
      line-height: 16px;
      color: $type-800;
    }
    .subregion-list-head {
      &:hover {
        .sort-icon {
          display: inline-block;
          transition: transform 0.4s;
        }
      }
    }
    .no-data {
      text-align: center;
      height: 32px;
      line-height: 32px;
      color: rgba(21, 22, 24, 0.36);
      margin-top: 16px;
    }
  }
}

:host ::ng-deep .hz-range-date-outlet {
  span {
    line-height: 20px;
    color: $type-800;
  }
}
.page-position {
  display: flex;
  flex-direction: row-reverse;
}
