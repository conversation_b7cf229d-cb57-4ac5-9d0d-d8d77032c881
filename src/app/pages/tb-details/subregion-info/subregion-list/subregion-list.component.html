<div class="update-record">
  <div class="tab-container" *ngIf="!loading">
    <nz-table
      [nzData]="partitionList"
      [nzShowPagination]="false"
      [nzScroll]="{ x: '800px', y: 'calc(100vh - 530px)' }"
      [nzWidthConfig]="['30%', '25%']"
    >
      <thead>
        <tr>
          <th nzAlign="left">分区名称</th>
          <th nzAlign="left" class="subregion-list-head">
            存储量
            <i
              hz-icon
              hzName="desc-sort-arrow"
              hzSize="16px"
              (click)="resultReverse()"
              [ngClass]="{ 'sort-asc': !sortStatus }"
              class="sort-icon"
              hz-action-icon
            ></i>
          </th>
          <th nzAlign="left" style="padding-left: 25px">创建时间</th>
          <th nzAlign="left">更新时间</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of filterData">
          <td>{{ item.name }}</td>
          <td>{{ item.data_count }}</td>
          <td>{{ item.ctime }}</td>
          <td>{{ item.utime }}</td>
        </tr>
      </tbody>
    </nz-table>
    <!-- <hz-empty
      hzEmptyIcon="no-result-light"
      hzEmptyTitle="暂无分区数据"
      *ngIf="!loading && partitionList.length === 0 && !isMiniScreen"
    ></hz-empty> -->
    <div class="no-data" *ngIf="!loading && partitionList.length === 0">暂无分区数据</div>
  </div>
</div>
<div class="page-position" *ngIf="partitionList.length > 0">
  <hz-pagination
    *ngIf="!loading"
    class="fr mt-8"
    [(hzPageIndex)]="pageIndex"
    [hzTotal]="partitionList.length"
    [hzPageSize]="pageSize"
    [hzHideOnSinglePage]="true"
    (hzPageIndexChange)="getFilterData()"
    (hzPageSizeChange)="getFilterData()"
  ></hz-pagination>
</div>
