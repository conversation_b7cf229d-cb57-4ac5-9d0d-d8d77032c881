import { Component, OnInit, OnDestroy, Input } from '@angular/core';
import { TbDetailsService } from './../../tb-details.service';
import { partitionInfo } from '../subregion-info.model';
import { Subject, Subscription, fromEvent } from 'rxjs';
import { debounceTime, takeUntil } from 'rxjs/operators';
@Component({
  selector: 'subregion-list',
  templateUrl: './subregion-list.component.html',
  styleUrls: ['./subregion-list.component.scss']
})
export class SubregionListComponent implements OnInit, OnDestroy {
  sortStatus = true; // 排序默认降序
  destroy$ = new Subject<void>();
  pageIndex = 1;
  pageSize = 9;
  partitionList: Array<partitionInfo> = [];
  filterData: Array<partitionInfo> = [];
  subMsgResize: Subscription;
  //   // 左侧信息栏是否展开
  //   isShowLeft: boolean = true;
  //   // 宽度偏移
  //   tableWidth: string = '240px';
  @Input() set partitionInfoList(value: Array<partitionInfo>) {
    this.partitionList = value;
    this.getFilterData();
  }
  @Input() loading;
  constructor(private tbDetailsService: TbDetailsService) {}

  /**
   * 排序回调
   */
  resultReverse() {
    this.sortStatus = !this.sortStatus;
    this.partitionList.sort((a: partitionInfo, b: partitionInfo) => {
      return !this.sortStatus ? a.data_count - b.data_count : b.data_count - a.data_count;
    });
    this.pageIndex = 1;
    this.getFilterData();
  }

  // 翻页
  getFilterData() {
    this.filterData = this.partitionList.slice((this.pageIndex - 1) * this.pageSize, this.pageIndex * this.pageSize);
  }

  ngOnInit(): void {
    // this.subMsgResize = this.tbDetailsService.subjectChartResize.pipe(debounceTime(500)).subscribe((data) => {
    //     this.isShowLeft = data;
    //     this.tableResize();
    // });
    // setTimeout(() => {
    //     this.tableResize();
    // }, 100);
  }

  //   ngAfterViewInit() {
  //     fromEvent(window, 'resize')
  //       .pipe(debounceTime(500), takeUntil(this.destroy$))
  //       .subscribe(() => {
  //         this.tableResize();
  //       });
  //   }

  // 根据左侧展开收起，适配 13寸小屏幕
  tableResize() {}

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
    // this.subMsgResize.unsubscribe();
  }
}
