/**
 * 公共接口
 */
export interface Requestparams {
  tb_id: string;
  tb_type?: string;
  type?: number;
  start_time?: string;
  end_time?: string;
  page_no: number;
  page_size: number;
  sort_type: string;
  sort_field: string;
}
export interface Types {
  id: number;
  value: string;
}
// -- test 测试数据
export interface TableInfo {
  tb_id: string;
  id: number;
  operator: string;
  operator_name: string;
  operator_id?: any;
  type: number;
  ctime: string;
  details?: any;
  ent_id: string;
  op_type: number;
  tb_update_log_dto: Detail[];
}

export interface Detail {
  op_type: number;
  details: string;
}

/**
 * 分区信息
 */
export interface partitionInfo {
  name: string; // 分区名字
  data_count: number; // 分区数据量
  ctime: string; // 创建时间
}
