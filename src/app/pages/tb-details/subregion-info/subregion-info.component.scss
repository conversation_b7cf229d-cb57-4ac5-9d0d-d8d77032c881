.asset-statistics-wrap {
  height: 100%;
  .asset-statistics {
    display: flex;
    flex-direction: column;
    height: 100%;
    .data-chart {
      // flex: 1;
      // padding: 16px 0;
      display: flex;
      min-height: 0;
      height: 292px;
      background: #ffffff;
      box-shadow: 0px 1px 2px rgb(4 8 16 / 3%), 0px 2px 4px rgb(4 8 16 / 3%), 0px 8px 64px rgb(4 8 16 / 10%);
      border-radius: 8px;
      data-line-chart {
        flex: 1;
        // margin-right: 40px;
      }
      data-bar-chart {
        flex: 1;
      }
    }
    .field-info {
      line-height: 32px;
      height: 32px;
      display: flex;
      margin: 8px 0;
      background: #ffffff;
      box-shadow: 0px 1px 2px #04081008, 0px 2px 4px #04081008, 0px 8px 64px #0408100a;
      border-radius: 8px;
      font-size: 13px;
      cursor: default;
      .info-text {
        margin-left: 58px;
        color: rgba(21, 22, 24, 0.36);
      }
      .info-value {
        margin-left: 8px;
      }
      .title {
        font-weight: 600;
        color: rgba(21, 22, 24, 0.36);
        margin-left: 32px;
      }
    }
    // .subregion-list {
    //   height: calc(100% - 312px);
    // }
  }
}
@media screen and (max-height: 800px) {
  .asset-statistics-wrap {
    overflow: auto;
  }
  .asset-statistics {
    // overflow: auto;
    height: auto;
    .data-chart {
      min-height: 292px;
    }
  }
}
