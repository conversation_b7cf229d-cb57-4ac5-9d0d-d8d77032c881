import { Component, Input, OnInit, ViewChild } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { partitionInfo } from './subregion-info.model';
import { TbDetailsService } from './../tb-details.service';
import { TbDataPreviewService } from './../tb-data-preview/tb-data-preview.service';

@Component({
  selector: 'hz-subregion-info',
  templateUrl: './subregion-info.component.html',
  styleUrls: ['./subregion-info.component.scss']
})
export class SubregionInfo implements OnInit {
  // 数据 loading
  lineChartLoading = false;
  lineChartData: Array<Array<string>> = [];
  // 用于显示分区列表
  partitionInfoList: Array<partitionInfo> = [];
  subregionConfig: any = {
    field_name: '',
    unit: ''
  };
  unitMap: any = {
    day: '日',
    month: '月',
    year: '年'
  };
  constructor(private tbDetailsService: TbDetailsService, private tbDataPreviewService: TbDataPreviewService) {}

  ngOnInit() {
    const tbType = this.tbDetailsService.curTbType;
    const tbBaseInfo = this.tbDetailsService.tbBaseInfo;
    const originType = tbBaseInfo.origin_type;
    const tbId = this.tbDetailsService.curTbId;
    // console.log(tbType, tbBaseInfo, tbId);
    this.getLineChartData();
    this.getTbPartitionList();
  }

  // 获取数据量变化
  getLineChartData() {
    this.lineChartData = [
      ['year=null/month=null/day=null', '0'] // 默认数据，初始化时需要赋值，否则 图表更新数据无法重绘。
    ];
  }

  lineDateChange(tabIndex: number) {
    // console.log('粒度切换', tabIndex);
  }

  // 获取分区列表
  getTbPartitionList() {
    this.lineChartLoading = true;
    const params: {
      tb_id: string;
      page_no?: number; // 页码
      page_size?: number; // 每页返回数据量
      sort_key?: string; // 排序key
      sort_direction?: 'asc' | 'desc'; //asc 升序or desc 降序
    } = { tb_id: this.tbDetailsService.curTbId };

    this.tbDataPreviewService
      .getTbPartitionList(params)
      .pipe(finalize(() => (this.lineChartLoading = false)))
      .subscribe(res => {
        if (res.pt_list) {
          this.lineChartData = this.setChartData(res.pt_list);
          this.partitionInfoList = res.pt_list;
        }
        this.subregionConfig = res.pt_config;
        this.subregionConfig.unit = this.unitMap[res.pt_config.unit];
      });
  }

  // 格式化图表数据
  setChartData(list: any) {
    if (!list) {
      console.error('返回分区数据有误，请与管理员联系');
      return [];
    }
    let templist = [];
    list.forEach((item: any) => {
      templist.push([item.name, item.data_count]);
    });
    return templist.reverse();
  }
}
