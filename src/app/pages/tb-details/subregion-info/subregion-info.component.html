<div class="asset-statistics-wrap">
  <div class="asset-statistics">
    <div class="field-info">
      <span class="title">分区配置</span>
      <span class="info-text">分区字段：</span>
      <span class="info-value">{{ subregionConfig.field_name }}</span>
      <span class="info-text">分区粒度：</span>
      <span class="info-value">{{ subregionConfig.unit }}</span>
    </div>
    <div class="data-chart">
      <data-line-chart
        [loading]="lineChartLoading"
        [chartData]="lineChartData"
        (dateChange)="lineDateChange($event)"
        #dataLineChartComponent
      ></data-line-chart>
    </div>
    <div class="subregion-list">
      <subregion-list
        [loading]="lineChartLoading"
        [partitionInfoList]="partitionInfoList"
      ></subregion-list>
    </div>
  </div>
</div>
<hz-loading-gif *ngIf="lineChartLoading"></hz-loading-gif>
