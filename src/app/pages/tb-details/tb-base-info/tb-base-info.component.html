<div class="table-base-info-box" *ngIf="tableBaseInfo">
  <div class="table-title mb16">
    <span class="table-name-text">基本信息</span>
  </div>
  <div class="table-item">
    <div class="item-title">基础元数据</div>
    <div class="item-content">
      <div class="title mr8">表名称</div>
      <div class="desc">
        {{ tableBaseInfo.title }}
        <hz-tag
          class="ml8"
          hzType="solid"
          hzSize="tiny"
          [hzTextColor]="tbTypeTextColor[tableBaseInfo.tb_storage_flag]"
          [ngStyle]="{ background: tbTypeBgColor[tableBaseInfo.tb_storage_flag] }"
          [hzReadonly]="true"
        >
          {{ tbType[tableBaseInfo.tb_storage_flag] }}
        </hz-tag>
      </div>
    </div>
    <div class="item-content">
      <div class="title mr8">原表名称</div>
      <div class="desc mr8">{{ tableBaseInfo.tb_origin_name }}</div>
    </div>
    <div class="item-content">
      <div class="title mr8">表描述</div>
      <div class="desc mr8">{{ tableBaseInfoComment }}</div>
    </div>
    <div class="item-content" *ngIf="tableBaseInfo.tb_code">
      <div class="title mr8">表编码</div>
      <div class="desc mr8">{{ tableBaseInfo.tb_code }}</div>
    </div>
    <div class="item-content" *ngIf="tableBaseInfo.ctime">
      <div class="title mr8">创建时间</div>
      <div class="desc mr8">{{ tableBaseInfo.ctime }}</div>
    </div>
  </div>
  <div class="split-line"></div>
  <div class="table-item">
    <div class="item-title">业务元数据</div>
    <div class="item-content">
      <div class="title mr8">所属数据层</div>
      <div class="desc mr8">{{ tableTypeData[tableBaseInfo.type] }}</div>
    </div>
    <div
      class="item-content"
      *ngIf="
        tableBaseInfo.type !== 'RAW' &&
        tableBaseInfo.type !== 'PROCESS' &&
        tableBaseInfo.type !== 'RESULT'
      "
    >
      <div class="title mr8">加入专题目录</div>
      <div class="desc mr8">
        <ng-container *ngIf="tableBaseInfo.asset_folder_path?.length > 0; else defaultTpl">
          <hz-breadcrumb hzSize="medium">
            <hz-breadcrumb-item *ngFor="let item of tableBaseInfo.asset_folder_path">
              <span class="midway-path">
                {{ item }}
              </span>
            </hz-breadcrumb-item>
          </hz-breadcrumb>
        </ng-container>
        <ng-template #defaultTpl>--</ng-template>
      </div>
    </div>
    <!-- 暂不显示 -->
    <!-- <div class="item-content">
      <div class="title mr8">授权产品</div>
      <div class="desc mr8">暂无数据待展示</div>
    </div> -->
  </div>
  <div class="split-line"></div>
  <div class="table-item">
    <div class="item-title">技术元数据</div>
    <div class="item-content">
      <div class="title mr8">表ID</div>
      <div class="desc mr8">{{ tableBaseInfo.tb_id }}</div>
    </div>
    <div class="item-content" *ngIf="tableBaseInfo.tb_storage_flag !== 2">
      <div class="title mr8">数据量</div>
      <div class="desc mr8">{{ tableBaseInfo.data_count }}</div>
    </div>
    <div class="item-content" *ngIf="storageType[tableBaseInfo.storage_type]">
      <div class="title mr8">存储类型</div>
      <div class="desc mr8">{{ storageType[tableBaseInfo.storage_type] }}</div>
    </div>
    <div *ngIf="tableBaseInfo.storage_type !== 1" class="partition mt4">
      <div class="partition-item">
        <div class="partition-item-title">最早分区</div>
        <div class="partition-item-desc">
          {{ tableBaseInfo.first_partition ? tableBaseInfo.first_partition : '--' }}
        </div>
      </div>
      <div class="partition-item mt4">
        <div class="partition-item-title">最晚分区</div>
        <div class="partition-item-desc">
          {{ tableBaseInfo.latest_partition ? tableBaseInfo.latest_partition : '--' }}
        </div>
      </div>
    </div>
    <div
      class="item-content"
      *ngIf="tableBaseInfo.origin_type === 'streamingtb'; else updateModeTpl"
    >
      <div class="title mr8">更新方式</div>
      <div class="desc mr8">
        {{
          tableBaseInfo.tb_type === 0 && tableBaseInfo.schedule_config?.mode === 4
            ? '实时更新'
            : '自动更新'
        }}
      </div>
    </div>
    <ng-template #updateModeTpl>
      <div
        class="item-content"
        *ngIf="
          tableBaseInfo.schedule_config &&
          tableBaseInfo.schedule_config.mode !== null &&
          tableBaseInfo.schedule_config.mode !== undefined
        "
      >
        <div class="title mr8">更新方式</div>
        <div class="desc mr8">{{ updateMode[tableBaseInfo.schedule_config.mode] }}</div>
      </div>
    </ng-template>
    <div
      class="item-content"
      *ngIf="
        tableBaseInfo.schedule_config &&
        tableBaseInfo.schedule_config.sync_config &&
        (tableBaseInfo.schedule_config.mode === 1 || tableBaseInfo.schedule_config.mode === 0)
      "
    >
      <div class="title mr8">更新周期</div>
      <div class="desc mr8">{{ tableBaseInfo.schedule_config.sync_config }}</div>
    </div>
    <div
      class="item-content"
      *ngIf="tableBaseInfo.origin_type === 'streamingtb'; else updateTypeTpl"
    >
      <div class="title mr8">更新类型</div>
      <div class="desc mr8">增量</div>
    </div>
    <ng-template #updateTypeTpl>
      <div
        class="item-content"
        *ngIf="tableBaseInfo.update_type !== null && tableBaseInfo.update_type !== undefined"
      >
        <div class="title mr8">更新类型</div>
        <div class="desc mr8">{{ updateType[tableBaseInfo.update_type] }}</div>
      </div>
    </ng-template>
    <div
      class="item-content"
      *ngIf="tableBaseInfo.origin_type !== 'streamingkafka' && tableBaseInfo.tb_storage_flag === 2"
    >
      <div class="title mr8">更新状态</div>
      <div class="desc mr8">
        <hz-tag hzSize="tiny" hzTextColor="#1F71FF">正在更新</hz-tag>
      </div>
    </div>
    <div
      class="item-content"
      *ngIf="tableBaseInfo.origin_type !== 'streamingkafka' && tableBaseInfo.tb_storage_flag !== 2"
    >
      <div class="title mr8">更新状态</div>
      <div class="desc mr8">
        <hz-tag hzSize="tiny" [hzTextColor]="updateStatus[tableBaseInfo.update_status]?.color">
          {{ updateStatus[tableBaseInfo.update_status]?.text }}
        </hz-tag>
      </div>
    </div>
    <div class="item-content">
      <div class="title mr8">
        {{ tableBaseInfo.tb_type === 0 ? '更新时间' : '启动时间' }}
      </div>
      <div class="desc mr8">{{ tableBaseInfo.last_update_time || '--' }}</div>
    </div>
  </div>
</div>
