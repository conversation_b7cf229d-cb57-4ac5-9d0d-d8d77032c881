import { Component, Input, OnInit } from '@angular/core';
import {
  TableBaseInfo,
  TableBaseInfoTypeKeyMap,
  TableBaseInfoUpdateModeKeyMap,
  TableBaseInfoUpdateTypeKeyMap,
  TableBaseInfostorageTypeKeyMap,
  TableBaseInfoUpdateStatusKeyMap,
  TableBaseInfoTbTypeKeyMap
} from '../tb-details.model';
import { TbDetailsService } from '../tb-details.service';
@Component({
  selector: 'hz-tb-base-info',
  templateUrl: './tb-base-info.component.html',
  styleUrls: ['./tb-base-info.component.scss']
})
export class TbBaseInfoComponent implements OnInit {
  isShowLastUsedTime = false;
  // 表类型 0数据表 1流式表
  tbType: TableBaseInfoTbTypeKeyMap = {
    0: '普通表',
    1: '在线表',
    2: '流式表'
  };
  tbTypeTextColor = {
    0: '#0099EB',
    1: '#FF9431',
    2: '#9646FF'
  };
  tbTypeBgColor = {
    0: 'rgba(0, 153, 235, 0.12)',
    1: 'rgba(255, 148, 49, 0.12)',
    2: 'rgba(150, 70, 255, 0.12)'
  };
  // 存储类型
  storageType: TableBaseInfostorageTypeKeyMap = {
    1: '非分区',
    2: '分区表',
    3: '分区表'
  };
  // 0 增量、1 全量
  updateType: TableBaseInfoUpdateTypeKeyMap = {
    0: '增量',
    1: '全量'
  };

  tableTypeData: TableBaseInfoTypeKeyMap = {
    RAW: '原始库',
    RESULT: '清洗结果库',
    STANDARD: '标准库',
    BENCH: '专题标准库',
    TOPIC: '主题库',
    ELEMENT: '实体库',
    RELATION: '关系库',
    TAG: '标签库',
    STREAMINGTB: '流式库',
    TOPIC_NEW: '部标专题库',
    RESOURCE: '资源库',
    PROCESS: '开发结果库'
  };

  // 更新方式
  updateMode: TableBaseInfoUpdateModeKeyMap = {
    0: '自定义更新',
    1: '定时更新',
    2: '自动更新',
    3: '暂停更新',
    4: '实时更新'
  };

  // 更新状态
  updateStatus: TableBaseInfoUpdateStatusKeyMap = {
    0: {
      color: '#FF9431',
      text: '新建'
    },
    1: {
      color: '#239545',
      text: '更新成功'
    },
    2: {
      color: '#FF5266',
      text: '更新失败'
    },
    3: {
      color: '#1F71FF',
      text: '正在更新'
    },
    6: {
      color: '#1F71FF',
      text: '正在更新'
    },
    7: {
      color: 'rgba(15, 34, 67, 0.36)',
      text: '暂未映射'
    }
  };

  // 是否展示创建时间
  tableBaseInfoComment = '';
  @Input() tableBaseInfo: TableBaseInfo;

  constructor() {}

  ngOnInit() {
    if (!!this.tableBaseInfo.comment === false) {
      this.tableBaseInfoComment = '暂无描述';
    } else if (typeof this.tableBaseInfo.comment === 'string') {
      this.tableBaseInfoComment = this.tableBaseInfo.comment.trim() || '暂无描述';
    }
    this.isShowLastUsedTime = ['RAW', 'RESULT'].indexOf(this.tableBaseInfo.type) < 0;
    localStorage.setItem('tableType', JSON.stringify(this.tableBaseInfo.tb_storage_flag));
  }
}
