@import '~@haizhi/ui/styles/themes/light.data';

.table-base-info-box {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  padding: 16px 24px;
  overflow: auto;
  .table-title {
    font-size: 16px;
    color: $type-900;
  }
  .table-item {
    .item-title {
      color: $type-800;
      font-weight: bold;
    }
    .item-content {
      display: flex;
      margin-top: 8px;
      .title {
        min-width: 72px;
        font-weight: 600;
        line-height: 20px;
        color: $type-600;
        white-space: nowrap;
      }
      .desc {
        color: $type-900;
        line-height: 20px;
        word-break: break-word;
        .midway-path {
          color: $type-900;
          font-size: 12px;
        }
      }
    }
    .item-content-reset {
      margin-top: 8px;
      .title-reset {
        font-weight: 600;
        line-height: 20px;
        color: $type-600;
      }
    }
    .partition {
      padding: 4px 8px;
      background: $mono-a100;
      border-radius: 8px;
      .partition-item {
        display: flex;
        .partition-item-title {
          min-width: 48px;
          white-space: nowrap;
          line-height: 20px;
          color: $type-600;
          margin-right: 24px;
        }
        .partition-item-desc {
          color: $type-800;
          word-break: break-word;
        }
      }
    }
  }
  .split-line {
    width: 100%;
    height: 1px;
    background: $mono-a500;
    opacity: 0.6;
    margin: 16px 0;
  }
}
::ng-deep .hz-tag-solid.hz-tag-tiny {
  border-radius: 16px !important;
}
