import { Component, ElementRef, Input, OnInit, ViewChild, AfterViewInit, OnDestroy } from '@angular/core';
import G6 from '@antv/g6';
import BloodTooltip from './blood-tooltip';
import { from, fromEvent, Subject } from 'rxjs';
import { debounceTime, finalize, takeUntil } from 'rxjs/operators';
import { BloodGraphService } from '../blood-graph.service';
import { TbDetailsService } from './../../tb-details.service';
import { TableBaseInfo } from './../../tb-details.model';
import {
  EdgeMapConfig,
  StatusMapConfig,
  NodeTypeMapConfig,
  TableTypeMapConfig,
  BloodDataConfig,
  FieldTypeMapConfig,
  NodeConfig,
  GetBloodDataParams
} from '../blood-graph.model';
@Component({
  selector: 'lineage-graph',
  templateUrl: './lineage-graph.component.html',
  styleUrls: ['./lineage-graph.component.scss']
})
export class LineageGraphComponent implements OnInit, AfterViewInit, OnDestroy {
  constructor(private tbDetailsService: TbDetailsService, private bloodGraphService: BloodGraphService) {}
  @ViewChild('lineageGraph', { static: false }) lineageGraph: ElementRef;
  destroy$ = new Subject();
  @Input() type: 'table' | 'field' = 'table'; // table: 表级血缘 field: 字段血缘
  data: BloodDataConfig = {
    nodes: [],
    edges: []
  };
  @Input() set bloodData(value: BloodDataConfig) {
    if (this.graph) {
      this.graph.clear();
      this.graph.destroy();
    }
    this.data = value;
    if (this.defaultConfig && value.nodes && value.nodes.length > 0) {
      this.nodeIsRequest = {};
      this.initGraph(this.data);
    }
  }
  @Input() statisticData: {
    [key: string]: {
      title: string;
      count: number;
    };
  };
  tbBaseInfo: TableBaseInfo;
  graph = null;
  defaultConfig = null;
  tooltip = null;
  edgeMap: { [index: string]: EdgeMapConfig } = {};
  statusMap: { [index: number]: StatusMapConfig } = {
    1: {
      color: '#239545',
      text: '成功'
    },
    3: {
      color: '#1F71FF',
      text: '更新中'
    },
    2: {
      color: '#FF5266',
      text: '更新失败'
    },
    0: {
      color: '#1F71FF',
      text: '更新中'
    },
    6: {
      color: '#1F71FF',
      text: '更新中'
    },
    7: {
      color: 'rgba(15, 34, 67, 0.36)',
      text: '暂未映射'
    }
  };
  nodeTypeMap: { [index: number]: NodeTypeMapConfig } = {};
  tableTypeMap: { [index: string]: TableTypeMapConfig } = {
    RAW: {
      color: '#1F71FF',
      text: '原始库'
    },
    RESULT: {
      color: '#1F71FF',
      text: '清洗结果库'
    },
    STANDARD: {
      color: '#1F71FF',
      text: '标准库'
    },
    BENCH: {
      color: '#1F71FF',
      text: '专题标准库'
    },
    TOPIC: {
      color: '#1F71FF',
      text: '主题库'
    },
    ELEMENT: {
      color: '#1F71FF',
      text: '实体库'
    },
    RELATION: {
      color: '#1F71FF',
      text: '关系库'
    },
    TAG: {
      color: '#1F71FF',
      text: '标签库'
    },
    STREAMINGTB: {
      color: '#1F71FF',
      text: '流式库'
    },
    TOPIC_NEW: {
      color: '#1F71FF',
      text: '部标专题库'
    },
    PROCESS: {
      color: '#1F71FF',
      text: '开发结果库'
    }
  };
  colors = {
    line: '#CCCED4'
  };
  imgUrl = 'assets/images/blood/';
  // 字段类型 字段类型: INT(0)|DOUBLE(1)|STRING(2)|DATETIME(3)
  fieldTypeMap: { [index: number]: FieldTypeMapConfig } = {
    0: {
      img: this.imgUrl + 'type-number.svg',
      imgWhite: this.imgUrl + 'select/type-number.svg',
      0: this.imgUrl + 'field-type/0/type-number.svg',
      1: this.imgUrl + 'field-type/1/type-number.svg',
      2: this.imgUrl + 'field-type/2/type-number.svg'
    },
    1: {
      img: this.imgUrl + 'type-number.svg',
      imgWhite: this.imgUrl + 'select/type-number.svg',
      0: this.imgUrl + 'field-type/0/type-number.svg',
      1: this.imgUrl + 'field-type/1/type-number.svg',
      2: this.imgUrl + 'field-type/2/type-number.svg'
    },
    2: {
      img: this.imgUrl + 'type-string.svg',
      imgWhite: this.imgUrl + 'select/type-string.svg',
      0: this.imgUrl + 'field-type/0/type-string.svg',
      1: this.imgUrl + 'field-type/1/type-string.svg',
      2: this.imgUrl + 'field-type/2/type-string.svg'
    },
    3: {
      img: this.imgUrl + 'type-date.svg',
      imgWhite: this.imgUrl + 'select/type-date.svg',
      0: this.imgUrl + 'field-type/0/type-date.svg',
      1: this.imgUrl + 'field-type/1/type-date.svg',
      2: this.imgUrl + 'field-type/2/type-date.svg'
    },
    4: {
      img: this.imgUrl + 'type-blob.svg',
      imgWhite: this.imgUrl + 'select/type-blob.svg',
      0: this.imgUrl + 'field-type/0/type-blob.svg',
      1: this.imgUrl + 'field-type/1/type-blob.svg',
      2: this.imgUrl + 'field-type/2/type-blob.svg'
    }
  };
  connectedNodeIds: string[] = []; // 当前节点的上下游节点

  // 记录节点是否请求过数据
  nodeIsRequest: { [index: string]: boolean } = {};

  loading = false;

  ngOnInit(): void {
    this.tbBaseInfo = this.tbDetailsService.tbBaseInfo;
    this.edgeMap = this.bloodGraphService.edgeMap;
    this.nodeTypeMap = this.bloodGraphService.nodeTypeMap;
    if (this.type === 'table') {
      this.registerTableNode();
    } else {
      this.registerFieldNode();
    }
    this.registerEdge();
  }

  ngAfterViewInit(): void {
    const ele = this.lineageGraph.nativeElement;
    fromEvent(window, 'resize')
      .pipe(debounceTime(500), takeUntil(this.destroy$))
      .subscribe(() => {
        if (!this.graph || this.graph.get('destroyed')) {
          return;
        }
        if (!ele.scrollWidth || !ele.scrollHeight) {
          return;
        }
        this.graph.changeSize(ele.scrollWidth, ele.scrollHeight);
      });
    const $height = ele.clientHeight;
    const $width = ele.clientWidth;
    this.defaultConfig = {
      width: $width, // Number，必须，图的宽度
      height: $height, // Number，必须，图的高度
      fitView: true,
      // animate: true,
      animateCfg: {
        duration: 300 // Number，一次动画的时长
      },
      fitCenter: true,
      maxZoom: 1,
      minZoom: 0.5,
      // renderer: 'svg', // 自定义节点图形Shape为dom时需指定图渲染方式renderer为'svg'
      modes: {
        default: ['zoom-canvas', 'drag-canvas', 'activate-relations']
      },
      defaultNode: {
        type: this.type === 'table' ? 'table-node' : 'field-node'
      },
      defaultEdge: {
        type: 'kinship-edge'
      },
      layout: {
        type: 'dagre', // 层次布局
        rankdir: 'LR',
        nodesep: 16,
        ranksepFunc: d => {
          if (d.node_position === 1) {
            return 78;
          }
          return 100;
        }
      }
    };
    if (!this.graph && this.data.nodes && this.data.nodes.length > 0) {
      this.initGraph(this.data);
    }
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    if (this.graph) {
      this.graph.clear();
      this.graph.destroy();
    }
  }

  // 注册表节点
  registerTableNode() {
    const $this = this;
    G6.registerNode(
      'table-node',
      {
        draw: (cfg: any, group) => {
          const { id, tb_name, node_position, can_extract, collapsed, upperCollapsed, tb_storage_flag } = cfg;
          const select = node_position === 1; // 上游(0)|自身(1)|下游(2)
          let background = select ? $this.nodeTypeMap[tb_storage_flag].cbg : '#fff';
          const size = select ? 14 : 12;
          let color = select ? '#fff' : 'rgba(21, 22, 24, 0.72)';

          // 判断是否为更新失败状态，需要特殊颜色处理（但不包括当前表）
          const isUpdateFailed = cfg.update_status === 2 && node_position !== 1;

          // 添加矩形
          const num = tb_name.length > 10 ? 10 : tb_name.length;
          let width = select ? num * 14 : num * 12;
          width = select ? 154 : 96;

          // 根据更新状态决定颜色方案
          let strokeColor = select ? $this.nodeTypeMap[tb_storage_flag].borderShdow : $this.colors.line;
          if (isUpdateFailed) {
            strokeColor = 'rgba(255, 82, 102, 1)'; // 边框颜色
            background = 'rgba(255, 82, 102, 0.08)'; // 背景填充颜色
            color = 'rgba(255, 82, 102, 1)'; // 文字颜色
          }


          const rectConfig = {
            width: width + 48,
            height: select ? 48 : 28,
            lineWidth: isUpdateFailed ? 2 : 1, // 更新失败时边框加粗
            fill: background,
            radius: 6,
            stroke: strokeColor,
            cursor: 'pointer'
          };
          const nodeOrigin = {
            x: -rectConfig.width / 2,
            y: -rectConfig.height / 2
          };
          // 添加描边
          group.addShape('rect', {
            attrs: {
              x: nodeOrigin.x - 2,
              y: nodeOrigin.y - 2,
              width: width + 48 + 4,
              height: (select ? 48 : 28) + 4,
              lineWidth: 4,
              radius: 6
            },
            name: 'table-rect-stroke'
          });
          // 添加矩形
          const rect = group.addShape('rect', {
            attrs: {
              x: nodeOrigin.x,
              y: nodeOrigin.y,
              ...rectConfig
            },
            name: 'table-rect'
          });
          // 添加文本
          group.addShape('text', {
            attrs: {
              fontSize: size,
              x: select ? 46 + nodeOrigin.x : 30 + nodeOrigin.x,
              y: select ? 31 + nodeOrigin.y : 20 + nodeOrigin.y,
              text: tb_name.length > 8 ? tb_name.substr(0, 8) + '...' : tb_name,
              fill: color,
              fontWeight: select ? 600 : 400,
              lineHeight: 20,
              cursor: 'pointer'
            },
            name: 'table-name'
          });
          // 添加图片背景区域
          if (select) {
            group.addShape('circle', {
              attrs: {
                x: nodeOrigin.x + 26,
                y: nodeOrigin.y + 24,
                width: 32,
                height: 32,
                r: 16,
                fill: 'rgba(255, 255, 255, 0.32)'
              },
              name: 'icon-circle'
            });
          }
          // 添加图片
          group.addShape('image', {
            attrs: {
              x: select ? 18 + nodeOrigin.x : 10 + nodeOrigin.x,
              y: select ? 16 + nodeOrigin.y : 6 + nodeOrigin.y,
              width: 16,
              height: 16,
              img: $this.nodeTypeMap[tb_storage_flag].img
            },
            name: 'table-image'
          });
          // 添加展开收起
          if (can_extract) {
            const extractBtnConfig = {
              y: -6,
              width: 12,
              height: 12,
              stroke: 'rgba(86, 98, 118, 0.64)',
              cursor: 'pointer',
              fill: '#fff',
              radius: 2
            };
            const extractTextConfig = {
              y: -1,
              textAlign: 'center',
              textBaseline: 'middle',
              text: collapsed ? '+' : '-',
              fontSize: 12,
              cursor: 'pointer',
              fill: 'rgba(86, 98, 118, 0.64)'
            };
            // 上游
            if (node_position === 0) {
              group.addShape('rect', {
                attrs: {
                  x: -rectConfig.width / 2 - 6,
                  ...extractBtnConfig
                },
                name: 'collapse-back',
                modelId: id
              });
              group.addShape('text', {
                attrs: {
                  x: -rectConfig.width / 2,
                  ...extractTextConfig
                },
                name: 'collapse-text',
                modelId: id
              });
            }
            // 下游
            if ((node_position === 1 && $this.statisticData.direct_downstream_cnt.count > 0) || node_position === 2) {
              group.addShape('rect', {
                attrs: {
                  x: rectConfig.width / 2 - 6,
                  ...extractBtnConfig
                },
                name: 'collapse-back',
                modelId: id
              });
              group.addShape('text', {
                attrs: {
                  x: rectConfig.width / 2,
                  ...extractTextConfig
                },
                name: 'collapse-text',
                modelId: id
              });
            }
            // 当前表的向上展开和向下展开 需要判断一下当前节点的上游节点是否存在
            if (node_position === 1 && $this.statisticData.direct_upstream_cnt.count > 0) {
              // 向上
              group.addShape('rect', {
                attrs: {
                  x: -rectConfig.width / 2 - 6,
                  ...extractBtnConfig
                },
                name: 'up-collapse-back',
                modelId: id
              });
              group.addShape('text', {
                attrs: {
                  x: -rectConfig.width / 2,
                  ...extractTextConfig,
                  text: upperCollapsed ? '+' : '-'
                },
                name: 'up-collapse-text',
                modelId: id
              });
            }
          }
          return rect;
        },
        update(cfg, item) {
          const group = item.getContainer();
          this.updateLinkPoints(cfg, group);
        },
        setState(name, value, item) {
          if (name === 'collapsed') {
            const group = item.getContainer();
            const collapseText = group.find(e => e.get('name') === 'collapse-text');
            if (collapseText) {
              if (!value) {
                collapseText.attr({
                  text: '-'
                });
              } else {
                collapseText.attr({
                  text: '+'
                });
              }
            }
          }
          if (name === 'upperCollapsed') {
            const group = item.getContainer();
            const collapseText = group.find(e => e.get('name') === 'up-collapse-text');
            if (collapseText) {
              if (!value) {
                collapseText.attr({
                  text: '-'
                });
              } else {
                collapseText.attr({
                  text: '+'
                });
              }
            }
          }
          if (name === 'hover') {
            const model: any = item.getModel();
            const select = model.node_position === 1; // 上游(0)|自身(1)|下游(2)
            const group = item.getContainer();
            const shape = group.get('children')[0];
              const borderColor = select ? $this.nodeTypeMap[model.tb_storage_flag].borderShdow : 'rgba(15, 34, 67, 0.07)';
              if (value) {
                shape.attr('stroke', borderColor);
              } else {
                shape.attr('stroke', 'transparent');
              }
          }
        }
      },
      'rect'
    );
  }
  // 注册字段节点
  registerFieldNode() {
    const $this = this;
    G6.registerNode(
      'field-node',
      {
        draw: (cfg: any, group) => {
          const {
            id,
            tb_name,
            field_name,
            field_type,
            node_position,
            can_extract,
            collapsed,
            upperCollapsed,
            tb_storage_flag,
            is_group_node,
            field_count
          } = cfg;
          const select = node_position === 1; // 上游(0)|自身(1)|下游(2)
          const background = select ? $this.nodeTypeMap[tb_storage_flag].fieldCbg : $this.nodeTypeMap[tb_storage_flag].fieldbg;
          const size = select ? 14 : 12;
          const color = select ? '#fff' : 'rgba(21, 22, 24, 0.72)';
          // 字段信息矩形
          // const fieldNum = field_name.length > 10 ? 10 : field_name.length;
          // const tableNum = tb_name.length > 10 ? 10 : tb_name.length;
          // const num = Math.max(fieldNum, tableNum);
          // let width = select ? num * 14 : num * 12;
          const width = select ? 112 : 96;
          const fieldRectConfig = {
            width: width + 48,
            height: select ? 64 : 56,
            lineWidth: 1,
            fill: background,
            radius: 6,
            stroke: select ? $this.nodeTypeMap[tb_storage_flag].color : $this.colors.line,
            cursor: 'pointer'
          };
          const nodeOrigin = {
            x: -fieldRectConfig.width / 2,
            y: -fieldRectConfig.height / 2
          };
          // 添加描边
          group.addShape('rect', {
            attrs: {
              x: nodeOrigin.x - 2,
              y: nodeOrigin.y - 2,
              width: width + 48 + 4,
              height: (select ? 64 : 56) + 4,
              lineWidth: 4,
              radius: 6
            },
            name: 'field-rect-stroke'
          });
          const lineDash = is_group_node ? [2] : [0];
          const rect = group.addShape('rect', {
            attrs: {
              x: nodeOrigin.x,
              y: nodeOrigin.y,
              ...fieldRectConfig,
              lineDash
            },
            name: 'field-rect'
          });
          // 字段名
          if (is_group_node) {
            group.addShape('text', {
              attrs: {
                fontSize: 12,
                x: 8 + nodeOrigin.x,
                y: 20 + nodeOrigin.y,
                text: '包含字段',
                fill: 'rgba(21, 22, 24, 0.72)',
                fontWeight: 600,
                lineHeight: 20,
                cursor: 'pointer'
              },
              name: 'group-node-title'
            });
            let countRectWidth = 18;
            if (field_count > 10) {
              countRectWidth = 25;
            }
            if (field_count > 100) {
              countRectWidth = 30;
            }
            if (field_count > 1000) {
              countRectWidth = 35;
            }
            group.addShape('rect', {
              attrs: {
                x: 8 + nodeOrigin.x + 4 + 48,
                y: nodeOrigin.y + 5,
                width: countRectWidth,
                height: 18,
                fill: $this.nodeTypeMap[tb_storage_flag].color,
                radius: 8
              },
              name: 'group-count-rect'
            });
            group.addShape('text', {
              attrs: {
                fontSize: 10,
                x: 8 + nodeOrigin.x + 4 + 48 + 6,
                y: 19 + nodeOrigin.y,
                text: field_count,
                fill: '#fff',
                fontWeight: 600,
                cursor: 'pointer'
              },
              name: 'group-node-count'
            });
          } else {
            group.addShape('text', {
              attrs: {
                fontSize: size,
                x: 30 + nodeOrigin.x,
                y: select ? 25 + nodeOrigin.y : 20 + nodeOrigin.y,
                text: field_name.length > 8 ? field_name.substr(0, 8) + '...' : field_name,
                fill: color,
                fontWeight: select ? 600 : 400,
                lineHeight: 20,
                cursor: 'pointer'
              },
              name: 'field-name'
            });
            // 字段icon
            group.addShape('image', {
              attrs: {
                x: 10 + nodeOrigin.x,
                y: select ? 10 + nodeOrigin.y : 6 + nodeOrigin.y,
                width: 16,
                height: 16,
                img: select ? $this.fieldTypeMap[field_type].imgWhite : $this.fieldTypeMap[field_type][tb_storage_flag]
              },
              name: 'field-image'
            });
          }
          // 表信息矩形
          group.addShape('rect', {
            attrs: {
              x: nodeOrigin.x,
              y: select ? nodeOrigin.y + 36 : nodeOrigin.y + 28,
              width: width + 48,
              height: 28,
              fill: '#fff',
              radius: [0, 0, 6, 6]
            },
            name: 'table-rect'
          });
          // 表名
          group.addShape('text', {
            attrs: {
              fontSize: 12,
              x: 30 + nodeOrigin.x,
              y: select ? nodeOrigin.y + 56 : nodeOrigin.y + 48,
              text: tb_name.length > 8 ? tb_name.substr(0, 8) + '...' : tb_name,
              fill: 'rgba(21, 22, 24, 0.48)',
              lineHeight: 20,
              cursor: 'pointer'
            },
            name: 'table-name'
          });
          // 表icon
          group.addShape('image', {
            attrs: {
              x: 10 + nodeOrigin.x,
              y: select ? 42 + nodeOrigin.y : 34 + nodeOrigin.y,
              width: 16,
              height: 16,
              img: $this.nodeTypeMap[tb_storage_flag].img
            },
            name: 'table-image'
          });
          // 添加展开收起
          if (can_extract && !is_group_node) {
            const extractBtnConfig = {
              y: -6,
              width: 12,
              height: 12,
              stroke: 'rgba(86, 98, 118, 0.64)',
              cursor: 'pointer',
              fill: '#fff',
              radius: 2
            };
            const extractTextConfig = {
              y: -1,
              textAlign: 'center',
              textBaseline: 'middle',
              text: collapsed ? '+' : '-',
              fontSize: 12,
              cursor: 'pointer',
              fill: 'rgba(86, 98, 118, 0.64)'
            };
            // 上游节点
            if (node_position === 0) {
              group.addShape('rect', {
                attrs: {
                  x: -fieldRectConfig.width / 2 - 6,
                  ...extractBtnConfig
                },
                name: 'collapse-back',
                modelId: id
              });
              group.addShape('text', {
                attrs: {
                  x: -fieldRectConfig.width / 2,
                  ...extractTextConfig
                },
                name: 'collapse-text',
                modelId: id
              });
            }
            // 下游节点或者当前节点
            if ((node_position === 1 && this.statisticData.direct_downstream_cnt.count > 0) || node_position === 2) {
              group.addShape('rect', {
                attrs: {
                  x: fieldRectConfig.width / 2 - 6,
                  ...extractBtnConfig
                },
                name: 'collapse-back',
                modelId: id
              });
              group.addShape('text', {
                attrs: {
                  x: fieldRectConfig.width / 2,
                  ...extractTextConfig
                },
                name: 'collapse-text',
                modelId: id
              });
            }
            // 当前表的向上展开和向下展开
            if (node_position === 1 && this.statisticData.direct_upstream_cnt.count > 0) {
              // 向上
              group.addShape('rect', {
                attrs: {
                  x: -fieldRectConfig.width / 2 - 6,
                  ...extractBtnConfig
                },
                name: 'up-collapse-back',
                modelId: id
              });
              group.addShape('text', {
                attrs: {
                  x: -fieldRectConfig.width / 2,
                  ...extractTextConfig,
                  text: upperCollapsed ? '+' : '-'
                },
                name: 'up-collapse-text',
                modelId: id
              });
            }
          }
          return rect;
        },
        update(cfg, item) {
          const group = item.getContainer();
          this.updateLinkPoints(cfg, group);
        },
        setState(name, value, item) {
          if (name === 'collapsed') {
            const group = item.getContainer();
            const collapseText = group.find(e => e.get('name') === 'collapse-text');
            if (collapseText) {
              if (!value) {
                collapseText.attr({
                  text: '-'
                });
              } else {
                collapseText.attr({
                  text: '+'
                });
              }
            }
          }
          if (name === 'upperCollapsed') {
            const group = item.getContainer();
            const collapseText = group.find(e => e.get('name') === 'up-collapse-text');
            if (collapseText) {
              if (!value) {
                collapseText.attr({
                  text: '-'
                });
              } else {
                collapseText.attr({
                  text: '+'
                });
              }
            }
          }
          if (name === 'hover') {
            const model: any = item.getModel();
            const select = model.node_position === 1; // 上游(0)|自身(1)|下游(2)
            const group = item.getContainer();
            const shape = group.get('children')[0];
            const borderColor = select ? $this.nodeTypeMap[model.tb_storage_flag].borderShdow : 'rgba(15, 34, 67, 0.07)';
            if (value) {
              shape.attr('stroke', borderColor);
            } else {
              shape.attr('stroke', 'transparent');
            }
          }
        }
      },
      'rect'
    );
  }
  // 注册边
  registerEdge() {
    const $this = this;
    G6.registerEdge('kinship-edge', {
      itemType: 'edge',
      draw: function draw(cfg: any, group) {
        const { startPoint, endPoint, label, lineage_type } = cfg;
        let sourceNode = cfg.sourceNode.getModel();
        const nodePosition = sourceNode.node_position;
        if (nodePosition === 0) {
          sourceNode = cfg.targetNode.getModel();
        }
        const hgap = Math.abs(endPoint.x - startPoint.x);
        let pathcdis = 58;
        let circleLineWidth = 1;
        let circleStrokeColor = $this.edgeMap[lineage_type] ? $this.edgeMap[lineage_type].color : '';
        if (sourceNode.is_group_node) {
          pathcdis = 14;
          circleStrokeColor = '#CCCED4';
          circleLineWidth = 1.5;
        }
        let path = [
          ['M', startPoint.x, startPoint.y],
          ['C', startPoint.x + hgap / 4, startPoint.y, endPoint.x - hgap / 2, endPoint.y, endPoint.x - pathcdis, endPoint.y]
        ];
        let relationRectPos = {
          x: endPoint.x - 36 - 22,
          y: endPoint.y - 9
        };
        let lineRectPos = {
          x: endPoint.x - 21.5,
          y: endPoint.y - 1
        };
        let textPos = {
          x: endPoint.x - 28 - 22,
          y: endPoint.y + 6
        };
        let circlePos = {
          x: endPoint.x - 7,
          y: endPoint.y
        };
        if (nodePosition === 0) {
          path = [
            ['M', startPoint.x + pathcdis, startPoint.y],
            ['C', startPoint.x + pathcdis + hgap / 4, startPoint.y, startPoint.x + pathcdis + hgap / 2, endPoint.y, endPoint.x, endPoint.y]
          ];
          relationRectPos = {
            x: startPoint.x + 22,
            y: startPoint.y - 9
          };
          lineRectPos = {
            x: startPoint.x + 14,
            y: startPoint.y - 1
          };
          textPos = {
            x: startPoint.x + 30,
            y: startPoint.y + 6
          };
          circlePos = {
            x: startPoint.x + 8,
            y: startPoint.y
          };
        }
        const line = group.addShape('path', {
          attrs: {
            stroke: $this.colors.line,
            path,
            lineWidth: 2,
            endArrow: false
          },
          name: 'edge-path'
        });
        if (!sourceNode.is_group_node) {
          // 添加关系
          group.addShape('rect', {
            attrs: {
              width: 36,
              height: 20,
              lineWidth: 1,
              fill: '#fff',
              radius: 10,
              stroke: $this.edgeMap[lineage_type] ? $this.edgeMap[lineage_type].color : '',
              ...relationRectPos
            }
          });
          // 添加关系右边的小线段
          group.addShape('rect', {
            attrs: {
              width: 8,
              height: 2,
              fill: $this.colors.line,
              ...lineRectPos
            }
          });
          group.addShape('text', {
            attrs: {
              ...textPos,
              text: $this.edgeMap[lineage_type] ? $this.edgeMap[lineage_type].text : '',
              fontSize: 10,
              fill: $this.edgeMap[lineage_type] ? $this.edgeMap[lineage_type].color : ''
            },
            name: 'text-shape-edge'
          });
        }
        group.addShape('circle', {
          attrs: {
            r: 3,
            width: 6,
            height: 6,
            ...circlePos,
            lineWidth: circleLineWidth,
            fill: '#fff',
            stroke: circleStrokeColor
          }
        });
        return line;
      }
    });
  }
  // 注册tooltip
  registerTooltip() {
    const $this = this;
    this.tooltip = new BloodTooltip({
      className: $this.type === 'field' ? 'blood-tooltip' : 'table-blood-tooltip',
      offsetX: -36,
      offsetY: 24,
      // 允许出现 tooltip 的 item 类型
      itemTypes: ['node'],
      getContent: e => {
        const node = e.item.getModel();
        return $this.getTooltipContent(node);
      },
      shouldBegin: e => {
        let eleArr = ['table-rect', 'table-name', 'table-image'];
        if ($this.type === 'field') {
          eleArr = [
            'table-rect',
            'table-name',
            'table-image',
            'field-rect',
            'field-name',
            'field-image',
            'group-node-title',
            'group-count-rect',
            'group-node-count'
          ];
        }
        if (eleArr.indexOf(e.target.get('name')) > -1) {
          return true;
        }
        return false;
      }
    });
    // this.tooltip.updatePosition = () => {
    //   console.log(this.tooltip.get('shouldBegin'));
    // };
  }
  // 初始化图例
  initGraph(data) {
    this.registerTooltip();
    // 实例化 minimap 插件
    // const minimap = new G6.Minimap({
    //   size: [100, 100],
    //   className: 'minimap',
    //   type: 'delegate',
    // });
    if (!data) {
      return;
    }

    this.graph = new G6.Graph({
      container: this.lineageGraph.nativeElement,
      ...this.defaultConfig,
      plugins: [this.tooltip]
    });
    this.initEvent();
    this.graph.data(data); // 读取数据
    this.graph.render(); // 渲染
  }
  // 初始化事件
  initEvent() {
    const handleCollapse = async (e, collapseField) => {
      const target = e.target;
      const id = target.get('modelId') || e.item.getModel().id;
      const item = this.graph.findById(id);
      const nodeModel = item.getModel();
      const currentModelX = nodeModel.x;
      const currentModelY = nodeModel.y;
      // 当前是折叠状态true 应该展开 当前是展开状态 应该收起
      // 隐藏相邻节点
      const position = nodeModel.node_position;
      let neighborNodes = [];
      // 请求数据
      let isAdd = false;
      if (nodeModel.node_position !== 1 && nodeModel.can_extract && nodeModel[collapseField] && !this.nodeIsRequest[nodeModel.id]) {
        await this.getData(nodeModel).then((resultData: BloodDataConfig) => {
          this.nodeIsRequest[nodeModel.id] = true;
          if (resultData.nodes.length > 0) {
            // 添加节点
            resultData.nodes.forEach(node => {
              node.x = currentModelX;
              node.y = currentModelY;
              if (node.can_extract) {
                node.collapsed = true;
              }
              this.graph.addItem('node', node);
            });
            // 添加边
            resultData.edges.forEach(edge => {
              this.graph.addItem('edge', edge);
            });
            isAdd = true;
          }
        });
      }
      if (position === 2 || (position === 1 && collapseField === 'collapsed')) {
        // 只获取当前节点指向的目标节点
        neighborNodes = this.graph.getNeighbors(id, 'target');
      } else {
        // 只获取当前节点的源节点
        neighborNodes = this.graph.getNeighbors(id, 'source');
      }
      this.connectedNodeIds = [];
      this.deepNodes(neighborNodes, nodeModel, nodeModel[collapseField]);
      // 隐藏边
      // if (position === 2 || (position === 1 && collapseField === 'collapsed')) {
      //   this.graph.getEdges().forEach(edge => {
      //     const edgeModel = edge.getModel();
      //     if (this.connectedNodeIds.indexOf(edgeModel.target) > -1) {
      //       if (nodeModel[collapseField]) {
      //         edge.show();
      //       } else {
      //         edge.hide();
      //       }
      //     }
      //   });
      // } else {
      //   this.graph.getEdges().forEach(edge => {
      //     const edgeModel = edge.getModel();
      //     if (this.connectedNodeIds.indexOf(edgeModel.source) > -1) {
      //       if (nodeModel[collapseField]) {
      //         edge.show();
      //       } else {
      //         edge.hide();
      //       }
      //     }
      //   });
      // }
      nodeModel[collapseField] = !nodeModel[collapseField];
      this.graph.setItemState(item, collapseField, nodeModel[collapseField] ? 1 : 0);
      if (position !== 1 || (position === 1 && !nodeModel[collapseField])) {
        // if (!nodeModel[collapseField]) {
        // nodeModel.originX = nodeModel.originX ? nodeModel.originX : nodeModel.x;
        // nodeModel.originy = nodeModel.originy ? nodeModel.originy : nodeModel.y;
        this.graph.layout();
        this.graph.focusItem(item, true, {
          easing: 'easeLinear',
          duration: 500
        });
        // this.graph.moveTo(nodeModel.originX, nodeModel.y);
        // setTimeout(() => {
        //   this.graph.fitView();
        // }, 500);
      }
      // if (isAdd) {
      //   this.graph.refresh();
      // }
    };
    this.graph.on('collapse-text:click', e => {
      handleCollapse(e, 'collapsed');
      e.stopPropagation();
    });
    this.graph.on('collapse-back:click', e => {
      handleCollapse(e, 'collapsed');
      e.stopPropagation();
    });
    this.graph.on('up-collapse-text:click', e => {
      handleCollapse(e, 'upperCollapsed');
      e.stopPropagation();
    });
    this.graph.on('up-collapse-back:click', e => {
      handleCollapse(e, 'upperCollapsed');
      e.stopPropagation();
    });
    // 点击节点
    this.graph.on('node:click', e => {
      const node: NodeConfig = e.item.getModel();
      if (node.is_group_node) {
        handleCollapse(e, 'collapsed');
        return false;
      }
      let url = `./${node.tb_id}/blood-info/`;
      if (this.type === 'table') {
        url += `table-blood?tbType=${node.tb_type}`;
      } else {
        url += `field-blood?tbType=${node.tb_type}&fieldId=${node.field_id}`;
      }
      window.open(url, '_blank');
    });
    // 监听鼠标进入节点事件
    this.graph.on('node:mouseenter', evt => {
      const node = evt.item;
      // 激活该节点的 hover 状态
      this.graph.setItemState(node, 'hover', true);
    });
    // 监听鼠标离开节点事件
    this.graph.on('node:mouseleave', evt => {
      const node = evt.item;
      // 关闭该节点的 hover 状态
      this.graph.setItemState(node, 'hover', false);
    });
  }
  // 获取tooltip内容
  getTooltipContent(node: NodeConfig) {
    let alignCenter = 'center';
    if (node.tb_name.length > 9) {
      alignCenter = 'flex-start';
    }
    let createTimeText = '创建时间';
    if (node.tb_type === 'RAW') {
      createTimeText = '接入时间';
    }
    if (this.type === 'table') {
      let tpl = `<ul class="table-${node.tb_type}">
        <li style="align-items: ${alignCenter}">
          <span class='title'>表名称</span>
          <span class='content'>
            ${node.tb_name}
            <span class='tag type' style="border: 0; color: ${
              this.nodeTypeMap[node.tb_storage_flag] ? this.nodeTypeMap[node.tb_storage_flag].color : ''
            };
            background-color: ${this.nodeTypeMap[node.tb_storage_flag] ? this.nodeTypeMap[node.tb_storage_flag].bgColor : ''};">
              ${this.nodeTypeMap[node.tb_storage_flag] ? this.nodeTypeMap[node.tb_storage_flag].text : ''}
            </span>
            <span class='tag type' style="border: 0; color: ${
              this.nodeTypeMap[node.tb_storage_flag] ? this.nodeTypeMap[node.tb_storage_flag].color : ''
            }; display:${this.tbBaseInfo.storage_type === 3 ? 'unset' : 'none'};
            background-color: ${this.nodeTypeMap[node.tb_storage_flag] ? this.nodeTypeMap[node.tb_storage_flag].bgColor : ''};">
              分区表
            </span>
          </span>
        </li>`;
      if (node.tb_type === 'RAW') {
        tpl += `
          <li>
            <span class='title'>原始名称</span>
            <span class='content'>${node.ori_tb_name || '-'}</span>
          </li>
        `;
      }
      tpl += `
        <li>
          <span class='title'>${createTimeText}</span>
          <span class='content'>${node.create_time}</span>
        </li>
        <li class='align-center'>
          <span class='title'>所属数据层</span>
          <span class='content'>${this.tableTypeMap[node.tb_type] ? this.tableTypeMap[node.tb_type].text : ''}</span>
        </li>
        <li>
          <span class="title">存储类型</span>
          <span class='content'>${node.is_partition_tb === 1 ? '分区' : '非分区'}</span>
        </li>
      `;
      if (node.tb_type === 'RAW') {
        tpl += `
            <li>
              <span class='title'>接入方式</span>
              <span class='content'>${node.access_mode || '-'}</span>
            </li>
          `;
      }
      tpl += `<li>
          <span class='title'>表ID</span>
          <span class='content'>${node.tb_id}</span>
        </li>
        <li>
          <span class='title'>数据量</span>
          <span class='content'>${this.parseNum(node.count)}</span>
        </li>
        <li class='align-center'>
          <span class='title'>更新状态</span>
          <span class='content tag'
            style="color: ${this.statusMap[node.update_status] ? this.statusMap[node.update_status].color : ''};
            border-color: ${this.statusMap[node.update_status] ? this.statusMap[node.update_status].color : ''};">
            ${this.statusMap[node.update_status] ? this.statusMap[node.update_status].text : ''}
          </span>
        </li>
        <li>
          <span class='title'>更新时间</span>
          <span class='content'>${node.update_time}</span>
        </li>
      </ul>`;
      // <img src="${this.imgUrl}tooltip-arrow.svg" class='tooltip-arrow' />
      return tpl;
    } else {
      let tpl = '';
      if (node.is_group_node) {
        tpl = '<span class="group-node-hover">涉及该表多个字段，此处作聚合展示，点击可查看字段明细</span>';
        return tpl;
      }
      tpl = `<ul class="field-${node.tb_type}">
        <li>
          <span class='title'>字段名称</span>
          <span class='content'>${node.field_name}</span>
        </li>`;
      if (node.tb_type === 'RAW') {
        tpl += `
          <li>
            <span class='title'>原始名称</span>
            <span class='content'>${node.ori_field_name || '-'}</span>
          </li>
        `;
      }
      tpl += `<li>
        <span class='title'>${createTimeText}</span>
        <span class='content'>${node.create_time}</span>
      </li>
      <li style="align-items: ${alignCenter}">
        <span class='title'>所属表</span>
        <span class='content'>
          ${node.tb_name}
          <span class='tag type' style="border: 0; color: ${
            this.nodeTypeMap[node.tb_storage_flag] ? this.nodeTypeMap[node.tb_storage_flag].color : ''
          };
          background-color: ${this.nodeTypeMap[node.tb_storage_flag] ? this.nodeTypeMap[node.tb_storage_flag].bgColor : ''};">
            ${this.nodeTypeMap[node.tb_storage_flag] ? this.nodeTypeMap[node.tb_storage_flag].text : ''}
          </span>
          <span class='tag type' style="border: 0; color: ${
            this.nodeTypeMap[node.tb_storage_flag] ? this.nodeTypeMap[node.tb_storage_flag].color : ''
          }; display:${this.tbBaseInfo.storage_type === 3 ? 'unset' : 'none'};
          background-color: ${this.nodeTypeMap[node.tb_storage_flag] ? this.nodeTypeMap[node.tb_storage_flag].bgColor : ''};">
            分区表
          </span>
        </span>
      </li>
      <li class='align-center'>
        <span class='title'>所属数据层</span>
        <span class='content'>${this.tableTypeMap[node.tb_type] ? this.tableTypeMap[node.tb_type].text : ''}</span>
      </li>
      <li>
          <span class="title">存储类型</span>
          <span class='content'>${node.is_partition_tb === 1 ? '分区' : '非分区'}</span>
      </li>`;
      if (node.tb_type === 'RAW') {
        tpl += `
          <li>
            <span class='title'>接入方式</span>
            <span class='content'>${node.access_mode || '-'}</span>
          </li>`;
      }
      tpl += `
        <li class='align-center'>
          <span class='title'>更新状态</span>
          <span class='content tag'
            style="color: ${this.statusMap[node.update_status] ? this.statusMap[node.update_status].color : ''};
            border-color: ${this.statusMap[node.update_status] ? this.statusMap[node.update_status].color : ''};">
            ${this.statusMap[node.update_status] ? this.statusMap[node.update_status].text : ''}
          </span>
        </li>
        <li>
          <span class='title'>更新时间</span>
          <span class='content'>${node.update_time}</span>
        </li>
      </ul>`;
      // <img src="${this.imgUrl}tooltip-arrow.svg" class='tooltip-arrow' />
      return tpl;
    }
  }
  // 数据量转换x亿x万x条
  parseNum(count: number) {
    if (!count) {
      return 0;
    }
    const numArr = [];
    let i = 0;
    while (count > 0) {
      let unit = '';
      switch (i) {
        case 0:
          unit = '条';
          break;
        case 1:
          unit = '万';
          break;
        case 2:
          unit = '亿';
          break;
      }
      numArr.unshift((count % 10000) + unit);
      count = Math.floor(count / 10000);
      i++;
    }
    return numArr.join('');
  }

  // 节点展开收起
  deepNodes(nodes, nodeModel, isOpen) {
    nodes.forEach(node => {
      const model = node.getModel();
      const id = model.id;
      if (isOpen) {
        this.graph.showItem(node);
        // node.show();
      } else {
        // node.hide();
        this.graph.hideItem(node);
      }
      this.connectedNodeIds.push(id);
      let neighborNodes = [];
      if (!model.collapsed) {
        if (model.node_position === 2) {
          neighborNodes = this.graph.getNeighbors(id, 'target');
        } else {
          neighborNodes = this.graph.getNeighbors(id, 'source');
        }
        if (neighborNodes.length > 0) {
          this.deepNodes(neighborNodes, model, isOpen);
        }
      }
    });
  }

  // 扩展血缘数据
  getData(nodeModel) {
    return new Promise(resolve => {
      let params: GetBloodDataParams = {
        tb_id: nodeModel.tb_id,
        tb_type: nodeModel.tb_type,
        node_position: nodeModel.node_position,
        node_id: nodeModel.id
      };
      const resultData: BloodDataConfig = {
        nodes: [],
        edges: []
      };
      this.loading = true;
      if (this.type === 'table') {
        this.bloodGraphService
          .getTableBloodData(params)
          .pipe(finalize(() => (this.loading = false)))
          .subscribe(data => {
            resultData.nodes = data.nodes;
            resultData.edges = data.edges;
            resolve(resultData);
          });
      } else {
        params.is_group_node = nodeModel.is_group_node;
        if (nodeModel.is_group_node) {
          // node_id 和 group_tb_id 用当前节点的 其余信息tb_id 和 tb_type 和 node_position 和 field_id用上一个节点的
          const item = this.graph.findById(nodeModel.id);
          // 获取聚合节点源节点
          let source = null;
          let edge = null;
          // 下游
          if (nodeModel.node_position === 2) {
            source = this.graph.getNeighbors(nodeModel.id, 'source')[0].getModel();
            edge = item.getInEdges()[0].getModel();
          } else {
            // 上游
            source = this.graph.getNeighbors(nodeModel.id, 'target')[0].getModel();
            edge = item.getOutEdges()[0].getModel();
          }
          params = {
            ...params,
            tb_id: source.tb_id,
            tb_type: source.tb_type,
            field_id: source.field_id,
            group_lineage_type: edge.lineage_type,
            group_tb_id: nodeModel.tb_id
          };
        } else {
          params.field_id = nodeModel.field_id;
        }
        this.bloodGraphService
          .getFieldsBloodData(params)
          .pipe(finalize(() => (this.loading = false)))
          .subscribe(data => {
            resultData.nodes = data.nodes;
            resultData.edges = data.edges;
            resolve(resultData);
          });
      }
    });
  }
}
