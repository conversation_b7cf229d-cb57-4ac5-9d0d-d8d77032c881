@import '@haizhi/ui/styles/themes/light.data.scss';
#lineage-graph {
  background: $mono-200;
  width: 100%;
  height: 100%;
  ::ng-deep .blood-tooltip,
  ::ng-deep .table-blood-tooltip {
    border-radius: 8px;
    background-color: $mono-100;
    box-shadow: $container-c300;
    position: relative;
    // width: 246px;
    // min-width: 246px;
    .tooltip-arrow {
      position: absolute;
      left: 16px;
      top: -6px;
    }
    .group-node-hover {
      padding: 8px 16px;
      width: 200px;
      display: inline-block;
    }
    ul {
      width: 340px;
      padding: 12px 16px 8px 16px;
      &.table-RAW {
        li .title {
          width: 60px;
          min-width: 60px;
        }
      }
      &.field-RAW {
        li .title {
          width: 60px;
          min-width: 60px;
        }
      }
      li {
        margin-bottom: 8px;
        line-height: 20px;
        display: flex;
        &.align-center {
          align-items: center;
        }
        .title {
          font-weight: 600;
          font-size: 12px;
          line-height: 20px;
          color: $type-600;
          width: 60px;
          margin-right: 8px;
          display: inline-block;
          white-space: nowrap;
          min-width: 60px;
        }
        .content {
          color: $type-800;
          word-break: break-all;
        }
        .tag {
          font-size: 10px;
          line-height: 14px;
          display: inline-block;
          padding: 4px 6px;
          border: 1px solid $primary-900;
          border-radius: 6px;
          color: $primary-900;
          white-space: nowrap;
          &.type {
            border-radius: 16px;
            line-height: 20px;
            padding: 1px 7px;
            margin-left: 4px;
          }
        }
      }
    }
  }
  // ::ng-deep .table-blood-tooltip {
  //   width: 340px;
  // }
}
