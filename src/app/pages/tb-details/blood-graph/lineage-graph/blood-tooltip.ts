import G6 from '@antv/g6';
import { IG6GraphEvent, IAbstractGraph as IGraph } from '@antv/g6-core';
import { modifyCSS } from '@antv/dom-util';
export default class BloodTooltip extends G6.Tooltip {
  updatePosition(e) {
    const shouldBegin = this.get('shouldBegin');
    const tooltip = this.get('tooltip');
    if (!shouldBegin(e)) {
      modifyCSS(tooltip, {
        visibility: 'hidden'
      });
      return;
    }
    const graph: IGraph = this.get('graph');
    const width: number = graph.get('width');
    const height: number = graph.get('height');

    const offsetX = this.get('offsetX') || 0;
    const offsetY = this.get('offsetY') || 0;

    // const mousePos = graph.getPointByClient(e.clientX, e.clientY);
    const point = graph.getPointByClient(e.clientX, e.clientY);
    let { x, y } = graph.getCanvasByPoint(point.x, point.y);

    // let x = mousePos.x + offsetX;
    // let y = mousePos.y + offsetY;
    // let x = e.x + offsetX;
    // let y = e.y + offsetY;
    x += offsetX;
    y += offsetY;

    const bbox = tooltip.getBoundingClientRect();
    if (x + bbox.width > width) {
      x = x - bbox.width - offsetX;
    }

    if (y + bbox.height > height) {
      y = y - bbox.height - offsetY - 24;
    }

    modifyCSS(tooltip, {
      left: `${x}px`,
      top: `${y}px`,
      visibility: 'visible'
    });
  }
}
