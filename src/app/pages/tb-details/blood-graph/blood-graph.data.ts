export const relationData = {
  nodes: [
    {
      id: '1',
      tb_id: 'tb_12123123123',
      tb_name: '流式表',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称1',
      field_type: 2,
      node_position: 0,
      can_extract: true,
      collapsed: false, // 当前操作是收起（true）还是展开（false）
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 2,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 1
    },
    {
      id: '2',
      tb_id: 'tb_12123123123',
      tb_name: '我是一张原始表',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称2',
      field_type: 2,
      node_position: 1,
      can_extract: true,
      collapsed: false,
      upperCollapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 1
    },
    {
      id: '3',
      tb_id: 'tb_12123123123',
      tb_name: '源表',
      tb_type: 'RESULT',
      field_id: 'fk1123123',
      field_name: '字段名称3',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 0,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '4',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称4',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称4',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '5',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称5',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称5',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '6',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称6',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称6',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '7',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称7',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称7',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '8',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称8',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称8',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '9',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称9',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称9',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '10',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称10',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称10',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '11',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称11',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称11',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 2,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '12',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称12',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称12',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '13',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称13',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称13',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    },
    {
      id: '14',
      tb_id: 'tb_12123123123',
      tb_name: '标签表名称14',
      tb_type: 'RAW',
      field_id: 'fk1123123',
      field_name: '字段名称14',
      field_type: 2,
      node_position: 2,
      can_extract: true,
      collapsed: false,
      create_time: '2019/09/09 11:20:20',
      count: 123456,
      update_status: 1,
      update_time: '2019/09/09 11:20:20',
      access_mode: '数据库(monggoDB) / 流式数据(Kafka) / API / 文本 / 口袋数据',
      node_type: 0
    }
  ],
  edges: [
    {
      source: '1',
      target: '2',
      lineage_type: 'RULE'
    },
    {
      source: '2',
      target: '3',
      lineage_type: 'RULE'
    },
    {
      source: '3',
      target: '4',
      lineage_type: 'RULE'
    },
    {
      source: '3',
      target: '5',
      lineage_type: 'MAP'
    },
    {
      source: '3',
      target: '6',
      lineage_type: 'MODEL'
    },
    {
      source: '3',
      target: '7',
      lineage_type: 'TAG'
    },
    {
      source: '3',
      target: '8',
      lineage_type: 'RULE'
    },
    {
      source: '3',
      target: '9',
      lineage_type: 'RULE'
    },
    {
      source: '5',
      target: '10',
      lineage_type: 'RULE'
    },
    {
      source: '5',
      target: '11',
      lineage_type: 'RULE'
    },
    {
      source: '5',
      target: '12',
      lineage_type: 'RULE'
    },
    {
      source: '5',
      target: '13',
      lineage_type: 'RULE'
    },
    {
      source: '5',
      target: '14',
      lineage_type: 'RULE'
    }
  ]
};
export const modelListData = [
  {
    name: 'flow1',
    flow_id: 'flow_ffcc298b27364d36a34ca273e57f64e8',
    ctime: '2020-12-28 15:12:12',
    utime: '2020-12-28 15:12:20',
    status: '1'
  }
];
