import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { HttpService } from '../../../core/services/http.service';
import {
  EdgeMapConfig,
  NodeTypeMapConfig,
  StatisticsParams,
  FieldResultConfig,
  GetBloodDataParams,
  BloodDataConfig,
  RelatedModelParams,
  ModelListConfig
} from './blood-graph.model';

@Injectable({
  providedIn: 'root'
})
export class BloodGraphService {
  imgUrl = 'assets/images/blood/';
  // 关系类型
  edgeMap: { [index: string]: EdgeMapConfig } = {
    MAP: {
      // color: '#9646FF',
      color: 'rgba(21, 22, 24, 0.48)',
      text: '映射'
    },
    MODEL: {
      // color: '#0099EB',
      color: 'rgba(21, 22, 24, 0.48)',
      text: '模型'
    },
    TAG: {
      // color: '#FF9431',
      color: 'rgba(21, 22, 24, 0.48)',
      text: '标签'
    },
    RULE: {
      // color: '#00BBC2',
      color: 'rgba(21, 22, 24, 0.48)',
      text: '规则'
    },
    PUSH: {
      color: 'rgba(21, 22, 24, 0.48)',
      text: '推送'
    }
    // ELEMENT_EXTRA: {
    //   color: 'rgba(21, 22, 24, 0.48)',
    //   text: '要素'
    // }
  };
  // 节点类型-大类
  nodeTypeMap: { [index: number]: NodeTypeMapConfig } = {
    0: {
      color: '#0099EB',
      text: '普通表',
      bgColor: 'rgba(0, 153, 235, 0.12)', // tag背景色
      cbg: 'l(0) 0:#A0DDFF 0.4:#6CCEF8 1:#0099EB',
      fieldCbg: 'l(0) 0:#0099EB 0.4:#6CCEF8 1:#A0DDFF',
      fieldbg: 'rgba(0, 153, 235, 0.12)',
      hzName: 't-normal',
      img: this.imgUrl + 't-normal.svg',
      borderShdow: 'rgba(0, 153, 235, 0.12)'
    },
    1: {
      color: '#FF9431',
      bgColor: 'rgba(255, 148, 49, 0.12)',
      cbg: 'l(0) 0:#FED134 0.4:#FFB129 1:#FF7C03',
      fieldCbg: 'l(0) 0:#FF7C03 0.4:#FFB129 1:#FED134',
      fieldbg: 'rgba(255, 148, 49, 0.12)',
      text: '在线表',
      hzName: 't-online',
      img: this.imgUrl + 't-online.svg',
      borderShdow: 'rgba(255, 148, 49, 0.12)'
    },
    2: {
      color: '#9646FF',
      bgColor: 'rgba(150, 70, 255, 0.09)',
      cbg: 'l(0) 0:#F2A0FF 0.4:#C05FFF 1:#9646FF',
      fieldCbg: 'l(0) 0:#9646FF 0.4:#C05FFF 1:#F2A0FF',
      fieldbg: 'rgba(150, 70, 255, 0.09)',
      text: '流式表',
      hzName: 't-flow',
      img: this.imgUrl + 't-flow.svg',
      borderShdow: 'rgba(150, 70, 255, 0.12)'
    }
  };

  constructor(private http: HttpService) {}

  // 获取字段血缘统计数据
  getStatistics(params: StatisticsParams): Observable<{ [key: string]: number }> {
    return this.http.get('/api/lineage/field/statistics', params);
  }
  // 获取表级血缘统计数据
  getTbStatistics(params: StatisticsParams): Observable<{ [key: string]: number }> {
    return this.http.get('/api/tb/lineage/statistics', params);
  }
  // 获取表的所有字段
  getFields(params: { tb_id: string }): Observable<FieldResultConfig> {
    return this.http.get('/api/tb/editable_schema', params);
  }
  // 获取字段血缘数据
  getFieldsBloodData(params: GetBloodDataParams): Observable<BloodDataConfig> {
    return this.http.get('/api/lineage/field/detail', params);
  }
  // 获取表级血缘数据
  getTableBloodData(params: GetBloodDataParams): Observable<BloodDataConfig> {
    return this.http.get('/api/tb/lineage/detail', params);
  }
  // 获取关联模型
  getRelatedModel(params: RelatedModelParams): Observable<{ data: ModelListConfig[] }> {
    return this.http.get('/api/tb/flow/relation', params);
  }
  // 获取所属模型
  getBelongModel(params: { tb_id: string }): Observable<ModelListConfig> {
    return this.http.get('/api/tb/flow/belong', params);
  }
  // 获取关联模型权限 adv_id为模型id
  getModelPermission(params: { adv_id: string }): Observable<number> {
    return this.http.post('/api/dataflow/check/product/permission', params);
  }
}
