import { Component, OnInit } from '@angular/core';
import { BloodDataConfig, GetBloodDataParams, StatisticsParams } from '../blood-graph.model';
import { BloodGraphService } from '../blood-graph.service';
import { TbDetailsService } from '../../tb-details.service';
import { finalize } from 'rxjs/operators';
import { forkJoin } from 'rxjs';
// import { relationData } from '../blood-graph.data';

@Component({
  selector: 'table-blood',
  templateUrl: './table-blood.component.html',
  styleUrls: ['./table-blood.component.scss']
})
export class TableBloodComponent implements OnInit {
  params: GetBloodDataParams = {
    tb_id: '',
    tb_type: '',
    node_position: 1,
    node_id: ''
  };
  bloodData: BloodDataConfig = {
    nodes: [],
    edges: []
  };
  loading = false;
  statisticData: {
    [key: string]: {
      title: string;
      count: number;
    };
  } = {
    direct_upstream_cnt: {
      title: '直接上游数', // 直接上游数
      count: 0
    },
    total_upstream_cnt: {
      title: '全部上游数', // 全部上游数
      count: 0
    },
    direct_downstream_cnt: {
      title: '直接下游数', // 直接下游数
      count: 0
    },
    total_downstream_cnt: {
      title: '全部下游数', // 全部下游数
      count: 0
    }
  };
  constructor(private bloodGraphService: BloodGraphService, private tbDetailsService: TbDetailsService) {}

  ngOnInit() {
    this.params.tb_id = this.tbDetailsService.curTbId;
    this.params.tb_type = this.tbDetailsService.curTbType;
    // this.bloodData = relationData;
    this.getData();
  }

  getData() {
    this.loading = true;
    const stsParams: StatisticsParams = {
      tb_id: this.tbDetailsService.curTbId,
      tb_type: this.tbDetailsService.curTbType
    };
    const bloodDataGet$ = this.bloodGraphService.getTableBloodData(this.params);
    const statisGet$ = this.bloodGraphService.getTbStatistics(stsParams);
    forkJoin([bloodDataGet$, statisGet$])
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(results => {
        results[0].nodes.forEach(item => {
          if (item.node_position !== 1 && item.can_extract) {
            item.collapsed = true;
          }
        });
        this.bloodData = results[0];
        Object.keys(this.statisticData).forEach(key => {
          this.statisticData[key].count = results[1][key];
        });
      });
  }
}
