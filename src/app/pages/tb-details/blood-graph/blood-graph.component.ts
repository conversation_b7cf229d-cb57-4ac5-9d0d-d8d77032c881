import { Component, OnInit } from '@angular/core';
import { TbDetailsService } from '../tb-details.service';

@Component({
  selector: 'blood-graph',
  templateUrl: './blood-graph.component.html',
  styleUrls: ['./blood-graph.component.scss']
})
export class BloodGraphComponent implements OnInit {
  tabs = [
    { route: 'table-blood', title: '表级血缘', show: true },
    { route: 'field-blood', title: '字段血缘', show: true },
    { route: 'related-model', title: '关联模型', show: true }
  ];
  tbType = this.tbDetailsService.curTbType;

  constructor(private tbDetailsService: TbDetailsService) {}

  ngOnInit() {
    this.tabs.forEach(item => {
      // 关联模型 【非要素表】
      if (this.tbType === 'ELEMENT' && item.route === 'related-model') {
        item.show = false;
      }
      // 字段血缘 【非动态表】
      // if (this.tbType === 'TOPIC_NEW' && item.route === 'field-blood') {
      //   item.show = false;
      // }
    });
  }
}
