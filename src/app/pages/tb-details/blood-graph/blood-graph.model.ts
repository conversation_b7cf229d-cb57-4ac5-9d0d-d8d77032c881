// 血缘统计相关
export interface StatisticsParams {
  field_id?: string;
  tb_id: string;
  tb_type: string;
}

// 字段配置
export interface FieldConfig {
  field_id: string;
  name: string;
  real_type: number;
  remark: string;
  seq_no: number;
  title: string;
  type: number;
  uniq_index: number;
}

export interface FieldResultConfig {
  comment?: string;
  fields: FieldConfig[];
  rely_tbs?: string[];
  tb_id?: string;
  is_sorted?: number;
}

// 图表相关
export interface NodeConfig {
  id: string; // 在整个血缘图中唯一的值
  tb_id: string; // 表ID
  tb_name: string; // 表名称
  // 表类型: RAW(原始表)|RESULT(结果表)|STANDARD(标准表)|TOPIC(主题表)|ELEMENT(要素表)|RELATION(关系表)|TAG(标签表)|STREAMINGTB(流式表)
  tb_type: 'RAW' | 'RESULT' | 'STANDARD' | 'TOPIC' | 'ELEMENT' | 'RELATION' | 'TAG' | 'STREAMINGTB';
  field_id: string; // 字段ID
  field_name: string; // 字段名称
  field_type: 0 | 1 | 2 | 3; // 字段类型: INT(0)|DOUBLE(1)|STRING(2)|DATETIME(3)
  node_position: 0 | 1 | 2; // 节点位置：上游(0)|自身(1)|下游(2)
  can_extract: 0 | 1; // 是否可展开 1可以展开
  create_time: string; // 创建时间
  count: number; // 数据量
  update_status: 0 | 1 | 2; // 更新状态: 成功(1)|更新中(3)|更新失败(2)
  update_time: string; // 更新时间
  access_mode: string; // 接入方式: 只有原始表有这个字段，tb_type为'RAW'时
  node_type: 0 | 1 | 2 | 3 | 4; // 节点类型: 工作表(0)|流式表(1)|业务算子(2)|图表(3)|API(4)
  tb_storage_flag: 0 | 1 | 2; // 存储类型
  collapsed?: boolean; // 当前操作是收起（true）还是展开（false）
  upperCollapsed?: boolean; // 当前表的上游展开标记
  x?: number;
  y?: number;
  ori_field_name?: string;
  ori_tb_name?: string;
  is_group_node?: number; // 是否是聚合节点
  field_count?: number; // 字段统计数
  is_partition_tb: 0 | 1; // 非分区  分区
}

// 血缘类型：PUSH(推送)|MODEL(模型)|MAP(映射)|TAG(标签)|RULE(规则)|ELEMENT_EXTRA(要素)
export type LineageType = 'PUSH' | 'MODEL' | 'MAP' | 'TAG' | 'RULE';

// 边配置
export interface EdgeConfig {
  source: string;
  target: string;
  lineage_type: LineageType;
}
// 边配置
export interface EdgeMapConfig {
  color: string;
  text: string;
}
// 更新状态
export interface StatusMapConfig {
  color: string;
  text: string;
}
// 表类型
export interface NodeTypeMapConfig {
  color: string; // 文字颜色和中心点的border颜色
  text: string;
  hzName?: string; // hzui icon name
  img?: string;
  imgWhite?: string;
  bgColor?: string; // 标签背景色
  cbg?: string; // 表节点中心点背景色
  fieldCbg?: string; // 字段节点中心点背景色
  fieldbg?: string; // 字段节点背景色-非中心点
  borderShdow?: string; // 描边色
}
// 表类型
export interface TableTypeMapConfig {
  color: string;
  text: string;
}
// 血缘关系数据
export interface BloodDataConfig {
  nodes: NodeConfig[];
  edges: EdgeConfig[];
}
// 字段类型配置
export interface FieldTypeMapConfig {
  img: string;
  imgWhite: string;
  0: string;
  1: string;
  2: string;
}
// 关联模型
export interface ModelListConfig {
  name: string;
  flow_id: string;
  ctime: string;
  utime: string;
  status: number;
}
// 获取血缘数据请求参数
export interface GetBloodDataParams {
  field_id?: string;
  tb_id: string;
  tb_type: string;
  node_position: number;
  node_id?: string;
  is_group_node?: number;
  group_tb_id?: string;
  group_lineage_type?: LineageType; // 聚合的血缘类型：PUSH(推送)|MODEL(模型)|MAP(映射)|TAG(标签)|RULE(规则)
}
// 获取关联模型参数
export interface RelatedModelParams {
  tb_id: string;
  sort_type: string; // null | 'desc' | 'asc';
  sort_field: string;
  type: string; // 表类型
}
