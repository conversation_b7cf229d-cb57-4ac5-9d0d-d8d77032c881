import { Component, ElementRef, OnInit, ViewChild, AfterViewInit, OnDestroy } from '@angular/core';
import { ModelListConfig, StatusMapConfig, RelatedModelParams } from '../blood-graph.model';
import { BloodGraphService } from '../blood-graph.service';
import { TbDetailsService } from '../../tb-details.service';
import { debounceTime, finalize, takeUntil } from 'rxjs/operators';
import { CookieService } from 'ngx-cookie-service';
import { MessageService } from '@haizhi/ng-hertz/message';
import { fromEvent, Subject } from 'rxjs';

@Component({
  selector: 'related-model',
  templateUrl: './related-model.component.html',
  styleUrls: ['./related-model.component.scss']
})
export class RelatedModelComponent implements OnInit, AfterViewInit, OnDestroy {
  @ViewChild('beRelyModel', { static: false }) beRelyModel: ElementRef;
  modelListData: ModelListConfig[] = [];
  scrollHeight = '262px';
  statusMap: { [index: string]: StatusMapConfig } = {
    '-1': {
      color: 'rgba(15, 34, 67, 0.2)',
      text: '删除'
    },
    // 新建
    0: {
      color: '#239545',
      text: '正常'
    },
    1: {
      color: '#239545',
      text: '正常'
    },
    2: {
      color: '#FF5266',
      text: '故障'
    },
    3: {
      color: '#1F71FF',
      text: '更新中'
    }
  };
  getRelatedModelParams: RelatedModelParams = {
    tb_id: '',
    sort_type: '',
    sort_field: '',
    type: ''
  };
  belongData: ModelListConfig = null;
  tbType = '';
  loading = false;
  getBelongLoading = false;
  ctimeSortDesc = true; // 默认向下箭头
  utimeSortDesc = true;
  destroy$ = new Subject();

  constructor(
    private bloodGraphService: BloodGraphService,
    private tbDetailsService: TbDetailsService,
    private cookieService: CookieService,
    private messageService: MessageService
  ) {}

  ngOnInit() {
    this.getRelatedModelParams.tb_id = this.tbDetailsService.curTbId;
    this.tbType = this.tbDetailsService.curTbType;
    this.getRelatedModelParams.type = this.tbType;
    this.getRelatedModel();
    if (this.tbType !== 'RAW') {
      this.getBelongModel();
    }
  }
  ngAfterViewInit(): void {
    this.setScroll();
    fromEvent(window, 'resize')
      .pipe(debounceTime(500), takeUntil(this.destroy$))
      .subscribe(() => {
        this.setScroll();
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  setScroll() {
    setTimeout(() => {
      const ele = this.beRelyModel.nativeElement;
      this.scrollHeight = ele.clientHeight - 41 - 16 + 'px';
    }, 100);
  }

  // 获取关联模型
  getRelatedModel() {
    this.loading = true;
    this.bloodGraphService
      .getRelatedModel(this.getRelatedModelParams)
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(data => {
        this.modelListData = data.data;
        this.setScroll();
      });
  }
  // 获取所属模型
  getBelongModel() {
    this.getBelongLoading = true;
    this.bloodGraphService
      .getBelongModel({
        tb_id: this.tbDetailsService.curTbId
      })
      .pipe(
        finalize(() => {
          this.getBelongLoading = false;
        })
      )
      .subscribe(data => {
        this.belongData = data;
      });
  }
  sortFunc(field: 'ctime' | 'utime') {
    this.getRelatedModelParams.sort_field = field;
    if (field === 'ctime') {
      this.ctimeSortDesc = !this.ctimeSortDesc;
      this.getRelatedModelParams.sort_type = this.ctimeSortDesc ? 'desc' : 'asc';
    } else {
      this.utimeSortDesc = !this.utimeSortDesc;
      this.getRelatedModelParams.sort_type = this.utimeSortDesc ? 'desc' : 'asc';
    }
    this.getRelatedModel();
  }

  // 跳转到模型管理
  goToModelManage(flowId: string, name: string) {
    localStorage.setItem(
      'originalSearchIdInfo',
      JSON.stringify({
        tbName: name,
        tbId: flowId
      })
    );
    // 30天后过期
    this.cookieService.set('curNode', JSON.stringify({ name, flow_id: flowId }), new Date().getTime() + 30 * 24 * 60 * 60 * 1000, '/dmc');
    // 开发结果表/动态表跳转到数据开发中的模型管理，其余类别跳转到数据清洗中的模型管理
    if (this.tbType === 'PROCESS' || this.tbType === 'TOPIC_NEW') {
      window.open('/dmc/#/data-organize/data-develop/model-manage', '_blank');
    } else {
      window.open('/dmc/#/data-process/resource-lib/model-manage', '_blank');
    }
  }

  // 跳转到自主建模或模型管理
  toOrDoraemon(flowId: string, name: string) {
    this.bloodGraphService
      .getModelPermission({
        adv_id: flowId
      })
      .subscribe(data => {
        if (data === 0) {
          this.goToModelManage(flowId, name);
        } else if (data === 1) {
          // window.localStorage.setItem('tb_id', flowId);
          window.open(`/doraemon/index.html?system=etl#/datamodel/model-manage/${flowId}/?modelType=common&original=none`, '_blank');
        } else {
          this.messageService.warning('无权限跳转哦~');
        }
      });
  }
}
