@import '@haizhi/ui/styles/themes/light.data.scss';
.related-model {
  height: 100%;
  display: flex;
  flex-direction: column;
  p {
    margin-bottom: 0;
  }
  .title {
    font-weight: 600;
    font-size: 14px;
    line-height: 20px;
    color: $type-800;
    line-height: 32px;
    margin-bottom: 8px;
  }
  .belong-model {
    > .content {
      box-shadow: $container-c100;
      border-radius: 8px;
      padding: 24px;
      margin-bottom: 24px;
    }
    ul {
      display: flex;
      font-size: 12px;
      line-height: 20px;
      li {
        margin-right: 148px;
        &.model-name {
          .des {
            display: flex;
            hz-tag {
              transform: translateY(-2px);
            }
            span {
              margin-right: 8px;
              text-decoration: underline;
              cursor: pointer;
              color: $type-900;
              max-width: 194px;
              display: inline-block;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              font-weight: normal;
            }
          }
        }
        .des {
          font-weight: 600;
          margin-bottom: 4px;
          color: $type-600;
        }
      }
    }
    .no-data {
      text-align: center;
      color: $type-600;
    }
  }
  .be-rely-model {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;
    .goto-btn {
      text-decoration: underline;
      cursor: pointer;
      color: $type-900;
      &:hover {
        color: $type-800;
      }
    }
    .content {
      flex: 1;
      position: relative;
      height: calc(100% - 40px);
      box-shadow: $container-c100;
      border-radius: 8px;
      > div {
        padding: 8px 16px;
        overflow: auto;
        max-height: 100%;
        &.no-data {
          padding: 24px 16px;
        }
      }
    }
    .sort-icon {
      transition: transform 0.4s;
      cursor: pointer;
      margin-left: 8px;
      &.sort-asc {
        transform: rotate(-180deg);
      }
    }
  }
}
@media screen and (max-height: 800px) {
  .related-model {
    ::ng-deep .hz-icon.hz-empty-icon {
      font-size: 130px !important;
    }
  }
}
@media screen and (max-height: 700px) {
  .related-model {
    ::ng-deep .hz-icon.hz-empty-icon {
      font-size: 104px !important;
    }
  }
}
@media screen and (max-width: 1280px) {
  .related-model .belong-model ul li {
    margin-right: 130px;
  }
}
