<div class="related-model">
  <div class="belong-model" *ngIf="tbType != 'RAW'">
    <p class="title">所属模型</p>
    <div class="content">
      <ul *ngIf="belongData && belongData.name">
        <li class="model-name">
          <p class="des">
            <span
              (click)="goToModelManage(belongData.flow_id, belongData.name)"
              hz-tooltip
              [hzTooltipTitle]="belongData.name"
            >
              {{ belongData.name }}
            </span>
            <hz-tag
              hzSize="tiny"
              [hzTextColor]="statusMap[belongData.status]?.color"
              [hzBorderColor]="statusMap[belongData.status]?.color"
            >
              {{ statusMap[belongData.status]?.text }}
            </hz-tag>
          </p>
          <p class="content">{{ belongData.flow_id }}</p>
        </li>
        <li>
          <p class="des">创建时间</p>
          <p class="content">{{ belongData.ctime }}</p>
        </li>
        <li>
          <p class="des">更新时间</p>
          <p class="content">{{ belongData.utime }}</p>
        </li>
      </ul>
      <div class="no-data" *ngIf="!getBelongLoading && (!belongData || !belongData.name)">
        该表没有所属模型哦～
      </div>
    </div>
  </div>
  <div class="be-rely-model">
    <p class="title">被依赖模型</p>
    <div class="content" #beRelyModel>
      <div *ngIf="modelListData.length > 0">
        <nz-table
          [nzData]="modelListData"
          [nzShowPagination]="false"
          [nzScroll]="{ x: '1022px', y: scrollHeight }"
        >
          <thead>
            <tr>
              <th nzAlign="left" nzWidth="265px">模型名称</th>
              <th nzAlign="left" nzWidth="295px">模型ID</th>
              <th nzAlign="left" nzWidth="154px">
                创建时间
                <i
                  hz-icon
                  hzName="desc-sort-arrow"
                  hz-action-icon
                  class="sort-icon"
                  (click)="sortFunc('ctime')"
                  [ngClass]="{ 'sort-asc': !ctimeSortDesc }"
                ></i>
              </th>
              <th nzAlign="left" nzWidth="154px">
                更新时间
                <i
                  hz-icon
                  hzName="desc-sort-arrow"
                  hz-action-icon
                  class="sort-icon"
                  (click)="sortFunc('utime')"
                  [ngClass]="{ 'sort-asc': !utimeSortDesc }"
                ></i>
              </th>
              <th nzAlign="left" nzWidth="154px">状态</th>
            </tr>
          </thead>
          <tbody>
            <tr *ngFor="let item of modelListData">
              <td>
                <span class="goto-btn" (click)="toOrDoraemon(item.flow_id, item.name)">
                  {{ item.name }}
                </span>
              </td>
              <td>{{ item.flow_id }}</td>
              <td>{{ item.ctime }}</td>
              <td>{{ item.utime }}</td>
              <td>
                <hz-tag
                  hzSize="tiny"
                  [hzTextColor]="statusMap[item.status]?.color"
                  [hzBorderColor]="statusMap[item.status]?.color"
                >
                  {{ statusMap[item.status]?.text }}
                </hz-tag>
              </td>
            </tr>
          </tbody>
        </nz-table>
      </div>
      <hz-empty
        hzEmptyIcon="no-result-light"
        hzEmptyTitle="该表没有被依赖模型哦～"
        *ngIf="!loading && modelListData.length === 0"
      ></hz-empty>
      <hz-loading-gif *ngIf="loading"></hz-loading-gif>
    </div>
  </div>
</div>
