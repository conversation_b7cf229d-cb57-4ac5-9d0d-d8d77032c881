import { Component, OnInit } from '@angular/core';
import { BloodGraphService } from '../blood-graph.service';
import { TbDetailsService } from '../../tb-details.service';
import { FieldConfig, StatisticsParams } from '../blood-graph.model';
import { ActivatedRoute } from '@angular/router';
import { BloodDataConfig, GetBloodDataParams } from '../blood-graph.model';
import { finalize } from 'rxjs/operators';
import { forkJoin } from 'rxjs';

@Component({
  selector: 'field-blood',
  templateUrl: './field-blood.component.html',
  styleUrls: ['./field-blood.component.scss']
})
export class FieldBloodComponent implements OnInit {
  currentField = '';
  fieldArr: FieldConfig[] = [];
  params: GetBloodDataParams = {
    field_id: '',
    tb_id: '',
    tb_type: '',
    node_position: 1,
    node_id: ''
  };
  bloodData: BloodDataConfig = {
    nodes: [],
    edges: []
  };
  returnResult = false;
  loading = false;
  statisticData: {
    [key: string]: {
      title: string;
      count: number;
    };
  } = {
    direct_upstream_cnt: {
      title: '直接上游数', // 直接上游数
      count: 0
    },
    total_upstream_cnt: {
      title: '全部上游数', // 全部上游数
      count: 0
    },
    direct_downstream_cnt: {
      title: '直接下游数', // 直接下游数
      count: 0
    },
    total_downstream_cnt: {
      title: '全部下游数', // 全部下游数
      count: 0
    }
  };
  constructor(private bloodGraphService: BloodGraphService, private tbDetailsService: TbDetailsService, private route: ActivatedRoute) {}

  ngOnInit() {
    this.params.tb_id = this.tbDetailsService.curTbId;
    this.params.tb_type = this.tbDetailsService.curTbType;
    this.getFields();
  }

  getFields() {
    this.bloodGraphService
      .getFields({
        tb_id: this.tbDetailsService.curTbId
      })
      .subscribe(data => {
        this.currentField = this.route.snapshot.queryParamMap.get('fieldId');
        this.fieldArr = data.fields;
        if (!this.currentField) {
          this.currentField = this.fieldArr[0].field_id;
        }
        this.params.field_id = this.currentField;
        this.getData();
      });
  }
  getData() {
    this.loading = true;
    const stsParams: StatisticsParams = {
      tb_id: this.tbDetailsService.curTbId,
      tb_type: this.tbDetailsService.curTbType,
      field_id: this.params.field_id
    };
    const bloodDataGet$ = this.bloodGraphService.getFieldsBloodData(this.params);
    const statisGet$ = this.bloodGraphService.getStatistics(stsParams);
    forkJoin([bloodDataGet$, statisGet$])
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(results => {
        results[0].nodes.forEach(item => {
          if (item.node_position !== 1 && item.can_extract) {
            item.collapsed = true;
          }
        });
        this.bloodData = results[0];
        Object.keys(this.statisticData).forEach(key => {
          this.statisticData[key].count = results[1][key];
        });
      });
  }

  changeField() {
    this.params.field_id = this.currentField;
    this.getData();
  }
}
