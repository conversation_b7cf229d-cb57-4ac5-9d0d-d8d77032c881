<div class="field-blood">
  <div class="header">
    <blood-statistics [statisticData]="statisticData"></blood-statistics>
    <div class="filter-part">
      <span class="title">字段名称</span>
      <hz-select
        class="filter-select"
        [hzSize]="'small'"
        [(ngModel)]="currentField"
        hzPlaceholder="请选择"
        (ngModelChange)="changeField()"
      >
        <hz-option
          *ngFor="let item of fieldArr"
          [hzLabel]="item.title"
          [hzValue]="item.field_id"
        ></hz-option>
      </hz-select>
    </div>
  </div>
  <div class="content">
    <lineage-graph
      [type]="'field'"
      [bloodData]="bloodData"
      [statisticData]="statisticData"
    ></lineage-graph>
    <div class="no-data">
      <hz-empty
        hzEmptyIcon="no-result-light"
        hzEmptyTitle="该表没有血缘关系哦~"
        *ngIf="!(bloodData.nodes && bloodData.nodes.length > 0) && returnResult"
      ></hz-empty>
    </div>
    <hz-loading-gif *ngIf="loading"></hz-loading-gif>
  </div>
  <div class="legend">
    <blood-graph-legend></blood-graph-legend>
  </div>
</div>
