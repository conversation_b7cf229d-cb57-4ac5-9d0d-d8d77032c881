import { Component, OnInit } from '@angular/core';
import { BloodGraphService } from '../blood-graph.service';
import { EdgeMapConfig, NodeTypeMapConfig } from '../blood-graph.model';

@Component({
  selector: 'blood-graph-legend',
  templateUrl: './blood-graph-legend.component.html',
  styleUrls: ['./blood-graph-legend.component.scss']
})
export class BloodGraphLegendComponent implements OnInit {
  edgeData: { [index: string]: EdgeMapConfig } = {};
  nodeTypeData: { [index: number]: NodeTypeMapConfig } = {};
  objectKeys = Object.keys;
  visible = false;
  constructor(private bloodGraphService: BloodGraphService) {}

  ngOnInit() {
    this.edgeData = this.bloodGraphService.edgeMap;
    this.nodeTypeData = this.bloodGraphService.nodeTypeMap;
  }
}
