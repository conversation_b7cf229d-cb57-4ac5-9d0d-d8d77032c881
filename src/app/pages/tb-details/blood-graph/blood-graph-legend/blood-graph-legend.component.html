<button
  hz-button
  hzType="action"
  hzSize="m"
  class="legend-btn"
  hz-popover
  [hzPopoverPlacement]="'topLeft'"
  [hzPopoverTemplate]="legendTpl"
  [hzPopoverTrigger]="'click'"
  [(hzVisible)]="visible"
>
  <i hz-icon hzName="layer-ctrl"></i>
  <span>画布图例</span>
</button>
<ng-template #legendTpl>
  <div class="legend-content">
    <div class="header">
      <span>画布图例</span>
      <i
        hz-icon
        hzName="f-close"
        hzSize="16px"
        class="close"
        (click)="visible = false"
        hz-action-icon
      ></i>
    </div>
    <div class="content">
      <div class="node-type item">
        <span>节点类型</span>
        <ul>
          <ng-container *ngFor="let key of objectKeys(nodeTypeData)">
            <li>
              <i hz-icon style="fill: none" [hzName]="nodeTypeData[key]?.hzName"></i>
              <span>{{ nodeTypeData[key]?.text }}</span>
            </li>
          </ng-container>
        </ul>
      </div>
      <div class="line-type item">
        <span>连线类型</span>
        <ul>
          <li *ngFor="let key of objectKeys(edgeData)">
            <span>{{ edgeData[key]?.text }}</span>
            <span
              class="tag"
              [ngStyle]="{ 'border-color': edgeData[key]?.color, color: edgeData[key]?.color }"
            >
              <label>{{ edgeData[key]?.text }}</label>
            </span>
            <i [ngStyle]="{ 'background-color': edgeData[key]?.color }"></i>
          </li>
        </ul>
      </div>
    </div>
  </div>
</ng-template>
