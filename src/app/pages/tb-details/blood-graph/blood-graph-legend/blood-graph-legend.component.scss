@import '@haizhi/ui/styles/themes/light.data.scss';
.legend-btn {
  background-color: $mono-a100;
  user-select: none;
}
.legend-content {
  padding: 8px 24px 16px;
  background-color: $public-100;
  box-shadow: $container-c100;
  border-radius: 8px;
  width: 264px;
  .header {
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    span {
      font-weight: 600;
      font-size: 14px;
      line-height: 20px;
      color: $type-800;
    }
    .close {
      cursor: pointer;
    }
  }
  .item {
    > span {
      font-weight: 600;
      line-height: 20px;
      color: $type-600;
      display: inline-block;
      margin-bottom: 8px;
    }
    ul {
      li {
        display: inline-block;
        i {
          margin-right: 4px;
        }
        span {
          line-height: 20px;
          color: $type-800;
        }
      }
    }
    &.node-type {
      margin-bottom: 16px;
      ul {
        li {
          margin-right: 24px;
          &:last-child {
            margin-right: 0;
          }
        }
      }
    }
    &.line-type {
      ul {
        li {
          margin-right: 24px;
          position: relative;
          margin-bottom: 8px;
          .tag {
            width: 32px;
            height: 16px;
            background: $white-100;
            border-radius: 16px;
            display: inline-block;
            margin-left: 20px;
            line-height: 16px;
            text-align: center;
            border: 1px solid;
            position: relative;
            z-index: 2;
            label {
              font-size: 12px;
              transform: scale(0.83);
              display: inline-block;
            }
          }
          i {
            position: absolute;
            width: 54px;
            height: 2px;
            top: calc(50% - 2px);
            left: 33px;
            opacity: 0.32;
          }
        }
      }
    }
  }
}
