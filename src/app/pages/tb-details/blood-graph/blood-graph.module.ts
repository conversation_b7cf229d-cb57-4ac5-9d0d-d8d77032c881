import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { BloodGraphComponent } from './blood-graph.component';
import { RouterModule } from '@angular/router';
import { BloodStatisticsComponent } from './blood-statistics/blood-statistics.component';
import { BloodGraphLegendComponent } from './blood-graph-legend/blood-graph-legend.component';
import { SharedModule } from '../../../shared/shared.module';
import { TableBloodComponent } from './table-blood/table-blood.component';
import { FieldBloodComponent } from './field-blood/field-blood.component';
import { RelatedModelComponent } from './related-model/related-model.component';
import { FormsModule } from '@angular/forms';
import { LineageGraphComponent } from './lineage-graph/lineage-graph.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { CookieService } from 'ngx-cookie-service';
@NgModule({
  declarations: [
    BloodGraphComponent,
    BloodStatisticsComponent,
    BloodGraphLegendComponent,
    TableBloodComponent,
    FieldBloodComponent,
    RelatedModelComponent,
    LineageGraphComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild([
      {
        path: '',
        component: BloodGraphComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'table-blood'
          },
          { path: 'table-blood', component: TableBloodComponent },
          { path: 'field-blood', component: FieldBloodComponent },
          { path: 'related-model', component: RelatedModelComponent }
        ]
      }
    ]),
    SharedModule,
    FormsModule,
    NzTableModule
  ],
  providers: [CookieService]
})
export class BloodGraphModule {}
