import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { TableSchema, TableSchemaTypeKeyMap, TableSnapshot } from '../../tb-details.model';
import { TbDetailsService } from '../../tb-details.service';
import { MessageService } from '@haizhi/ng-hertz/message';

@Component({
  selector: 'structure-versions',
  templateUrl: './structure-versions.component.html',
  styleUrls: ['./structure-versions.component.scss']
})
export class StructureVersionsComponent implements OnInit {
  curTbId: string;
  curTbType: string;
  loading = false;
  total: number;
  updateDate: string;
  tbSnapshotList: Array<TableSnapshot>;
  tableSchemaType: TableSchemaTypeKeyMap = {
    0: {
      icon: 'type-number',
      text: '数字'
    },
    1: {
      icon: 'type-number',
      text: '数字'
    },
    2: {
      icon: 'type-string',
      text: '文本'
    },
    3: {
      icon: 'type-date',
      text: '日期'
    },
    4: {
      icon: 'type-blob',
      text: '二进制'
    }
  };

  constructor(private tbDetailsService: TbDetailsService, private messageService: MessageService) {}

  ngOnInit() {
    this.curTbId = this.tbDetailsService.curTbId;
    this.curTbType = this.tbDetailsService.curTbType;
    this.getTbSnapshotList();
  }

  showCurSchemaBox(data: TableSnapshot) {
    data.isShow = !data.isShow;
  }

  trackByIndex(_: number, data: TableSchema): string {
    return data.field_id;
  }

  getTbSnapshotList() {
    this.loading = true;
    this.tbDetailsService
      .getTbSnapshotList(this.curTbId)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(data => {
        for (const item of data.list) {
          item.isShow = false;
          item.isEdit = false;
        }
        this.tbSnapshotList = data.list;
        this.total = data.total;
        this.updateDate = data.utime;
      });
  }

  setSnapshotName(data: TableSnapshot) {
    this.loading = true;
    this.tbDetailsService
      .setTbSnapshotName(data.tb_snapshot_id, data.snapshot_name)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(() => {
        this.messageService.success('修改成功');
        data.isEdit = false;
      });
  }
}
