@import '~@haizhi/ui/styles/themes/light.data';

.table-info-wrap {
  width: 100%;
  margin-top: 16px;
  display: flex;
  padding: 2px 0;
  align-items: center;
  justify-content: space-between;

  .table-info-left {
    .total-number {
      color: $type-700;
      font-size: 12px;
      line-height: 20px;

      .number-text {
        color: $primary-900;
        font-weight: bold;
      }
    }
  }

  .table-info-right {
    display: flex;
    align-items: center;

    .update-time {
      line-height: 20px;
      font-weight: 600;
      font-size: 12px;
      color: $type-600;
      margin-right: 16px;

      .time-text {
        display: inline-block;
        color: $type-800;
        font-weight: normal;
        margin-left: 8px;
      }
    }
  }
}

.table-content-box {
  max-height: calc(100% - 40px);
  margin-top: 8px;
  padding: 0 16px;
  box-sizing: border-box;
  box-shadow: $container-c100;
  border-radius: 8px;
  background: #fff;
  overflow: hidden auto;

  .version-list {
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 8px 0;

    .hide-header-box {
      height: 84px;
      .show-schema-button {
        transform: rotate(0deg);
      }
    }

    .show-header-box {
      height: 364px;
      .show-schema-button {
        transform: rotate(180deg);
      }
    }

    .item {
      transition: height 0.3s;
      overflow: hidden;

      &:first-child {
        .item-header-box {
          border-top: none;
        }
      }

      &-header-box {
        line-height: 20px;
        display: flex;
        padding: 16px 24px;
        border-top: 1px solid $mono-a300;
        position: relative;
        background-color: transparent;

        &:hover {
          background-color: $mono-200;
        }
        .header-item {
          width: 282px;

          .header-title {
            font-weight: 600;
            font-size: 12px;
            color: $type-600;
          }

          .header-value {
            display: flex;
            align-items: center;
            margin-top: 4px;
            height: 28px;
            .value-text {
              max-width: 198px;
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
              font-size: 12px;
              color: $type-800;
              height: 20px;
              line-height: 20px;
            }

            .cur-version-tag {
              font-size: 10px;
              height: 22px;
              line-height: 22px;
              color: rgb(255, 82, 102);
              box-sizing: border-box;
              width: 52px;
              background-color: rgba(255, 82, 102, 0.09);
              border-radius: 6px;
              overflow: hidden;
              vertical-align: middle;
              display: flex;
              justify-content: center;
              align-items: center;
              .tag-text {
                display: inline-block;
                white-space: nowrap;
                transform: scale(0.83);
              }
            }

            input[type='text'] {
              width: 198px;
            }
            .button-box {
              width: 28px;
              height: 28px;
            }
          }
        }
        .tooltip-box {
          width: 32px;
          height: 32px;
          position: absolute;
          right: 24px;
          top: 50%;
          transform: translateY(-50%);
          display: flex;
          .show-schema-button {
            transition: transform 0.3s;
          }
        }
      }

      &-schema-box {
        padding: 8px 0 8px 32px;
        border-top: 1px solid $mono-a300;
        ::ng-deep .ant-table-fixed-header .ant-table-scroll .ant-table-header::-webkit-scrollbar {
          border: none;
        }
        .table-box {
          width: 100%;
          height: 262px;
          padding: 8px 16px;
          background-color: $mono-300;
          border-radius: 8px;
          overflow: hidden;

          ::ng-deep .ant-table-header {
            background-color: transparent;
            overflow: hidden !important;
            margin-bottom: 0 !important;
          }

          ::ng-deep .ant-table-wrapper .ant-table-thead tr th {
            background-color: transparent;
          }

          ::ng-deep .ant-table-body {
            background-color: transparent;
          }

          ::ng-deep .ant-table-placeholder {
            background-color: transparent;
          }
        }
      }
    }
  }
}

.ml-14 {
  margin-left: 14px;
}

.type-text {
  margin-left: 4px;
  color: $type-800;
  font-size: 12px;
}

.button-box {
  display: flex;
  align-items: center;
  justify-content: center;
}

.empty-box {
  position: absolute;
  width: 100%;
  height: calc(100% - 8px);
  .empty-icon {
    position: absolute;
    width: 100%;
    top: 50%;
    transform: translateY(-100%);
  }
}

td {
  position: relative;
  .td-content {
    max-width: calc(100% - 20px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
}
