<ng-container *ngIf="tbSnapshotList && tbSnapshotList.length">
  <div class="table-info-wrap">
    <div class="table-info-left">
      <div class="total-number">
        共
        <span class="number-text">{{ total }}</span>
        条
      </div>
    </div>
    <div class="table-info-right">
      <div class="update-time">
        更新时间
        <span class="time-text">{{ updateDate }}</span>
      </div>
      <button
        hz-button
        hzType="primary"
        [routerLink]="['/version-compare/' + curTbId]"
        [disabled]="tbSnapshotList.length < 2"
      >
        版本对比
      </button>
    </div>
  </div>
  <div class="table-content-box">
    <ul class="version-list">
      <li
        *ngFor="let item of tbSnapshotList"
        class="item"
        [ngClass]="item.isShow ? 'show-header-box' : 'hide-header-box'"
      >
        <div class="item-header-box">
          <div class="header-item mr-24">
            <div class="header-title">版本名称</div>
            <div class="header-value">
              <ng-container *ngIf="!item.isEdit; else editSnapshotNameTpl">
                <div class="value-text" [title]="item.snapshot_name">{{ item.snapshot_name }}</div>
                <div *ngIf="item.current_version" class="cur-version-tag ml8">
                  <span class="tag-text">当前版本</span>
                </div>
                <button
                  *ngIf="curTbType !== 'STANDARD'"
                  class="ml-14"
                  hz-button
                  hzSize="xs"
                  hzType="icon-only"
                  (click)="item.isEdit = true"
                >
                  <i hz-icon hzName="edit"></i>
                </button>
              </ng-container>
              <ng-template #editSnapshotNameTpl>
                <input type="text" hz-input [(ngModel)]="item.snapshot_name" />
                <div class="button-box ml8 mr4">
                  <button hz-button hzSize="s" hzType="icon-only" (click)="setSnapshotName(item)">
                    <i hz-icon hzName="info-ok"></i>
                  </button>
                </div>
                <div class="button-box">
                  <button hz-button hzSize="s" hzType="icon-only" (click)="item.isEdit = false">
                    <i hz-icon hzName="info-cancel"></i>
                  </button>
                </div>
              </ng-template>
            </div>
          </div>
          <div class="header-item">
            <div class="header-title">更新时间</div>
            <div class="header-value">
              <div class="value-text">{{ item.utime }}</div>
            </div>
          </div>
          <div
            class="tooltip-box button-box"
            hz-tooltip
            [hzTooltipTitle]="item.isShow ? '收起' : '展开'"
            hzTooltipPlacement="top"
            (click)="showCurSchemaBox(item)"
          >
            <button
              class="show-schema-button"
              hz-button
              hzSize="m"
              hzType="icon-only"
              [hzIconOnlyDouble]="true"
            >
              <i hz-icon hzName="triangle"></i>
            </button>
          </div>
        </div>
        <div class="item-schema-box">
          <div class="table-box">
            <nz-table
              #virtualTable
              nzVirtualScroll
              [nzVirtualItemSize]="40"
              [nzData]="item.fields"
              [nzVirtualForTrackBy]="trackByIndex"
              [nzFrontPagination]="false"
              [nzShowPagination]="false"
              [nzScroll]="{ x: '946px', y: '212px' }"
            >
              <thead>
                <tr>
                  <th nzWidth="262px">字段名称</th>
                  <th nzWidth="240px">字段类型</th>
                  <th nzWidth="444px">字段描述</th>
                </tr>
              </thead>
              <tbody>
                <ng-template nz-virtual-scroll let-data let-index="index">
                  <tr>
                    <td>{{ data.title }}</td>
                    <td>
                      <i
                        hz-icon
                        [hzName]="tableSchemaType[data.type].icon"
                        hzColor="rgba(31,113,255,1)"
                      ></i>
                      <span class="type-text">{{ tableSchemaType[data.type].text }}</span>
                    </td>
                    <td>
                      <div class="td-content" hz-tooltip [hzTooltipTitle]="data.remark">
                        {{ data.remark }}
                      </div>
                    </td>
                  </tr>
                </ng-template>
              </tbody>
            </nz-table>
          </div>
        </div>
      </li>
    </ul>
  </div>
</ng-container>
<div
  class="table-content-box empty-box"
  *ngIf="!(tbSnapshotList && tbSnapshotList.length > 0) && !loading"
>
  <div class="empty-icon">
    <hz-empty hzEmptyIcon="no-result-light" hzEmptyTitle="该表没有历史版本哦~"></hz-empty>
  </div>
</div>

<hz-loading-gif *ngIf="loading"></hz-loading-gif>
