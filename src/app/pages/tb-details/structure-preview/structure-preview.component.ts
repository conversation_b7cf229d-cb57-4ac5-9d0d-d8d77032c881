import { TbDetailsService } from './../tb-details.service';
import { Component, OnInit } from '@angular/core';
import { TableBaseInfo } from '../tb-details.model';

@Component({
  selector: 'structure-preview',
  templateUrl: './structure-preview.component.html',
  styleUrls: ['./structure-preview.component.scss']
})
export class StructurePreviewComponent implements OnInit {
  tabs = [
    { route: 'current', title: '当前版本' },
    { route: 'versions', title: '历史版本' }
  ];
  tbBaseInfo: TableBaseInfo;
  isHaveTbVersions: boolean;

  constructor(private tbDetailsService: TbDetailsService) {}

  ngOnInit() {
    this.tbBaseInfo = this.tbDetailsService.tbBaseInfo;
    this.isHaveTbVersions = ['RAW', 'RESULT', 'STANDARD'].indexOf(this.tbBaseInfo.type) > -1;
  }
}
