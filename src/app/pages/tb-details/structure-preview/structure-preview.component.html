<div class="tab-content-box">
  <hz-tabs *ngIf="isHaveTbVersions" hzTabsType="auxiliary" [hzTabsRouteMode]="true">
    <ng-container *ngFor="let item of tabs; let i = index">
      <hz-tab [hzTabTitle]="tabTitleTemplate"></hz-tab>
      <ng-template #tabTitleTemplate>
        <a [routerLink]="item.route" routerLinkActive="active" queryParamsHandling="preserve">
          <span>{{ item.title }}</span>
        </a>
      </ng-template>
    </ng-container>
  </hz-tabs>
  <div class="pb16">
    <router-outlet></router-outlet>
  </div>
</div>
