import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { StructurePreviewComponent } from './structure-preview.component';
import { StructureCurrentVersionComponent } from './structure-current-version/structure-current-version.component';
import { StructureVersionsComponent } from './structure-versions/structure-versions.component';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../../shared/shared.module';
import { NzTableModule } from 'ng-zorro-antd/table';
import { FormsModule } from '@angular/forms';
import { BatchSetFieldNameModule } from '../../../ui/batch-set-field-name/batch-set-field-name.module';

@NgModule({
  declarations: [StructurePreviewComponent, StructureCurrentVersionComponent, StructureVersionsComponent],
  imports: [
    CommonModule,
    RouterModule.forChild([
      {
        path: '',
        component: StructurePreviewComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'current'
          },
          {
            path: 'current',
            component: StructureCurrentVersionComponent
          },
          {
            path: 'versions',
            component: StructureVersionsComponent
          }
        ]
      }
    ]),
    FormsModule,
    SharedModule,
    NzTableModule,
    BatchSetFieldNameModule
  ]
})
export class StructurePreviewModule {}
