@import '~@haizhi/ui/styles/themes/light.data';

.table-info-wrap {
  width: 100%;
  margin-top: 16px;
  display: flex;
  padding: 2px 0;
  align-items: center;
  justify-content: space-between;

  .table-info-left {
    display: flex;
    align-items: center;
    .total-number {
      color: $type-700;
      font-size: 12px;
      line-height: 20px;
      .number-text {
        color: $primary-900;
        font-weight: bold;
      }
    }
  }

  .table-info-right {
    display: flex;
    align-items: center;

    .update-time {
      line-height: 20px;
      font-weight: 600;
      font-size: 12px;
      color: $type-600;
      margin-right: 16px;

      .time-text {
        display: inline-block;
        color: $type-800;
        font-weight: normal;
        margin-left: 8px;
      }
    }
  }
}

.table-content-box {
  overflow: hidden;
  // max-height: calc(100% - 84px);
  margin-top: 8px;
  padding: 8px 16px;
  box-sizing: border-box;
  box-shadow: $container-c100;
  border-radius: 8px;
  background: #fff;
  // overflow: hidden auto;
  ::ng-deep .ant-table-tbody > tr > td {
    transition: none;
  }
}

.type-text {
  margin-left: 4px;
  color: $type-800;
  font-size: 12px;
}

input[type='text'] {
  width: 100%;
}

.edit-status {
  height: 41px;
  td {
    transition: none;
    padding: 0 16px;
    .attention {
      float: right;
    }
  }
}

.pagination-box {
  display: flex;
  justify-content: flex-end;
  ::ng-deep .hz-pagination {
    margin-bottom: 0;
  }
}

td {
  position: relative;
  .td-content {
    max-width: calc(100% - 20px);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
  }
}

.number {
  ::ng-deep .hz-select-selection-placeholder {
    color: rgba(21, 22, 24, 0.72);
  }
}
