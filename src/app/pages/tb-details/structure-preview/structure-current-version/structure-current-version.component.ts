import { Component, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild, enableProdMode } from '@angular/core';
import { finalize, startWith, takeUntil, throttleTime } from 'rxjs/operators';
import { TableSchema, TableBaseInfo, TableSchemaTypeKeyMap, APITbModifyParams, DynamicFieldsConfig } from '../../tb-details.model';
import { TbDetailsService } from '../../tb-details.service';
import { MessageService } from '@haizhi/ng-hertz/message';
import { MatDialog } from '@angular/material/dialog';
import { BatchSetFieldNameComponent } from '../../../../ui/batch-set-field-name/batch-set-field-name.component';
import { Subject, fromEvent } from 'rxjs';
import { NzTableComponent } from 'ng-zorro-antd';

@Component({
  selector: 'structure-current-version',
  templateUrl: './structure-current-version.component.html',
  styleUrls: ['./structure-current-version.component.scss']
})
export class StructureCurrentVersionComponent implements OnInit, OnDestroy {
  tbCurrentVersionData: Array<TableSchema> = [];
  tbCurrentAllDataCope: Array<TableSchema> = [];
  dynamicFields: DynamicFieldsConfig = {
    t_head: [],
    t_body: []
  };
  isEdit = false;
  curTbId: string;
  curTbType: string;
  loading = false;
  total: number;
  updateDate: string;
  tbBaseInfo: TableBaseInfo;
  isHaveTbVersions: boolean;
  maxLength = Math.floor((window.innerHeight - 248) / 40);
  dialogCloseData:
    | number
    | {
        [key: string]: string;
      };
  tableSchemaType: TableSchemaTypeKeyMap = {
    0: {
      icon: 'type-number',
      text: '数字'
    },
    1: {
      icon: 'type-number',
      text: '数字'
    },
    2: {
      icon: 'type-string',
      text: '文本'
    },
    3: {
      icon: 'type-date',
      text: '日期'
    },
    4: {
      icon: 'type-blob',
      text: '二进制'
    }
  };
  hasDynFidTbType = ['TOPIC_NEW', 'BENCH', 'TOPIC']; // 含有动态数据元的表类型
  @ViewChild('virtualTable', { static: false }) nzTableComponent: NzTableComponent;
  private destroy$ = new Subject();

  constructor(private tbDetailsService: TbDetailsService, private messageService: MessageService, public dialog: MatDialog) {}

  ngOnInit() {
    this.curTbId = this.tbDetailsService.curTbId;
    this.curTbType = this.tbDetailsService.curTbType;
    this.tbBaseInfo = this.tbDetailsService.tbBaseInfo;
    this.isHaveTbVersions = ['RAW', 'RESULT', 'STANDARD'].indexOf(this.tbBaseInfo.type) > -1;
    this.getTbCurrentVersionData();
    const resize$ = fromEvent(window, 'resize');
    resize$.pipe(startWith(true), throttleTime(40), takeUntil(this.destroy$)).subscribe(() => {
      this.maxLength = Math.floor((window.innerHeight - 248) / 40);
    });
  }

  getTbCurrentVersionData() {
    this.loading = true;
    this.tbDetailsService
      .getTbCurrentVersionData(this.curTbId)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(data => {
        this.dynamicFields = data.fields_property;
        data.fields.forEach(e => {
          if (e.type === 0) {
            e.type = 1;
          }
        });
        // 添加ID 保存修改接口时使用
        for (const item of data.fields) {
          item.tb_id = data.tb_id;
        }
        // 复制一份原始数据 用作对比
        this.tbCurrentAllDataCope = JSON.parse(JSON.stringify(data.fields));
        this.tbCurrentVersionData = data.fields;
        this.total = data.total;
        this.updateDate = data.utime;
      });
  }

  setTbCurrentVersionData() {
    const APIData: Array<APITbModifyParams> = [];
    this.tbCurrentVersionData.forEach((item, i) => {
      // 判断修改的元素并且把相对应的数据组装
      if (JSON.stringify(this.tbCurrentAllDataCope[i]) !== JSON.stringify(item)) {
        if (item.title) {
          APIData.push({
            tb_id: item.tb_id,
            name: this.tbBaseInfo.title,
            fid: item.field_id,
            type: item.type,
            fid_name: item.title,
            remark: item.remark
          });
        }
      }
    });
    if (APIData.length) {
      this.loading = true;
      this.tbDetailsService
        .setTbCurrentVersionData(APIData)
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(() => {
          this.messageService.success('修改成功');
          this.getTbCurrentVersionData();
          this.isEdit = false;
        });
    } else {
      this.isEdit = false;
      this.tbCurrentVersionData = JSON.parse(JSON.stringify(this.tbCurrentAllDataCope));
    }
  }

  cancel() {
    this.tbCurrentVersionData = JSON.parse(JSON.stringify(this.tbCurrentAllDataCope));
    this.isEdit = false;
    this.dialogCloseData = null;
  }

  trackByIndex(_: number, data: TableSchema): string {
    return data.field_id;
  }

  setFieldName() {
    const dialogRef = this.dialog.open(BatchSetFieldNameComponent, {
      width: '500px',
      disableClose: true,
      data: {
        tbId: this.curTbId
      }
    });
    dialogRef.afterClosed().subscribe(data => {
      this.dialogCloseData = data;
      if (typeof data === 'number' && data) {
        if (data === 1) {
          // 将字段描述批量作为新字段名
          this.tbCurrentVersionData.forEach(value => {
            value.title = value.remark;
          });
        } else if (data === 2) {
          // 将原字段名批量作为新字段名
          this.tbCurrentVersionData.forEach(value => {
            value.title = value.name;
          });
        }
      } else if (typeof data === 'object' && data) {
        for (const key in data) {
          if (Object.prototype.hasOwnProperty.call(data, key)) {
            const field = data[key];
            this.tbCurrentVersionData.find(e => e.field_id === key).title = field.title;
            this.tbCurrentVersionData.find(e => e.field_id === key).remark = field.remark;
          }
        }
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }
}
