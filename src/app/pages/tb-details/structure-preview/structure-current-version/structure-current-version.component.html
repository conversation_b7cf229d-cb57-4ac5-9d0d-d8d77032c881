<ng-container *ngIf="tbCurrentVersionData && tbCurrentVersionData.length">
  <div [ngStyle]="!isHaveTbVersions && { 'margin-top': '0' }" class="table-info-wrap">
    <div class="table-info-left">
      <div class="total-number">
        共
        <span class="number-text">{{ total }}</span>
        条
      </div>
      <button class="ml-16" *ngIf="isEdit" hz-button hzType="action" (click)="setFieldName()">
        <i hz-icon hzName="cfg-log"></i>
        <span>批量修改字段名</span>
      </button>
    </div>
    <div class="table-info-right">
      <div class="update-time" *ngIf="tbBaseInfo.type !== 'TOPIC'; else commentTpl">
        更新时间
        <span class="time-text">{{ updateDate }}</span>
      </div>
      <ng-template #commentTpl>
        <div class="update-time">
          建议数据来源
          <span class="time-text" style="width: auto">{{ tbBaseInfo.comment }}</span>
        </div>
      </ng-template>
      <button
        *ngIf="tbBaseInfo && tbBaseInfo.type === 'RAW' && !isEdit"
        hz-button
        hzType="primary"
        (click)="isEdit = true"
      >
        编辑字段
      </button>
      <ng-container *ngIf="isEdit">
        <button hz-button hzType="outline" (click)="cancel()">取消</button>
        <button class="ml8" hz-button hzType="primary" (click)="setTbCurrentVersionData()">
          保存
        </button>
      </ng-container>
    </div>
  </div>
  <div class="table-content-box">
    <!-- [ngStyle]="{
      height:
        tbCurrentVersionData.length < maxLength
          ? tbCurrentVersionData.length * 40 + 16 + 40 + 'px'
          : 'auto'
    }" -->
    <nz-table
      #virtualTable
      nzVirtualScroll
      [nzVirtualItemSize]="40"
      [nzData]="tbCurrentVersionData"
      [nzVirtualForTrackBy]="trackByIndex"
      [nzFrontPagination]="false"
      [nzShowPagination]="false"
      [nzScroll]="{
        y: isHaveTbVersions ? 'calc(100vh - 296px)' : 'calc(100vh - 240px)',
        x: 1002 + (dynamicFields ? dynamicFields.t_head.length * 140 : 0) + 'px'
      }"
    >
      <thead>
        <tr>
          <th nzWidth="224px" *ngIf="curTbType === 'RAW'">字段原始名称</th>
          <th nzWidth="224px">字段名称</th>
          <th nzWidth="140px">字段类型</th>
          <th nzWidth="224px">字段描述</th>
          <ng-container *ngIf="dynamicFields && hasDynFidTbType.indexOf(curTbType) > -1">
            <th nzWidth="140px" *ngFor="let item of dynamicFields.t_head">{{ item }}</th>
          </ng-container>
        </tr>
      </thead>
      <tbody>
        <ng-template nz-virtual-scroll let-data let-index="index">
          <tr [ngClass]="isEdit && 'edit-status'">
            <td *ngIf="curTbType === 'RAW'">
              {{ data.name }}
              <i
                class="attention"
                *ngIf="
                  isEdit &&
                  dialogCloseData &&
                  dialogCloseData !== 1 &&
                  dialogCloseData !== 2 &&
                  !dialogCloseData[data.field_id]
                "
                hz-icon
                hzName="attention"
                hzColor="#FFD604"
              ></i>
            </td>
            <td>
              <ng-container *ngIf="isEdit; else tableSchemaTitleTpl">
                <input type="text" hz-input [(ngModel)]="data.title" />
              </ng-container>
              <ng-template #tableSchemaTitleTpl>
                <div class="td-content" hz-tooltip [hzTooltipTitle]="data.title">
                  {{ data.title }}
                </div>
              </ng-template>
            </td>
            <td>
              <ng-container *ngIf="isEdit; else tableSchemaTypeTpl">
                <hz-select
                  [ngClass]="{
                    number: data.type === 0
                  }"
                  [(ngModel)]="data.type"
                  [hzNoMatchPlaceholder]="data.type === 0 ? '数字' : 'not-fount'"
                  [hzLabelRender]="labelTpl"
                >
                  <ng-container *ngFor="let item of tableSchemaType | keyvalue; let i = index">
                    <hz-option
                      [hzLabel]="item.value.text"
                      [hzValue]="+item.key"
                      *ngIf="item.key !== '0'"
                      [hzData]="item"
                      [hzCustomContent]="optionTpl"
                    >
                      <ng-template #optionTpl>
                        <i
                          class="mr-4"
                          hz-icon
                          [hzName]="tableSchemaType[item.key].icon"
                          hzColor="rgba(31,113,255,1)"
                        ></i>
                        <span>{{ tableSchemaType[item.key].text }}</span>
                      </ng-template>
                    </hz-option>
                  </ng-container>
                </hz-select>
                <ng-template #labelTpl let-data>
                  <i
                    class="mr-4"
                    hz-icon
                    [hzName]="tableSchemaType[data.hzData.key].icon"
                    hzColor="rgba(31,113,255,1)"
                  ></i>
                  <span>{{ tableSchemaType[data.hzData.key].text }}</span>
                </ng-template>
              </ng-container>
              <ng-template #tableSchemaTypeTpl>
                <i
                  hz-icon
                  [hzName]="tableSchemaType[data.type].icon"
                  hzColor="rgba(31,113,255,1)"
                ></i>
                <span class="type-text">{{ tableSchemaType[data.type].text }}</span>
              </ng-template>
            </td>
            <td>
              <ng-container *ngIf="isEdit; else tableSchemaRemarkTpl">
                <input type="text" hz-input [(ngModel)]="data.remark" />
              </ng-container>
              <ng-template #tableSchemaRemarkTpl>
                <div class="td-content" hz-tooltip [hzTooltipTitle]="data.remark">
                  {{ data.remark }}
                </div>
              </ng-template>
            </td>
            <ng-container *ngIf="dynamicFields && hasDynFidTbType.indexOf(curTbType) > -1">
              <td nzWidth="140px" *ngFor="let item of dynamicFields.t_body[index]">{{ item }}</td>
            </ng-container>
          </tr>
        </ng-template>
      </tbody>
    </nz-table>
  </div>
</ng-container>
<hz-loading-gif *ngIf="loading"></hz-loading-gif>
