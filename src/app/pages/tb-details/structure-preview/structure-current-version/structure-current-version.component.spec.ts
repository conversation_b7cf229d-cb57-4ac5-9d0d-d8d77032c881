import { async, ComponentFixture, TestBed } from '@angular/core/testing';

import { StructureCurrentVersionComponent } from './structure-current-version.component';

describe('StructureCurrentVersionComponent', () => {
  let component: StructureCurrentVersionComponent;
  let fixture: ComponentFixture<StructureCurrentVersionComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [StructureCurrentVersionComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(StructureCurrentVersionComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
