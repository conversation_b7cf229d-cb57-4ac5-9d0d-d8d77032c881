export type TbInfoTabsType = 'data-preview' | 'structure' | 'blood-info' | 'log' | 'advanced' | 'file-manage' | 'subregion';

type TableBaseInfoTbType = 0 | 1 | 2;
type TableBaseInfoTbTypeName = '普通表' | '在线表' | '流式表';
export type TableBaseInfoTbTypeKeyMap = {
  [key in TableBaseInfoTbType]: TableBaseInfoTbTypeName;
};

// RAW(原始表)|RESULT(结果表)|STANDARD(标准表)|TOPIC(主题表)|ELEMENT(要素表)|RELATION(关系表)|TAG(标签表)| STREAMINGTB(流式表)
export type TableBaseInfoType =
  | 'RAW'
  | 'RESULT'
  | 'STANDARD'
  | 'BENCH'
  | 'TOPIC'
  | 'ELEMENT'
  | 'RELATION'
  | 'TAG'
  | 'STREAMINGTB'
  | 'TOPIC_NEW'
  | 'RESOURCE'
  | 'PROCESS';
type TableBaseInfoTypeName =
  | '原始库'
  | '清洗结果库'
  | '业务标准库'
  | '专题标准库'
  | '标准库'
  | '主题库'
  | '实体库'
  | '关系库'
  | '标签库'
  | '流式库'
  | '部标专题库'
  | '资源库'
  | '开发结果库';
export type TableBaseInfoTypeKeyMap = {
  [key in TableBaseInfoType]: TableBaseInfoTypeName;
};

type TableBaseInfoUpdateMode = 0 | 1 | 2 | 3 | 4;
type TableBaseInfoUpdateModeName = '自定义更新' | '定时更新' | '自动更新' | '暂停更新' | '实时更新';
export type TableBaseInfoUpdateModeKeyMap = {
  [key in TableBaseInfoUpdateMode]: TableBaseInfoUpdateModeName;
};

type TableBaseInfostorageType = 1 | 2 | 3;
type TableBaseInfostorageTypeName = '非分区' | '分区表' | '分区表';
export type TableBaseInfostorageTypeKeyMap = {
  [key in TableBaseInfostorageType]: TableBaseInfostorageTypeName;
};

type TableBaseInfoUpdateType = 0 | 1;
type TableBaseInfoUpdateTypeName = '增量' | '全量';
export type TableBaseInfoUpdateTypeKeyMap = {
  [key in TableBaseInfoUpdateType]: TableBaseInfoUpdateTypeName;
};

type TableBaseInfoUpdateStatus = 0 | 1 | 2 | 3 | 6 | 7;
type TagTextColorName = '#239545' | '#FF5266' | '#1F71FF' | '#FF9431' | 'rgba(15, 34, 67, 0.36)';
type TagTextName = '新建' | '更新成功' | '更新失败' | '正在更新' | '暂未映射';
export type TableBaseInfoUpdateStatusKeyMap = {
  [key in TableBaseInfoUpdateStatus]: {
    color: TagTextColorName;
    text: TagTextName;
  };
};

// 表基本信息
export interface TableBaseInfo {
  title: string; // 表名称
  tb_type: TableBaseInfoTbType; // 表类型 0数据表 1流式表
  type: TableBaseInfoType; // RAW(原始表)|RESULT(结果表)|STANDARD(标准表)|TOPIC(主题表)|ELEMENT(要素表)|RELATION(关系表)|TAG(标签表)| STREAMINGTB(流式表)
  comment: string; // 表描述
  tb_origin_name?: string; // 表原始名称
  tb_code?: string; // 表编码
  category?: string; // 分类
  config_type?: 0 | 1 | 2 | 3 | 4 | 5; // 配置方式 0、1、2 3、模型  4、数据关系 5、聚合关系  仅显示标签、关系
  sync_time?: string; // 接入时间
  ctime?: string; // 创建时间
  tb_id: string; // 表ID
  schedule_config: {
    mode: TableBaseInfoUpdateMode; // 更新方式 0新建、1更新成功、2更新失败、3正在更新
    sync_config: string; // 更新周期
  };
  update_type: TableBaseInfoUpdateType; // 更新类型 0 增量、1 全量
  update_status: TableBaseInfoUpdateStatus; // 更新状态  error失败、finished成功、syncing更新中、terminated停止
  last_update_time: string; // 最后更新时间
  last_used_time: string; // 最后一次使用时间
  origin_type?: TableBaseInfoOriginType;
  source_path: Array<{ name: string }>; // 表的路径，面包屑需要
  asset_source_path: Array<{ name: string }>; // 除原始表加入的资产目录
  relation_type_id: string; // 关系类型
  storage_type?: number; // 3 为分区表
  data_count?: number; // 数据量
  latest_partition?: string; // 最晚分区
  first_partition?: string; // 最早分区
  asset_folder_path?: string[];
  tb_storage_flag?: number;
}

export type TableBaseInfoOriginType =
  | 'excel'
  | 'adv_view'
  | 'opends'
  | 'view'
  | 'ds'
  | 'flow'
  | 'streamingkafka'
  | 'streamingtb'
  | 'streaming'
  | 'streamingdatahub';
type TableSchemaType = 0 | 1 | 2 | 3 | 4;
type TableSchemaIconName = 'type-number' | 'type-string' | 'type-blob' | 'type-date';
type TableSchemaTextName = '数字' | '数字' | '文本' | '日期' | '二进制';
export type TableSchemaTypeKeyMap = {
  [key in TableSchemaType]: {
    icon: TableSchemaIconName;
    text: TableSchemaTextName;
  };
};

// 表结构
export interface TableSchema {
  tb_id?: string; // 表ID
  remark: string; // 字段描述
  name: string; // 原始名称（不可修改）
  title: string; // 名称
  field_id: string; // 字段id
  real_type: null;
  seq_no: number;
  uniq_index: number;
  type: TableSchemaType; // 字段类型 0 1 number 2 string 3 dateTime
}

// 保存编辑完的当前版本需要的参数
export interface APITbModifyParams {
  tb_id: string;
  name: string;
  fid: string;
  type: TableSchemaType;
  fid_name: string;
  remark: string;
}

// 历史版本
export interface TableSnapshot {
  snapshot_name: string; // 版本名称
  utime: string; // 更新时间
  current_version: boolean; // 是否当前版本
  fields: Array<TableSchema>;
  tb_snapshot_id: string; // 版本ID
  history_version: string; // 版本号
  isShow?: boolean; // 列表是否展示
  isEdit?: boolean; // 是否编辑
  isDisabled?: boolean; // 版本对比是否禁止选择
}

// 文件管理
export interface ExcelFileList {
  ctime: string;
  excel_id: string;
  excel_name: string;
  is_download: number;
  map_id: string;
  owner: string;
  sheet_name: string;
  status: number;
  tb_id: string;
  utime: string;
}
// 文件管理-列表返回
export interface ExcelFileResultConfig {
  file_list: FileContentConfig;
  op_info?: any; // 页面拆开了但是用的是老接口，新页面该字段废弃
  op_log?: any; // 废弃
  history_file: FileContentConfig;
}
// 文件管理-列表返回
export interface FileContentConfig {
  content?: ExcelFileList[];
  cur_page?: number;
  pages_count?: number;
}

// 获取当前tb表结构api返回数据
export interface APITbEditableSchemaResponse {
  fields: Array<TableSchema>;
  tb_id: string;
  is_sorted: number;
  total: number;
  utime: string;
  fields_property?: DynamicFieldsConfig;
}
export interface DynamicFieldsConfig {
  t_head: string[];
  t_body: string[];
}
export interface ElementMapListRes {
  tb_id: string;
  title: string;
  config: ElementConfig[];
  event_config: EventConfig;
  fields: Fields[];
}
export interface ElementConfig {
  element_info?: {
    tb_id: string; // 要素表id
    tb_name?: string;
    type?: string;
    title?: string;
  };
  identify_fields?: string[];
  show_fields: string[];
  conf_id?: string;
}
export interface EventConfig {
  config_id?: string;
  dep_tb_field?: string[];
  show_fields?: string[];
}
export interface Fields {
  field_id: string;
  tb_id: string;
  title: string;
}
