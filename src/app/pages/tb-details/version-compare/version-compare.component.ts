import { finalize } from 'rxjs/operators';
import { TbDetailsService } from './../tb-details.service';
import { Component, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Location } from '@angular/common';
import { TableSchemaTypeKeyMap, TableSnapshot } from '../tb-details.model';

@Component({
  selector: 'version-compare',
  templateUrl: './version-compare.component.html',
  styleUrls: ['./version-compare.component.scss']
})
export class VersionCompareComponent implements OnInit {
  curTbId: string;
  leftVisible: boolean;
  rightVisible: boolean;
  loading = false;
  versionList: Array<TableSnapshot>;
  fromTbSnapshotId = '';
  toTbSnapshotId = '';
  fromTbSnapshot: TableSnapshot;
  toTbSnapshot: TableSnapshot;
  originSnapshot: TableSnapshot;
  tableSchemaType: TableSchemaTypeKeyMap = {
    0: {
      icon: 'type-number',
      text: '数字'
    },
    1: {
      icon: 'type-number',
      text: '数字'
    },
    2: {
      icon: 'type-string',
      text: '文本'
    },
    3: {
      icon: 'type-date',
      text: '日期'
    },
    4: {
      icon: 'type-blob',
      text: '二进制'
    }
  };

  constructor(private route: ActivatedRoute, private location: Location, private tbDetailsService: TbDetailsService) {}

  ngOnInit() {
    this.curTbId = this.route.snapshot.paramMap.get('tbId');
    this.getTbSnapshotList();
  }

  goBack() {
    this.location.back();
  }

  onLeftTableClick(item: TableSnapshot) {
    this.leftVisible = false;
    this.fromTbSnapshotId = item.tb_snapshot_id;
    this.setIsDisabled();
  }

  onRightTableClick(item: TableSnapshot) {
    this.rightVisible = false;
    this.toTbSnapshotId = item.tb_snapshot_id;
    this.setIsDisabled();
  }

  setIsDisabled() {
    this.versionList.forEach(e => {
      e.isDisabled = this.isDisabled(e);
    });
    this.getTbVersionCompare(this.fromTbSnapshotId, this.toTbSnapshotId);
  }

  isDisabled(item: TableSnapshot) {
    return [this.fromTbSnapshotId, this.toTbSnapshotId].indexOf(item.tb_snapshot_id) > -1;
  }

  getTbSnapshotList() {
    this.loading = true;
    this.tbDetailsService
      .getTbSnapshotList(this.curTbId)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(data => {
        this.versionList = data.list;
        this.fromTbSnapshotId = this.versionList[0].tb_snapshot_id;
        this.toTbSnapshotId = this.versionList[1].tb_snapshot_id;
        this.setIsDisabled();
      });
  }

  getTbVersionCompare(fromTbSnapshotId: string, toTbSnapshotId: string) {
    this.loading = true;
    this.tbDetailsService
      .getTbVersionCompare(fromTbSnapshotId, toTbSnapshotId)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(data => {
        this.fromTbSnapshot = data.from_snapshot;
        this.toTbSnapshot = data.to_snapshot;
        this.originSnapshot = data.origin_snapshot;
      });
  }
}
