@import '~@haizhi/ui/styles/themes/light.data';

.version-compare {
  max-height: calc(100% - 56px);
  overflow: hidden auto;

  .container {
    padding: 24px 78px 38px;
    box-sizing: border-box;

    .table-content {
      width: 100%;

      .table-box {
        width: 100%;

        thead {
          tr {
            th {
              height: 48px;

              .table-title {
                width: 100%;
                height: 100%;
                border-radius: 8px 8px 0px 0px;
                background-color: rgba(150, 70, 255, 0.09);
                box-shadow: $container-c200;
                padding: 8px 0 8px 16px;
                box-sizing: border-box;

                .title-box-active {
                  background-color: $mono-a200 !important;

                  .triangle-icon {
                    i {
                      transform: rotate(180deg);
                    }
                  }
                }

                .title-box {
                  background-color: transparent;
                  height: 34px;
                  border-radius: 6px;
                  display: flex;
                  align-items: center;
                  cursor: pointer;
                  user-select: none;
                  padding: 0 6px;
                  float: left;
                  transition: background-color 0.3s;

                  &:hover {
                    background-color: $mono-a200;
                  }

                  .title-name {
                    line-height: 22px;
                    height: 22px;
                    font-weight: 600;
                    font-size: 13px;
                    color: $type-800;
                  }

                  .cur-version-tag {
                    font-size: 12px;
                    height: 22px;
                    line-height: 22px;
                    color: rgb(255, 82, 102);
                    box-sizing: border-box;
                    width: 52px;
                    background-color: rgba(255, 82, 102, 0.09);
                    border-radius: 6px;
                    overflow: hidden;
                    vertical-align: middle;
                    text-align: center;

                    .tag-text {
                      display: inline-block;
                      white-space: nowrap;
                      transform: scale(0.83);
                    }
                  }

                  .triangle-icon {
                    width: 16px;
                    height: 16px;
                    display: flex;

                    i {
                      transition: transform 0.3s;
                    }
                  }
                }
              }

              &:first-child {
                .table-title {
                  background-color: $primary-a100;
                }
              }

              &:last-child {
                .table-title {
                  background-color: #ffeff1;
                }
              }
            }
          }
        }

        tbody {
          tr {
            td {
              box-shadow: $container-c200;

              .data-box {
                width: 100%;
                box-sizing: border-box;
                padding: 0 16px;
                font-size: 12px;
                line-height: 20px;
                color: $type-900;
                height: 100%;
                text-align: left;
              }

              .empty-data {
                text-align: left;
                font-size: 12px;
                line-height: 20px;
                color: $type-700;
                padding: 10px 16px;
              }

              .field-box {
                width: 100%;
                box-sizing: border-box;
                padding: 10px 16px;
                line-height: 20px;
                text-align: left;

                .title {
                  .title-text {
                    line-height: 20px;
                    font-weight: 600;
                    font-size: 12px;
                    color: $type-800;
                    margin-left: 4px;
                  }
                }

                .remark {
                  margin-top: 8px;
                  font-weight: normal;
                  font-size: 12px;
                  color: $type-700;
                  display: -webkit-box;
                  -webkit-box-orient: vertical;
                  -webkit-line-clamp: 2;
                  overflow: hidden;
                }
              }

              &:nth-of-type(2n) {
                box-shadow: none;
              }
            }

            &:nth-child(odd) {
              td {
                background-color: #fff;

                &:nth-of-type(2n) {
                  background-color: transparent;
                }
              }
            }

            &:nth-child(even) {
              td {
                background-color: $mono-200;

                &:nth-of-type(2n) {
                  background-color: transparent;
                }
              }
            }
          }
        }
      }
    }
  }
}

.disabled {
  ::ng-deep .hz-menu-item {
    cursor: not-allowed !important;
    color: rgb(170, 170, 170);

    &:hover {
      background-color: transparent !important;
    }
  }
}

.version-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  vertical-align: middle;
}

.cur-version {
  display: inline-block;
  vertical-align: middle;
}

.menu-list {
  ::ng-deep .hz-menu-item {
    white-space: normal;
    text-overflow: clip;
  }
}
