<back-bar title="版本对比" (backIconClick)="goBack()"></back-bar>
<div class="version-compare" *ngIf="fromTbSnapshot && toTbSnapshot">
  <div class="container">
    <div class="table-content">
      <table class="table-box">
        <thead>
          <tr>
            <th width="220px">
              <div class="table-title">
                <div class="title-box">
                  <div class="title-name mr-4">原始名称</div>
                </div>
              </div>
            </th>
            <th width="16px"></th>
            <th width="480px">
              <div class="table-title">
                <div
                  class="title-box"
                  hz-popover
                  [hzPopoverTemplate]="leftTpl"
                  [(hzVisible)]="leftVisible"
                  hzPopoverPlacement="bottomLeft"
                  hzPopoverTrigger="click"
                  [ngClass]="{ 'title-box-active': leftVisible }"
                >
                  <div class="title-name mr-4">{{ fromTbSnapshot.snapshot_name }}</div>
                  <div class="cur-version-tag" *ngIf="fromTbSnapshot.current_version">
                    <span class="tag-text">当前版本</span>
                  </div>
                  <div class="triangle-icon ml-4">
                    <i hz-icon hzName="triangle"></i>
                  </div>
                </div>
                <ng-template #leftTpl>
                  <hz-menu-list class="menu-list" hzMaxHeight="180px" hzWidth="260px">
                    <hz-menu-item
                      *ngFor="let item of versionList"
                      [ngClass]="{ disabled: item.isDisabled }"
                      (click)="!item.isDisabled && onLeftTableClick(item)"
                    >
                      <div
                        [ngStyle]="{
                          'max-width': item.current_version ? '150px' : '101%'
                        }"
                        class="version-name"
                      >
                        {{ item.snapshot_name }}
                      </div>
                      <div *ngIf="item.current_version" class="cur-version">(当前版本)</div>
                    </hz-menu-item>
                  </hz-menu-list>
                </ng-template>
              </div>
            </th>
            <th width="16px"></th>
            <th width="480px">
              <div class="table-title">
                <div
                  class="title-box"
                  hz-popover
                  [hzPopoverTemplate]="rightTpl"
                  [(hzVisible)]="rightVisible"
                  hzPopoverPlacement="bottomLeft"
                  hzPopoverTrigger="click"
                  [ngClass]="{ 'title-box-active': rightVisible }"
                >
                  <div class="title-name mr-4">{{ toTbSnapshot.snapshot_name }}</div>
                  <div class="cur-version-tag" *ngIf="toTbSnapshot.current_version">
                    <span class="tag-text">当前版本</span>
                  </div>
                  <div class="triangle-icon ml-4">
                    <i hz-icon hzName="triangle"></i>
                  </div>
                </div>
                <ng-template #rightTpl>
                  <hz-menu-list class="menu-list" hzMaxHeight="180px" hzWidth="260px">
                    <hz-menu-item
                      *ngFor="let item of versionList"
                      [ngClass]="{ disabled: item.isDisabled }"
                      (click)="!item.isDisabled && onRightTableClick(item)"
                    >
                      <div
                        [ngStyle]="{
                          'max-width': item.current_version ? '150px' : '101%'
                        }"
                        class="version-name"
                      >
                        {{ item.snapshot_name }}
                      </div>
                      <div *ngIf="item.current_version" class="cur-version">(当前版本)</div>
                    </hz-menu-item>
                  </hz-menu-list>
                </ng-template>
              </div>
            </th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let item of originSnapshot.fields; let i = index">
            <td>
              <div class="field-box" *ngIf="item && item.type; else emptyDataTpl">
                <div class="title">
                  <i
                    hz-icon
                    [hzName]="tableSchemaType[item.type].icon"
                    hzColor="rgba(31,113,255,1)"
                  ></i>
                  <span class="title-text">{{ item.name }}</span>
                </div>
              </div>
              <ng-template #emptyDataTpl>
                <span class="empty-data">无</span>
              </ng-template>
            </td>
            <td></td>
            <td>
              <div
                class="field-box"
                *ngIf="fromTbSnapshot.fields[i] && fromTbSnapshot.fields[i].type; else emptyDataTpl"
              >
                <div class="title">
                  <i
                    hz-icon
                    [hzName]="tableSchemaType[fromTbSnapshot.fields[i].type].icon"
                    hzColor="rgba(31,113,255,1)"
                  ></i>
                  <span class="title-text">
                    {{ fromTbSnapshot.fields[i].title }}
                  </span>
                </div>
                <div class="remark" *ngIf="fromTbSnapshot.fields[i].remark">
                  {{ fromTbSnapshot.fields[i].remark }}
                </div>
              </div>
              <ng-template #emptyDataTpl>
                <span class="empty-data">无</span>
              </ng-template>
            </td>
            <td></td>
            <td>
              <div
                class="field-box"
                *ngIf="toTbSnapshot.fields[i] && toTbSnapshot.fields[i].type; else emptyDataTpl"
              >
                <div class="title">
                  <i
                    hz-icon
                    [hzName]="tableSchemaType[toTbSnapshot.fields[i].type].icon"
                    hzColor="rgba(31,113,255,1)"
                  ></i>
                  <span class="title-text">
                    {{ toTbSnapshot.fields[i].title }}
                  </span>
                </div>
                <div class="remark" *ngIf="toTbSnapshot.fields[i].remark">
                  {{ toTbSnapshot.fields[i].remark }}
                </div>
              </div>
              <ng-template #emptyDataTpl>
                <span class="empty-data">无</span>
              </ng-template>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</div>
<hz-loading-gif *ngIf="loading"></hz-loading-gif>
