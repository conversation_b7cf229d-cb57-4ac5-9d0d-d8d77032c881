import { BackBarModule } from './../../../ui/back-bar/back-bar.module';
import { VersionCompareComponent } from './version-compare.component';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { SharedModule } from '../../../shared/shared.module';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
@NgModule({
  declarations: [VersionCompareComponent],
  imports: [
    CommonModule,
    RouterModule.forChild([
      {
        path: '',
        component: VersionCompareComponent
      }
    ]),
    SharedModule,
    FormsModule,
    BackBarModule
  ]
})
export class VersionCompareModule {}
