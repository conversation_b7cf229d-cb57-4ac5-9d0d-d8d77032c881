import { finalize, takeUntil } from 'rxjs/operators';
import { Component, OnDestroy, OnInit } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { TableBaseInfo, TableBaseInfoType } from './tb-details.model';
import { TbDetailsService } from './tb-details.service';
import { OfflineTaskWsInfo, WebsocketService } from '../../core/services/websocket.service';
import { NotificationService } from '@haizhi/ng-hertz/notification';
import { Subject } from 'rxjs';
import { MatDialog } from '@angular/material/dialog';
import { BdpOfflineTaskComponent } from '../../ui/bdp-offline-task/bdp-offline-task.component';
import { UserInfoService } from '../../core/services/user-info.service';

@Component({
  selector: 'hz-tb-details',
  templateUrl: './tb-details.component.html',
  styleUrls: ['./tb-details.component.scss']
})
export class TbDetailsComponent implements OnInit, OnDestroy {
  curTbId: string;
  tbType: TableBaseInfoType;
  tableBaseInfo: TableBaseInfo;
  isTbDetailsLeftShow = true;
  loading = false;
  tabs = [
    { route: 'data-preview', title: '数据预览', show: true },
    { route: 'structure', title: '结构预览', show: true },
    { route: 'element-info', title: '要素信息', show: false },
    { route: 'subregion', title: '分区信息', show: false },
    { route: 'config-overview', title: '配置概况', show: false },
    { route: 'blood-info', title: '血缘信息', show: true },
    { route: 'auth-overview', title: '服务概况', show: false },
    { route: 'log', title: '日志', show: true },
    { route: 'advanced', title: '高级设置', show: false },
    { route: 'file-manage', title: '版本记录', show: false },
    { route: 'version-record', title: '版本记录', show: false },
    { route: 'stream-table-task', title: '流式任务', show: false }
  ];
  destroy$ = new Subject<void>();
  constructor(
    private route: ActivatedRoute,
    private tbDetailsService: TbDetailsService,
    private websocket: WebsocketService,
    private notificationService: NotificationService,
    private dialog: MatDialog,
    private userInfoService: UserInfoService
  ) {}

  ngOnInit() {
    this.tbDetailsService.curTbId = this.route.snapshot.paramMap.get('tbId');
    this.curTbId = this.tbDetailsService.curTbId;
    this.tbDetailsService.curTbType = this.route.snapshot.queryParamMap.get('tbType') as TableBaseInfoType;
    this.tbType = this.tbDetailsService.curTbType;
    this.getTbBaseInfo();
    this.websocket.listener.pipe(takeUntil(this.destroy$)).subscribe((value: any) => {
      if (value.type === 2) {
        // 显示离线任务通知弹层
        // TODO: 后端返回数据，无法判断是数据表的 格式校验 还是 导出数据，暂时通过模块区分
        const title = '格式校验';
        const type = '格式校验导出';
        const info: OfflineTaskWsInfo = value;
        if (info.status === 0) {
          const notice = this.notificationService.success(title, info.filename + type + '成功！', { duration: 6000 });
          notice.onCheck.subscribe(check => {
            this.openOfflineTask();
          });
        } else if (info.status === 2) {
          const notice = this.notificationService.error(title, info.filename + type + '失败！', { duration: 6000 });
          notice.onCheck.subscribe(check => {
            this.openOfflineTask();
          });
        }
      }
    });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  getTbBaseInfo() {
    this.loading = true;
    this.tbDetailsService
      .getTbBaseInfo(this.curTbId, this.tbType)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(
        data => {
          this.tableBaseInfo = data;
          this.tbDetailsService.tbBaseInfo = data;
          this.userInfoService.breadcrumb$.next(data.source_path);
          this.setTabs();
        },
        err => {
          console.log('getTbBaseInfo-err:', err);
        }
      );
  }

  /**
   * 根据表的属性，设置 tabs 展示
   */
  setTabs() {
    this.tabs.forEach(item => {
      // 配置概况【关系表/标签表】
      if ((this.tbType === 'RELATION' || this.tbType === 'TAG') && item.route === 'config-overview') {
        item.show = true;
      }
      // 授权概况【标准库标准表/标准库专题标准表/主题表/关系表/标签表/动态表参考主题表】
      const authCondition = ['STANDARD', 'BENCH', 'TOPIC', 'RELATION', 'TAG', 'TOPIC_NEW', 'ELEMENT'];
      if (authCondition.indexOf(this.tbType) > -1 && item.route === 'auth-overview') {
        item.show = true;
      }
      // 要素信息
      if ((this.tbType === 'STANDARD' || this.tbType === 'TOPIC') && item.route === 'element-info') {
        item.show = true;
      }
      // 高级设置【原始表/清洗结果表/开发结果表】
      // if (
      //   ((this.tbType !== 'RAW' && this.tableBaseInfo.origin_type !== 'streaming') ||
      //     ((this.tbType === 'RESULT' || this.tbType === 'PROCESS') && this.tableBaseInfo.origin_type !== 'streamingkafka')) &&
      //   item.route === 'advanced'
      // ) {
      //   item.show = true;
      // }
      // 流式任务【清洗结果表 且 origin_type 为 streamingkafka
      // if (this.tbType === 'RESULT' && this.tableBaseInfo.origin_type === 'streamingkafka' && item.route === 'stream-table-task') {
      //   item.show = true;
      // }
      // 判断显示流式表的流式任务tab
      const streamArr = ['streamingkafka', 'streaming', 'streamingdatahub'];
      if (
        streamArr.indexOf(this.tableBaseInfo.origin_type) > -1 &&
        this.tbType !== 'RAW' &&
        item.route === 'stream-table-task' &&
        this.tbType !== 'STANDARD'
      ) {
        item.show = true;
      }
      // 文件管理 【原始表】且 origin_type 为 excel
      if (
        this.tableBaseInfo.origin_type === 'excel' &&
        (this.tbType === 'RAW' || this.tbType === 'STANDARD') &&
        item.route === 'file-manage'
      ) {
        item.show = true;
      }
      // 文件管理 【原始表】且 origin_type 为 excel
      if (
        this.tableBaseInfo.origin_type === 'flow' &&
        (this.tbType === 'RESULT' || this.tbType === 'STANDARD') &&
        item.route === 'version-record' &&
        this.tableBaseInfo.storage_type !== 3
      ) {
        item.show = true;
      }
      // 分区信息
      if (item.route === 'subregion' && this.tableBaseInfo.storage_type === 3) {
        item.show = true;
      }
      // DMC元数据 除原始库 其他库不显示授权概况
      // if (this.tbType !== 'RAW' && item.title === '授权概况') {
      //   item.show = false;
      // }
    });
  }

  // 打开离线任务列表弹窗
  openOfflineTask() {
    this.dialog.open(BdpOfflineTaskComponent, {
      width: '844px',
      disableClose: true,
      data: {}
    });
  }

  clickShowTabs() {
    this.isTbDetailsLeftShow = !this.isTbDetailsLeftShow;
    this.tbDetailsService.subjectChartResize.next(this.isTbDetailsLeftShow);
  }
}
