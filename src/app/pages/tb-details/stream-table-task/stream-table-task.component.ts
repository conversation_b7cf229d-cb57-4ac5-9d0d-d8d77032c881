import { Component, Input, OnInit } from '@angular/core';
import { MatDialog } from '@angular/material';
import { Router } from '@angular/router';
import * as _ from 'lodash';
import { HttpService } from '../../../core/services/http.service';
import { finalize } from 'rxjs/operators';
import { ConfirmDialogComponent } from '../../../ui/common-dialog/dialog-confirm.component';
import { CreateStreamTableTaskDialogComponent } from './create-stream-table-task/create-stream-table-task.component';
import { TbDetailsService } from '../tb-details.service';

@Component({
  selector: 'stream-table-task',
  templateUrl: './stream-table-task.component.html',
  styleUrls: ['./stream-table-task.component.scss']
})
export class StreamTableTaskComponent implements OnInit {
  tbId: string;
  loading = false;
  tasks: Array<any> = [];
  taskNum = 0;
  triggerTimeS = 0;
  typeMap = {
    0: 'MySql',
    3: 'Http',
    4: 'Kafka',
    5: 'DataHub',
    6: 'RDS'
  };

  constructor(private http: HttpService, private router: Router, private dialog: MatDialog, private tbDetailsService: TbDetailsService) {}

  ngOnInit() {
    this.tbId = this.tbDetailsService.curTbId;
    this.getTaskList();
  }

  getTaskList() {
    this.loading = true;
    this.http
      .post('/api/job/stream_list', { tb_id: this.tbId, is_streaming: 1 })
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(result => {
        this.tasks = result.sinks;
        this.triggerTimeS = result.trigger_time_s;
        this.taskNum = result.sinks.length;
      });
  }

  createDialog() {
    const dialogRef = this.dialog.open(CreateStreamTableTaskDialogComponent, {
      width: '500px',
      height: '440px',
      disableClose: true,
      autoFocus: false,
      data: {
        status: 'create',
        tb_id: this.tbId
      }
    });
    dialogRef.afterClosed().subscribe(data => {
      if (data) {
        this.getTaskList();
      }
    });
  }

  editConfig(item: any) {
    const dialogRef = this.dialog.open(CreateStreamTableTaskDialogComponent, {
      width: '500px',
      height: '440px',
      disableClose: true,
      autoFocus: false,
      data: {
        status: 'modify',
        modify_data: _.cloneDeep(item),
        tb_id: this.tbId
      }
    });
    dialogRef.afterClosed().subscribe(data => {
      if (data) {
        this.getTaskList();
      }
    });
  }

  deleteTask(jobKey: string) {
    const dialogRef = this.dialog.open(ConfirmDialogComponent, {
      data: {
        title: '提示',
        content: '确定继续删除？'
      },
      width: '300px'
    });
    dialogRef.afterClosed().subscribe(data => {
      if (data) {
        this.loading = true;
        this.http
          .post('/api/job/stream_delete', { tb_id: this.tbId, job_key: jobKey })
          .pipe(finalize(() => (this.loading = false)))
          .subscribe(() => {
            this.getTaskList();
          });
      }
    });
  }

  toggleTask(item: any) {
    this.loading = true;
    let req$;
    if (item.status === 1) {
      req$ = this.http.post('/api/job/stream_stop', { tb_id: this.tbId, job_key: item.job_key });
    } else {
      req$ = this.http.post('/api/job/stream_start', { tb_id: this.tbId, job_key: item.job_key });
    }
    req$.pipe(finalize(() => (this.loading = false))).subscribe(() => {
      this.getTaskList();
    });
  }

  getTitle(config: any) {
    let title = '';
    switch (config.type) {
      case 0:
        title = config.database + '.' + config.dbtable;
        break;
      case 3:
        title = config.url;
        break;
      case 4:
        title = config.topic;
        break;
      case 6:
        title = config.database + '.' + config.dbtable;
        break;
      case 5:
        title = config.topic;
        break;
    }
    return title;
  }
}
