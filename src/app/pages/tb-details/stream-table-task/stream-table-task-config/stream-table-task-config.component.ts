import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';

@Component({
  selector: 'stream-table-task-config',
  templateUrl: './stream-table-task-config.component.html',
  styleUrls: ['./stream-table-task-config.component.scss']
})
export class StreamTableTaskConfigDialogComponent implements OnInit {
  connList: any = [];
  connIndex = -1;
  type = '';

  constructor(
    public dialogRef: MatDialogRef<StreamTableTaskConfigDialogComponent>,
    public dialog: MatDialog,
    @Inject(MAT_DIALOG_DATA) public dialogData: any
  ) {
    this.connList = this.dialogData.configArr;
    this.type = this.dialogData.type;
  }

  ngOnInit() {}

  export() {
    this.dialogRef.close(this.connIndex);
  }
}
