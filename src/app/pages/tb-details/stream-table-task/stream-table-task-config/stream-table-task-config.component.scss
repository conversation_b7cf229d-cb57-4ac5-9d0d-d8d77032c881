@import '~@haizhi/ui/styles/themes/light.data';
.conn-list {
  height: 272px;
  overflow: auto;
  .list-info {
    line-height: 32px;
    width: 100%;
    display: flex;
    .list-item {
      display: inline-block;
      width: calc(100% - 20px);
      flex: 1;
      display: flex;
      .name-inline {
        display: inline-block;
        max-width: 60px;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        vertical-align: middle;
      }
    }
  }
  input {
    vertical-align: top;
    margin-top: 9px;
  }
  label {
    line-height: 32px;
    height: 32px;
    display: inline-block;
    color: $type-600;
    font-weight: 600;
  }
  span {
    display: inline-block;
    color: $type-800;
    &.httl-url-width {
      flex: 1;
      word-break: break-all;
    }
  }

  .nodata {
    text-align: center;
    position: absolute;
    left: 50%;
    top: 50%;
    width: 96px;
    height: 120px;
    margin-left: -50px;
    margin-top: -60px;
    span {
      color: $type-700;
    }
  }
}

.url-width {
  max-width: 120px;
  vertical-align: top;
}

.colum-1 {
  width: 311px;
  display: inline-block;
  line-height: 32px;
}
.colum-2 {
  width: 209px;
  display: inline-block;
  line-height: 32px;
}
.colum-one {
  max-width: 240px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.colum-two {
  max-width: 148px;
  display: inline-block;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  vertical-align: middle;
}
.label-1 {
  width: 63px;
}
.label-2 {
  width: 53px;
}
.datahub-item {
  display: inline-block;
  width: calc(100% - 20px);
  line-height: 32px;
}
