<div mat-dialog-title>
  导入已有连接配置
  <i hz-icon hzName="close" [mat-dialog-close]="false"></i>
</div>
<mat-dialog-content>
  <div class="content-form">
    <label class="connect-header">选择已配置的连接信息</label>
    <div class="conn-list">
      <ng-container *ngIf="connList.length > 0 && (type == '0' || type == '6')">
        <div class="list-info mt16" *ngFor="let item of connList; let i = index">
          <input type="radio" name="connConfig" [(ngModel)]="connIndex" value="{{ i }}" />
          <div class="ml4 list-item">
            <label>数据库名称</label>
            <span class="ml8 mr16 name-inline" [title]="item.config.dbName">
              {{ item.config.dbName }}
            </span>
            <label>数据库地址</label>
            <span class="ml8 mr16 url-width nowrap" [title]="item.config.url">
              {{ item.config.url }}
            </span>
            <label>用户名</label>
            <span class="ml8 mr16 name-inline" [title]="item.config.user">
              {{ item.config.user }}
            </span>
            <label>密码</label>
            <span class="ml8 name-inline" [title]="item.config.password">
              {{ item.config.password }}
            </span>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="connList.length > 0 && type == '3'">
        <div class="list-info mt16" *ngFor="let item of connList; let i = index">
          <input type="radio" name="connConfig" [(ngModel)]="connIndex" value="{{ i }}" />
          <div class="ml4 list-item">
            <label>数据库地址</label>
            <span class="ml8 mr16 httl-url-width">{{ item.config.url }}</span>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="connList.length > 0 && type == '4'">
        <div class="list-info mt16" *ngFor="let item of connList; let i = index">
          <input type="radio" name="connConfig" [(ngModel)]="connIndex" value="{{ i }}" />
          <div class="ml4 list-item">
            <label>broker-list</label>
            <span class="ml8 mr16">{{ item.config.servers }}</span>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="connList.length > 0 && type == '5'">
        <div class="list-info mt16" *ngFor="let item of connList; let i = index">
          <input type="radio" name="connConfig" [(ngModel)]="connIndex" value="{{ i }}" />
          <div class="ml4 datahub-item">
            <div class="colum-1 mr-16">
              <label class="label-1">Endpoint</label>
              <span class="ml8 colum-one" [title]="item.config.url">{{ item.config.url }}</span>
            </div>
            <div class="colum-2">
              <label class="label-2">AccessID</label>
              <span class="ml8 colum-two" [title]="item.config.user">{{ item.config.user }}</span>
            </div>
            <div class="colum-1">
              <label class="label-1">AccessKey</label>
              <span class="ml8 colum-one" [title]="item.config.password">
                {{ item.config.password }}
              </span>
            </div>
            <div class="colum-2 ml-16">
              <label class="label-2">Project</label>
              <span class="ml8 colum-two" [title]="item.config.dbName">
                {{ item.config.dbName }}
              </span>
            </div>
          </div>
        </div>
      </ng-container>
      <ng-container *ngIf="connList.length == 0">
        <div class="nodata">
          <i hz-icon hzName="no-collect-light" hzSize="96px"></i>
          <span>暂无配置</span>
        </div>
      </ng-container>
    </div>
  </div>
</mat-dialog-content>
<article mat-dialog-actions>
  <button hz-button hzSize="m" hzType="text" [disabled]="connIndex == -1" (click)="export()">
    导入
  </button>
  <button hz-button hzSize="m" hzType="text" [mat-dialog-close]="false">取消</button>
</article>
