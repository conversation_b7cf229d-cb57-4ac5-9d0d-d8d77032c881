@import '~@haizhi/ui/styles/themes/light.data';
.flow-task-container {
  display: flex;
  justify-content: center;
  align-items: center;
  .task-list {
    width: 100%;
  }
  .task-title {
    padding-bottom: 8px;
    p {
      height: 32px;
      line-height: 32px;
    }
  }
}

.hz-table-wrap {
  padding: 8px 16px;
  height: calc(100% - 40px);
  box-shadow: $container-c100;
  border-radius: 8px;
  div {
    height: 100%;
    overflow: auto;
  }
  table {
    th {
      position: sticky;
      top: 0;
      z-index: 1;
      background: $mono-100;
      font-size: 12px;
      color: $type-700;
    }
    tr {
      .btn-operator {
        display: none;
      }
      &:hover {
        .btn-operator {
          display: inline-block;
        }
      }
    }
  }
}

.no-data {
  text-align: center;
  color: $type-600;
  padding-top: 20px;
}

.num {
  color: $primary-900;
}

@media screen and (max-width: 1680px) {
  table {
    td {
      &:first-child {
        width: 45% !important;
        max-width: 460px !important;
      }
      &:last-child {
        width: 25% !important;
        min-width: 276px;
      }
    }
  }
}
@media screen and (min-width: 1680px) {
  table {
    td {
      &:first-child {
        max-width: 620px !important;
      }
      &:last-child {
        max-width: 300px !important;
      }
    }
  }
}
