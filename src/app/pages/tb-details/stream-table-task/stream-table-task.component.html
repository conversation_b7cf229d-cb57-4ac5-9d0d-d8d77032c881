<div class="flow-task-container">
  <div class="task-list">
    <div class="task-title">
      <p>
        此流式表共包含
        <span class="num">{{ taskNum }}</span>
        个任务
        <button hz-button hzSize="s" hzType="primary" class="fr" (click)="createDialog()">
          新建任务
        </button>
      </p>
    </div>
    <div class="hz-table-wrap">
      <div>
        <table class="hz-table" *ngIf="tasks.length > 0">
          <thead>
            <th>输出配置</th>
            <th>输出方式</th>
            <th>更新频次</th>
            <th>导出状态</th>
            <th></th>
          </thead>
          <tbody>
            <tr *ngFor="let item of tasks; let i = index">
              <ng-container *ngIf="[0].indexOf(item.config.type) > -1">
                <td
                  [ngStyle]="{ width: '35%' }"
                  [title]="
                    (item.config.database ? item.config.database : item.config.dbName) +
                    '.' +
                    item.config.dbtable
                  "
                >
                  {{
                    (item.config.database ? item.config.database : item.config.dbName) +
                      '.' +
                      item.config.dbtable
                  }}
                </td>
              </ng-container>
              <ng-container *ngIf="[6].indexOf(item.config.type) > -1">
                <td
                  [ngStyle]="{ width: '35%' }"
                  [title]="item.config.database + '.' + item.config.dbtable"
                >
                  {{ item.config.database + '.' + item.config.dbtable }}
                </td>
              </ng-container>
              <ng-container *ngIf="[4, 5].indexOf(item.config.type) > -1">
                <td [ngStyle]="{ width: '35%' }" [title]="item.config.topic">
                  {{ item.config.topic }}
                </td>
              </ng-container>
              <ng-container *ngIf="[3].indexOf(item.config.type) > -1">
                <td [ngStyle]="{ width: '35%' }" [title]="item.config.url">
                  {{ item.config.url }}
                </td>
              </ng-container>
              <td [ngStyle]="{ width: '10%' }">{{ typeMap[item.config.type] }}</td>
              <td [ngStyle]="{ width: '10%' }">{{ triggerTimeS }}s</td>
              <td [ngStyle]="{ width: '10%' }">
                <ng-container *ngIf="item.status == 1">
                  <i hz-icon hzName="refresh" class="mr4" hzColor="#1F71FF"></i>
                  <span [ngStyle]="{ color: '#1F71FF' }">更新中</span>
                </ng-container>
                <ng-container *ngIf="!(item.status == 0) && !(item.status == 1)">
                  <i hz-icon hzName="attention" hzColor="#FF5266" class="mr4"></i>
                  <span [ngStyle]="{ color: '#FF5266' }">失败</span>
                </ng-container>
                <ng-container *ngIf="item.status == 0">
                  <i hz-icon hzName="no-running" class="mr4"></i>
                  未运行
                </ng-container>
              </td>
              <td [ngStyle]="{ width: '35%' }">
                <div class="btn-operator fl">
                  <button
                    hz-button
                    hzType="action"
                    class="mr8"
                    [disabled]="item.status == 1"
                    (click)="editConfig(item)"
                  >
                    <i hz-icon hzName="cfg-info"></i>
                    <span>任务配置</span>
                  </button>
                  <button
                    hz-button
                    hzType="action"
                    class="mr8"
                    (click)="toggleTask(item)"
                    *ngIf="item.status === 1"
                  >
                    <i hz-icon hzName="close-task"></i>
                    <span>关闭任务</span>
                  </button>
                  <button
                    hz-button
                    hzType="action"
                    class="mr8"
                    (click)="toggleTask(item)"
                    *ngIf="item.status !== 1"
                  >
                    <i hz-icon hzName="play-dense"></i>
                    <span>开启任务</span>
                  </button>
                  <button hz-button hzType="action" (click)="deleteTask(item.job_key)">
                    <i hz-icon hzName="trash"></i>
                    <span>删除</span>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <hz-empty
          hzEmptyIcon="no-rule-light"
          hzEmptyTitle="暂无任务，请先新建任务"
          *ngIf="tasks.length <= 0"
        ></hz-empty>
      </div>
    </div>
  </div>
</div>
<hz-loading-gif *ngIf="loading"></hz-loading-gif>
