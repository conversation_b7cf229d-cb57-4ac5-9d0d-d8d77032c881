import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';
import { MatDialogModule } from '@angular/material/dialog';
import { StreamTableTaskComponent } from './stream-table-task.component';
import { StreamTableTaskConfigDialogComponent } from './stream-table-task-config/stream-table-task-config.component';
import { CreateStreamTableTaskDialogComponent } from './create-stream-table-task/create-stream-table-task.component';
import { RouterModule } from '@angular/router';

@NgModule({
  declarations: [StreamTableTaskComponent, StreamTableTaskConfigDialogComponent, CreateStreamTableTaskDialogComponent],
  imports: [
    SharedModule,
    CommonModule,
    FormsModule,
    MatDialogModule,
    FormsModule,
    RouterModule.forChild([{ path: '', component: StreamTableTaskComponent }])
  ],
  entryComponents: [StreamTableTaskConfigDialogComponent, CreateStreamTableTaskDialogComponent],
  exports: []
})
export class StreamTableTaskModule {}
