<div mat-dialog-title>
  {{ status == 'create' ? '新建任务' : '编辑任务' }}
  <i hz-icon hzName="close" [mat-dialog-close]="false"></i>
</div>
<mat-dialog-content>
  <div class="output-mode-container">
    <div class="mode">
      <span class="label-name">输出方式</span>
      <ul>
        <ng-container *ngIf="!configData || (configData && !configData.hive_aliyun_output)">
          <li>
            <label>
              <input
                type="radio"
                [(ngModel)]="data.type"
                value="0"
                [checked]="data.type == '0'"
                class="mr4"
                (ngModelChange)="changeMode($event)"
                [disabled]="status == 'modify' && data.type != '0'"
              />
              MySql
            </label>
          </li>
          <li>
            <label>
              <input
                type="radio"
                [(ngModel)]="data.type"
                value="3"
                [checked]="data.type == '3'"
                class="mr4"
                (ngModelChange)="changeMode($event)"
                [disabled]="status == 'modify' && data.type != '3'"
              />
              Http
            </label>
          </li>
          <li>
            <label>
              <input
                type="radio"
                [(ngModel)]="data.type"
                value="4"
                [checked]="data.type == '4'"
                class="mr4"
                (ngModelChange)="changeMode($event)"
                [disabled]="status == 'modify' && data.type != '4'"
              />
              Kafka
            </label>
          </li>
        </ng-container>
        <ng-container *ngIf="configData && !!configData.hive_aliyun_output">
          <li>
            <label>
              <input
                type="radio"
                [(ngModel)]="data.type"
                value="6"
                [checked]="data.type == '6'"
                class="mr4"
                (ngModelChange)="changeMode($event)"
                [disabled]="status == 'modify' && data.type != '6'"
              />
              RDS
            </label>
          </li>
          <li>
            <label>
              <input
                type="radio"
                [(ngModel)]="data.type"
                value="5"
                [checked]="data.type == '5'"
                class="mr4"
                (ngModelChange)="changeMode($event)"
                [disabled]="status == 'modify' && data.type != '5'"
              />
              DataHub
            </label>
          </li>
        </ng-container>
      </ul>
    </div>
    <div class="mode-container">
      <ng-container *ngIf="!configData || (configData && !configData.hive_aliyun_output)">
        <ng-container *ngIf="data.type == '0'">
          <div class="label-item label-kafka-item">
            <span class="label-name">数据库地址</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.mysqlurl"
                placeholder="127.168.0.1:3306"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">数据库名称</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.mysqldbName"
                placeholder="my_dbbase"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">数据表名称</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.mysqldbtable"
                placeholder="my_dbtable"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">用户名</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.mysqluser"
                placeholder="admin"
              />
            </label>
          </div>
          <div class="label-item label-password label-kafka-item">
            <span class="label-name">密码</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.mysqlpassword"
                *ngIf="visiblePassword"
              />
              <input
                type="password"
                class="hz-input"
                [(ngModel)]="data.mysqlpassword"
                placeholder="******"
                *ngIf="!visiblePassword"
              />
            </label>
            <i
              hz-icon
              hzName="visible-true"
              (click)="$event.stopPropagation(); !!data.mysqlpassword && (visiblePassword = true)"
              *ngIf="!visiblePassword"
            ></i>
            <i
              hz-icon
              hzName="visible-false"
              (click)="$event.stopPropagation(); visiblePassword = false"
              *ngIf="visiblePassword"
            ></i>
          </div>
        </ng-container>
        <ng-container *ngIf="data.type == '3'">
          <div class="label-item label-kafka-item">
            <span class="label-name">数据库地址</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.httpurl"
                placeholder="127.168.0.1:8080"
              />
            </label>
          </div>
        </ng-container>
        <ng-container *ngIf="data.type == '4'">
          <div class="label-item label-kafka-item">
            <span class="label-name">broker-list</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.kafkaservers"
                placeholder="127.168.0.1:9092"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">Topic</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.kafkatopic"
                placeholder="主题"
              />
            </label>
          </div>
        </ng-container>
      </ng-container>
      <ng-container *ngIf="configData && !!configData.hive_aliyun_output">
        <ng-container *ngIf="data.type == '5'">
          <div class="label-item label-kafka-item">
            <span class="label-name">Endpoint</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.datahuburl"
                placeholder="endpoint"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">AccessID</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.datahubuser"
                placeholder="accessid"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">AccessKey</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.datahubpassword"
                placeholder="accesskey"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">Project</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.datahubdbName"
                placeholder="project"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">Topic</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.datahubtopic"
                placeholder="主题"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">版本号</span>
            <label>
              <hz-select [(ngModel)]="data.datahubversion">
                <hz-option
                  *ngFor="let item of versions; let i = index"
                  [hzValue]="item.value"
                  [hzLabel]="item.name"
                ></hz-option>
              </hz-select>
            </label>
          </div>
        </ng-container>
        <ng-container *ngIf="data.type == '6'">
          <div class="label-item label-kafka-item">
            <span class="label-name">数据库地址</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.rdsurl"
                placeholder="127.168.0.1:3306"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">数据库名称</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.rdsdatabase"
                placeholder="my_dbbase"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">数据表名称</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.rdsdbtable"
                placeholder="my_dbtable"
              />
            </label>
          </div>
          <div class="label-item label-kafka-item">
            <span class="label-name">用户名</span>
            <label>
              <input type="text" class="hz-input" [(ngModel)]="data.rdsuser" placeholder="admin" />
            </label>
          </div>
          <div class="label-item label-password label-kafka-item">
            <span class="label-name">密码</span>
            <label>
              <input
                type="text"
                class="hz-input"
                [(ngModel)]="data.rdspassword"
                *ngIf="visiblePassword"
              />
              <input
                type="password"
                class="hz-input"
                [(ngModel)]="data.rdspassword"
                placeholder="******"
                *ngIf="!visiblePassword"
              />
            </label>
            <i
              hz-icon
              hzName="visible-true"
              (click)="$event.stopPropagation(); !!data.rdspassword && (visiblePassword = true)"
              *ngIf="!visiblePassword"
            ></i>
            <i
              hz-icon
              hzName="visible-false"
              (click)="$event.stopPropagation(); visiblePassword = false"
              *ngIf="visiblePassword"
            ></i>
          </div>
        </ng-container>
      </ng-container>
    </div>
  </div>
</mat-dialog-content>

<mat-dialog-actions class="ngdialog-buttons">
  <button (click)="export()" class="fl export" [ngClass]="{ 'not-allowed': notAllowed }">
    导入已有连接配置
  </button>
  <button mat-button class="mat-button" (click)="createTask()">确定</button>
  <button mat-button class="mat-button" [mat-dialog-close]="false">取消</button>
</mat-dialog-actions>
<hz-loading-gif *ngIf="loading"></hz-loading-gif>
