@import '~@haizhi/ui/styles/themes/light.data';
.output-mode-container {
  height: 320px;
}
.mode {
  height: 32px;
  line-height: 32px;
  ul {
    width: 360px;
    display: inline-block;
    li {
      display: inline-block;
      margin-right: 24px;
      &:last-child {
        margin-right: 0;
      }
    }
  }
}

.label-name {
  width: 56px;
  display: inline-block;
  margin-right: 16px;
  color: $type-600;
  font-weight: 600;
}

.label-item {
  margin-top: 16px;
  height: 32px;
  line-height: 32px;
  &.label-password {
    position: relative;
    [hz-icon] {
      position: absolute;
      right: 8px;
      top: 8px;
      cursor: pointer;
    }
    input {
      padding-right: 30px;
    }
  }
}

input[type='text'],
input[type='password'] {
  width: 364px;
  box-sizing: border-box;
}

.mat-dialog-actions {
  min-height: 48px !important;
}

.export {
  color: $primary-900;
  text-decoration: underline;
  line-height: 32px;
  height: 32px;
  cursor: pointer;
  &.not-allowed {
    cursor: not-allowed;
    opacity: 0.5;
  }
}

.label-kafka-item {
  .label-name {
    width: 65px;
  }
  input {
    width: 355px;
  }
  label {
    width: 355px;
    display: inline-block;
  }
}
