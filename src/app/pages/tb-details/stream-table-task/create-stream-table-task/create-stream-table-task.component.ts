import { Component, Inject, OnInit } from '@angular/core';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import * as _ from 'lodash';
import { MessageService } from '@haizhi/ng-hertz/message';
import { HttpClient } from '@angular/common/http';
import { finalize } from 'rxjs/operators';
import { StreamTableTaskConfigDialogComponent } from '../stream-table-task-config/stream-table-task-config.component';
import { HttpService } from '../../../../core/services/http.service';

export interface FlowData {
  type?: string;
  mysqlurl?: string;
  mysqldbName?: string;
  mysqldbtable?: string;
  mysqluser?: string;
  mysqlpassword?: string;
  httpurl?: string;
  kafkaservers?: string;
  kafkatopic?: string;
  datahuburl?: string;
  datahubuser?: string;
  datahubpassword?: string;
  datahubtopic?: string;
  datahubdbName?: string;
  datahubversion?: number;
  rdsurl?: string;
  rdsdatabase?: string;
  rdsdbtable?: string;
  rdsuser?: string;
  rdspassword?: string;
}

@Component({
  selector: 'create-stream-table-task',
  templateUrl: './create-stream-table-task.component.html',
  styleUrls: ['./create-stream-table-task.component.scss']
})
export class CreateStreamTableTaskDialogComponent implements OnInit {
  data: FlowData = {
    type: '0',
    mysqlurl: '',
    mysqldbName: '',
    mysqldbtable: '',
    mysqluser: '',
    mysqlpassword: '',
    httpurl: '',
    kafkaservers: '',
    kafkatopic: '',
    datahuburl: '',
    datahubuser: '',
    datahubpassword: '',
    datahubtopic: '',
    datahubdbName: '',
    datahubversion: 0,
    rdsurl: '',
    rdsdatabase: '',
    rdsdbtable: '',
    rdsuser: '',
    rdspassword: ''
  };
  typeMap = {
    0: 'mysql',
    3: 'http',
    4: 'kafka',
    5: 'datahub',
    6: 'rds'
  };
  sqlConfigArr: any = null;
  httpConfigArr: any = null;
  kafkaConfigArr: any = null;
  dataHubConfigArr: any = null;
  rdsConfigArr: any = null;
  loading = false;
  status = 'create';
  modify_data: any;
  tb_id = '';
  visiblePassword = false;
  notAllowed = false;
  versions: any = [
    { name: '2.12之前', value: 0 },
    { name: '2.12之后', value: 1 }
  ];
  configData: any = JSON.parse(localStorage.getItem('All_CONFIG'));

  constructor(
    public dialogRef: MatDialogRef<StreamTableTaskConfigDialogComponent>,
    public dialog: MatDialog,
    private messageService: MessageService,
    private http: HttpService,
    @Inject(MAT_DIALOG_DATA) public dialogData: any
  ) {
    this.status = this.dialogData.status;
    this.modify_data = this.dialogData.modify_data;
    this.tb_id = this.dialogData.tb_id;
    if (this.status === 'modify') {
      this.modify_data.config.type = this.modify_data.config.type + '';
      const config = this.modify_data.config;
      this.data.type = config.type;
      this.matchKey(config, config.type);
    }
  }

  ngOnInit() {
    if (this.configData && !!this.configData.hive_aliyun_output && this.status !== 'modify') {
      this.data.type = '6';
    }
    if (this.data.type == '0') {
      this.changeMode('0');
    }
    if (this.data.type == '3') {
      this.changeMode('3');
    }
    if (this.data.type == '4') {
      this.changeMode('4');
    }
    if (this.data.type == '5') {
      this.changeMode('5');
    }
    if (this.data.type == '6') {
      this.changeMode('6');
    }
  }

  matchKey(data: any, type: string) {
    for (let key in data) {
      if (_.has(this.data, this.typeMap[type] + key)) {
        this.data[this.typeMap[type] + key] = data[key];
      }
      if (type == '0' && data.database) {
        this.data.mysqldbName = data.database;
      }
    }
  }

  changeMode(type: string) {
    this.visiblePassword = false;
    switch (type) {
      case '0':
        if (this.sqlConfigArr == null) {
          this.getConnectConfig({ type: 0 }).then(data => {
            this.sqlConfigArr = data;
          });
        } else {
          this.notAllowed = this.sqlConfigArr.length == 0;
        }
        break;
      case '3':
        if (this.httpConfigArr == null) {
          this.getConnectConfig({ type: 3 }).then(data => {
            this.httpConfigArr = data;
          });
        } else {
          this.notAllowed = this.httpConfigArr.length == 0;
        }
        break;
      case '4':
        if (this.kafkaConfigArr == null) {
          this.getConnectConfig({ type: 4 }).then(data => {
            this.kafkaConfigArr = data;
          });
        } else {
          this.notAllowed = this.kafkaConfigArr.length == 0;
        }
        break;
      case '5':
        if (this.dataHubConfigArr == null) {
          this.getConnectConfig({ type: 5 }).then(data => {
            this.dataHubConfigArr = data;
          });
        } else {
          this.notAllowed = this.dataHubConfigArr.length == 0;
        }
        break;
      case '6':
        if (this.rdsConfigArr == null) {
          this.getConnectConfig({ type: 6 }).then(data => {
            this.rdsConfigArr = data;
          });
        } else {
          this.notAllowed = this.rdsConfigArr.length == 0;
        }
        break;
    }
  }

  export() {
    if (this.notAllowed) {
      return false;
    }
    let arr: Array<any> = [];
    switch (this.data.type) {
      case '0':
        arr = this.sqlConfigArr;
        break;
      case '3':
        arr = this.httpConfigArr;
        break;
      case '4':
        arr = this.kafkaConfigArr;
        break;
      case '5':
        arr = this.dataHubConfigArr;
        break;
      case '6':
        arr = this.rdsConfigArr;
        break;
    }
    const dialogRef = this.dialog.open(StreamTableTaskConfigDialogComponent, {
      width: '637px',
      disableClose: true,
      autoFocus: false,
      data: {
        configArr: arr,
        type: this.data.type
      }
    });
    dialogRef.afterClosed().subscribe(index => {
      if (index) {
        const config = _.cloneDeep(arr[index].config);
        const tempData = _.cloneDeep(config);
        if (this.data.type == '0') {
          tempData.database = config.dbName;
        }
        if (this.data.type == '6') {
          tempData.database = config.dbName;
        }
        if (this.data.type == '5') {
          tempData.version = 1;
        }
        this.matchKey(tempData, this.data.type);
      }
    });
  }

  getConnectConfig(previewParams: any) {
    return new Promise((resolve, reject) => {
      this.http.post('/api/job/getconn', previewParams).subscribe(
        result => {
          _.each(result, item => {
            item.config = JSON.parse(item.config);
          });
          this.notAllowed = result.length == 0;
          resolve(result);
        },
        err => {
          this.notAllowed = true;
          resolve([]);
        }
      );
    });
  }

  saveHttp() {
    if (!this.checkEmpty(this.data.httpurl, 'url')) {
      return false;
    }
    return {
      type: 3,
      url: this.data.httpurl.trim()
    };
  }

  saveKafka() {
    if (!this.checkEmpty(this.data.kafkaservers, 'servers')) {
      return false;
    }
    if (!this.checkString(this.data.kafkatopic, 'topic')) {
      return false;
    }
    return {
      type: 4,
      topic: this.data.kafkatopic,
      servers: this.data.kafkaservers.trim()
    };
  }

  saveMysql() {
    if (!this.checkEmpty(this.data.mysqlurl, 'url')) {
      return false;
    }
    if (!this.checkEmpty(this.data.mysqldbName, 'database')) {
      return false;
    }
    if (!this.checkEmpty(this.data.mysqluser, 'user')) {
      return false;
    }
    if (!this.checkEmpty(this.data.mysqldbtable, 'dbtable')) {
      return false;
    }
    if (!this.checkEmpty(this.data.mysqlpassword, 'password')) {
      return false;
    }
    return {
      type: 0,
      url: this.data.mysqlurl.trim(),
      dbName: this.data.mysqldbName.trim(),
      dbtable: this.data.mysqldbtable.trim(),
      password: this.data.mysqlpassword.trim(),
      user: this.data.mysqluser.trim()
    };
  }

  saveRds() {
    if (!this.checkEmpty(this.data.rdsurl, 'url')) {
      return false;
    }
    if (!this.checkEmpty(this.data.rdsdatabase, 'database')) {
      return false;
    }
    if (!this.checkEmpty(this.data.rdsuser, 'user')) {
      return false;
    }
    if (!this.checkEmpty(this.data.rdsdbtable, 'dbtable')) {
      return false;
    }
    if (!this.checkEmpty(this.data.rdspassword, 'password')) {
      return false;
    }
    return {
      type: 6,
      url: this.data.rdsurl.trim(),
      database: this.data.rdsdatabase.trim(),
      dbtable: this.data.rdsdbtable.trim(),
      password: this.data.rdspassword.trim(),
      user: this.data.rdsuser.trim()
    };
  }

  saveDataHub() {
    if (!this.checkEmpty(this.data.datahuburl, 'endpoint')) {
      return false;
    }
    if (!this.checkEmpty(this.data.datahubuser, 'accessid')) {
      return false;
    }
    if (!this.checkEmpty(this.data.datahubpassword, 'accesskey')) {
      return false;
    }
    if (!this.checkEmpty(this.data.datahubtopic, 'topic')) {
      return false;
    }
    if (!this.checkTopicRule(this.data.datahubtopic, 'topic')) {
      return false;
    }
    if (!this.checkEmpty(this.data.datahubdbName, 'dbName')) {
      return false;
    }
    return {
      type: 5,
      url: this.data.datahuburl.trim(),
      password: this.data.datahubpassword.trim(),
      user: this.data.datahubuser.trim(),
      dbName: this.data.datahubdbName.trim(),
      topic: this.data.datahubtopic.trim(),
      version: this.data.datahubversion
    };
  }

  checkTopicRule(n: string, name: string) {
    if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(n)) {
      this.messageService.warning(name + '命名规则，英文字母开头，仅允许英文字母、数字及“_”，大小写不敏感');
      return false;
    }
    return true;
  }

  checkString(n: string, name: string) {
    const reg = /^[A-Za-z0-9_]+$/gi;
    if (!reg.test(n)) {
      this.messageService.warning(name + '只能包含字母数字和_');
      return false;
    }
    return true;
  }

  checkUrl(url: string) {
    const reURL = /^(https|http):\/\/.+$/;
    if (!reURL.test(url)) {
      this.messageService.warning('请输入以https|http开头的url');
      return false;
    }
    return true;
  }

  checkEmpty(n: string, name: string) {
    if (!n.trim()) {
      this.messageService.warning(name + '不能为空');
      return false;
    }
    return true;
  }

  createTask() {
    if (this.loading) {
      return;
    }
    const param: any = {
      tb_id: this.tb_id,
      config: {}
    };

    if (this.data.type == '0') {
      const sqlConfig = this.saveMysql();
      if (!sqlConfig) {
        return false;
      } else {
        param.config = sqlConfig;
      }
    }

    if (this.data.type == '3') {
      const httpConfig = this.saveHttp();
      if (!httpConfig) {
        return false;
      } else {
        param.config = httpConfig;
      }
    }
    if (this.data.type == '4') {
      const kafkaConfig = this.saveKafka();
      if (!kafkaConfig) {
        return false;
      } else {
        param.config = kafkaConfig;
      }
    }

    if (this.data.type == '5') {
      const dataHubConfig = this.saveDataHub();
      if (!dataHubConfig) {
        return false;
      } else {
        param.config = dataHubConfig;
      }
    }
    if (this.data.type == '6') {
      const rdsConfig = this.saveRds();
      if (!rdsConfig) {
        return false;
      } else {
        param.config = rdsConfig;
      }
    }
    this.check(param).then(data => {
      if (data) {
        if (this.status === 'modify') {
          param.job_key = this.modify_data.job_key;
        }
        let req$;
        if (this.status === 'create') {
          req$ = this.http.post('/api/job/stream_create', param);
        } else {
          req$ = this.http.post('/api/job/stream_modify', param);
        }
        this.loading = true;
        req$.pipe(finalize(() => (this.loading = false))).subscribe(res => {
          this.dialogRef.close(true);
        });
      }
    });
  }

  check(param: any) {
    return new Promise((resolve, reject) => {
      this.loading = true;
      this.http
        .post('/api/job/stream_check', { config: param.config })
        .pipe(finalize(() => (this.loading = false)))
        .subscribe(
          data => {
            resolve(true);
          },
          err => {
            resolve(false);
          }
        );
    });
  }
}
