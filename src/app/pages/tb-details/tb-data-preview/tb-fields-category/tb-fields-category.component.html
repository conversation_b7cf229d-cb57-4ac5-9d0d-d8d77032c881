<section class="tb-fields-category">
  <hz-search
    class="tb-fields-category-search"
    [(ngModel)]="queryText"
    hzWidth="200px"
    (ngModelChange)="queryTextChange$.next()"
  ></hz-search>
  <hz-tabs hzTabsType="dense">
    <hz-tab hzTabTitle="数据表字段">
      <ng-container *ngFor="let category of categoryList">
        <ng-container *ngIf="category.fields as filteredFields">
          <article class="tb-fields-category-item" *ngIf="filteredFields.length > 0">
            <div class="category-item-type">
              <ng-container *ngIf="checkable">
                <label
                  hz-checkbox
                  [(ngModel)]="category.selected"
                  [hzIndeterminate]="category.indeterminate"
                  (ngModelChange)="updateAllChecked(category)"
                >
                  <i
                    hz-icon
                    [hzName]="'type-' + category.type"
                    hzColor="rgba(31,113,255,1)"
                    class="mr-4"
                  ></i>
                  <span>{{ category.name }}</span>
                </label>
              </ng-container>
              <ng-container *ngIf="!checkable">
                <i
                  hz-icon
                  [hzName]="'type-' + category.type"
                  hzColor="rgba(31,113,255,1)"
                  class="mr-4"
                ></i>
                <span>{{ category.name }}</span>
              </ng-container>
            </div>
            <div class="category-item-list">
              <ul>
                <li *ngFor="let field of filteredFields" class="mr-24">
                  <ng-container *ngIf="checkable">
                    <label
                      hz-checkbox
                      [(ngModel)]="field.selected"
                      (ngModelChange)="updateSingleChecked(category)"
                    >
                      <span>{{ field.title }}</span>
                    </label>
                  </ng-container>
                  <ng-container *ngIf="!checkable">
                    <span>{{ field.title }}</span>
                  </ng-container>
                </li>
              </ul>
            </div>
          </article>
        </ng-container>
      </ng-container>
      <hz-empty hzEmptyIcon="no-result-light" hzEmptyTitle="没有符合条件的字段"></hz-empty>
    </hz-tab>
  </hz-tabs>
  <article class="tb-fields-category-footer" *ngIf="filteredFieldTotal !== 0">
    <button hz-button class="fr" (click)="modifySelected()">确定</button>
  </article>
</section>
