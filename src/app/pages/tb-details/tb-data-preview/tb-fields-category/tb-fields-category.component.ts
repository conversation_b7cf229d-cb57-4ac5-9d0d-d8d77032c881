import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { TableField, TableFilterType } from '../tb-data-preview.model';
import { HttpService } from '../../../../core/services/http.service';
import { MessageService } from '@haizhi/ng-hertz/message';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, map, takeUntil } from 'rxjs/operators';

@Component({
  selector: 'tb-fields-category',
  templateUrl: './tb-fields-category.component.html',
  styleUrls: ['./tb-fields-category.component.scss']
})
export class TbFieldsCategoryComponent implements OnInit, OnDestroy {
  queryText = '';
  queryTextChange$ = new Subject<void>();
  categoryList: TbFieldsCategory[] = [
    { type: 'date', indeterminate: false, selected: false, name: '日期', fields: [] },
    { type: 'string', indeterminate: false, selected: false, name: '文本', fields: [] },
    { type: 'number', indeterminate: false, selected: false, name: '数值', fields: [] },
    { type: 'blob', indeterminate: false, selected: false, name: '二进制', fields: [] }
  ];
  originFields: TableField[];
  filteredFieldTotal: number;
  destroy$ = new Subject<void>();
  @Input() checkable = true;
  @Input() tbId: string;
  @Input() set fields(value: TableField[]) {
    if (value) {
      this.originFields = value;
      this.setCategoryList(value);
    }
  }
  @Output() modifySuccess = new EventEmitter();

  constructor(private http: HttpService, private message: MessageService) {}

  ngOnInit() {
    this.categoryList.forEach(category => {
      this.updateSingleChecked(category);
    });
    // 搜索
    this.queryTextChange$
      .pipe(
        debounceTime(500),
        map(e => this.queryText),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(() => {
        this.setCategoryList(this.originFields);
      });
  }

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  updateAllChecked(category: TbFieldsCategory) {
    category.indeterminate = false;
    if (category.selected) {
      category.fields.forEach(e => (e.selected = true));
    } else {
      category.fields.forEach(e => (e.selected = false));
    }
  }

  updateSingleChecked(category: TbFieldsCategory) {
    if (category.fields.every(item => !item.selected)) {
      category.selected = false;
      category.indeterminate = false;
    } else if (category.fields.every(item => item.selected)) {
      category.selected = true;
      category.indeterminate = false;
    } else {
      category.indeterminate = true;
    }
  }

  modifySelected() {
    const fields: TableField[] = this.originFields.filter(e => e.selected);
    if (fields.length === 0) {
      this.message.error('请至少选择一个字段');
      return;
    }
    this.http.post('/api/tb/field_selected/modify', { tb_id: this.tbId, field_ids: fields.map(e => e.fid) }).subscribe(res => {
      this.message.success('保存成功');
      this.modifySuccess.emit();
    });
  }

  setCategoryList(fields: TableField[]) {
    this.categoryList[0].fields = [];
    this.categoryList[1].fields = [];
    this.categoryList[2].fields = [];
    this.categoryList[3].fields = [];
    this.filteredFieldTotal = 0;
    for (const item of fields) {
      if (item.title.toLocaleLowerCase().indexOf(this.queryText.toLocaleLowerCase()) > -1) {
        this.filteredFieldTotal++;
        switch (item.data_type) {
          case 'date':
            this.categoryList[0].fields.push(item);
            break;
          case 'string':
            this.categoryList[1].fields.push(item);
            break;
          case 'number':
            this.categoryList[2].fields.push(item);
            break;
          case 'blob':
            this.categoryList[3].fields.push(item);
            break;
        }
      }
    }
    // 搜索后，根据当前显示项，同步全选状态
    this.categoryList.forEach(category => {
      this.updateSingleChecked(category);
    });
  }
}

interface TbFieldsCategory {
  type: TableFilterType;
  indeterminate: boolean;
  selected: boolean;
  name: string;
  fields: TableField[];
}
