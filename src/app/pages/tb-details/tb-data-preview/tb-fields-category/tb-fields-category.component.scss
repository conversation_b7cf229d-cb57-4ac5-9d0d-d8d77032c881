@import '~node_modules/@haizhi/ui/styles/themes/light.data';
.tb-fields-category {
  font-size: 12px;
  color: $type-800;
  position: relative;
  &-search {
    position: absolute;
    right: 24px;
    z-index: 10;
  }
  &-item {
    display: flex;
    width: 100%;
    .category-item-type {
      display: inline-flex;
      align-items: center;
      width: 90px;
      margin-right: 16px;
      padding: 8px 0;
      line-height: 32px;
      color: $type-600;
      font-weight: 600;
      height: 48px;
    }
    .category-item-list {
      box-shadow: 0 -1px 0 0 rgba(15, 34, 67, 0.11) inset;
      width: 100%;
      padding: 8px 0;
      ul {
        list-style: none;
        line-height: 32px;
        margin: 0;
        max-height: 172px;
        overflow-y: auto;
        li {
          display: inline-flex;
          align-items: center;
          height: 32px;
        }
      }
    }
    ~ hz-empty {
      display: none;
    }
  }
  &-footer {
    margin-top: 8px;
    height: 32px;
    line-height: 32px;
  }
}
