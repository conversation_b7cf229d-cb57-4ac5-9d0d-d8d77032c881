import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { TbDataPreviewComponent } from './tb-data-preview.component';
import { FormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { SharedModule } from '../../../shared/shared.module';
import { TbFieldsCategoryComponent } from './tb-fields-category/tb-fields-category.component';
import { CheckboxModule } from '@haizhi/ng-hertz/checkbox';
import { NzTableModule } from 'ng-zorro-antd';
import { VerifyFormatModule } from '../../../ui/verify-format/verify-format.module';
import { TbFilterModule } from '../../../ui/tb-filter/tb-filter.module';
import { PipesModule } from '../../../core/pipes/pipes.module';
import { TbElementDistributionComponent } from './tb-element-distribution/tb-element-distribution.component';
import { BackBarModule } from 'src/app/ui/back-bar/back-bar.module';

@NgModule({
  declarations: [TbDataPreviewComponent, TbFieldsCategoryComponent, TbElementDistributionComponent],
  imports: [
    CommonModule,
    SharedModule,
    FormsModule,
    RouterModule.forChild([{ path: '', component: TbDataPreviewComponent }]),
    CheckboxModule,
    NzTableModule,
    VerifyFormatModule,
    TbFilterModule,
    PipesModule,
    BackBarModule
  ],
  entryComponents: [TbElementDistributionComponent]
})
export class TbDataPreviewModule {}
