export interface TableFilterWhere {
  where_type: 'condition' | 'sql';
  where_linker: 'or' | 'and';
  condition?: {
    filters: TableFilter[];
  };
  sql?: Array<{ sql: string; tb_id: string }>;
}

export interface TableFilterSort {
  fid: string;
  type: 'desc' | 'asc';
}

export interface TableFilter {
  type: TableFieldType;
  data_type: TableFilterType;
  value: string;
  operator: OperatorValue;
  start_date: string;
  end_date: string;
  fid: string;
  rangeDate?: Date[];
  [key: string]: any;
}

export class TableFilterOperator {
  0: '等于' = '等于';
  1: '不等于' = '不等于';
  2: '大于' = '大于';
  3: '小于' = '小于';
  4: '大于等于' = '大于等于';
  5: '小于等于' = '小于等于';
  6: '包含' = '包含';
  7: '不包含' = '不包含';
  8: '为空' = '为空';
  9: '不为空' = '不为空';
  10: '选择日期范围' = '选择日期范围';
}

export type TableFilterType = 'number' | 'string' | 'date' | 'blob';
type OperatorValue = keyof TableFilterOperator;
type OperatorName = TableFilterOperator[keyof TableFilterOperator];
type OperatorGroup = Record<TableFilterType, Array<OperatorValue>>;
export const OPERATOR_GROUP: OperatorGroup = {
  number: [0, 1, 2, 3, 4, 5, 8, 9],
  string: [0, 1, 6, 7, 8, 9],
  date: [10, 0, 1, 2, 3, 4, 5, 8, 9],
  blob: []
};

export const TableFieldTypeIconMap = {
  0: 'type-number',
  1: 'type-number',
  2: 'type-string',
  3: 'type-date',
  4: 'type-blob',
  date: 'type-date',
  number: 'type-number',
  string: 'type-string',
  blob: 'type-blob'
};

export type TableFieldType = 0 | 1 | 2 | 3 | 4;
export interface TableField {
  type: TableFieldType;
  data_type: TableFilterType;
  fid: string; // 与 field_id 相同
  field_id: string;
  name: string;
  title: string;
  selected?: boolean;
  [key: string]: any;
}

export interface TableSqlFunctionItem {
  demo: string;
  desc: string;
  name: string;
  usage: string;
}

type APIFunctionListClassification = 0 | 1 | 2 | 3 | 4 | 5 | 6;
export interface APIFunctionListResponse {
  classification: {
    [key in APIFunctionListClassification]: TableSqlFunctionItem[];
  };
}

export interface APITbPreviewResponse {
  blob_prefix: string;
  can_partition: boolean;
  comment: string;
  count: number;
  data: Array<string[]>;
  fields: TableField[];
  force_merge: 0 | 1;
  general: Array<any>;
  has_gis: 0 | 1 | null;
  label: string;
  materialized: 0 | 1;
  partition: null;
  schema: TableField[];
  source_field: TableField[];
  special_status: number;
  status: number;
  tb_name: string;
  tb_type: string;
  type: string;
  update_mode: 0;
  update_time: 1606578857;
  url_config: Array<any>;
  vfield_partition: boolean;
}

// 获取表的分区详细信息（同时用于图表和列表展示）
export interface TbPartitionParam {
  tb_id: string;
  page_no?: number; // 页码
  page_size?: number; // 每页返回数据量
  sort_key?: string; // 排序key
  sort_direction?: 'asc' | 'desc'; // asc 升序or desc 降序
}

export interface ElementTbParam {
  tb_id: string;
  page_no?: number; // 页码
  page_size?: number; // 每页返回数据量
  search_key?: string;
}
