@import '~node_modules/@haizhi/ui/styles/themes/light.data';

.tb-data-preview {
  height: 100%;
  display: flex;
  flex-direction: column;

  .tb-status-banner {
    margin-bottom: 8px;
    border-radius: 8px;
    width: 100%;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    color: $type-800;
    &-error {
      background-color: rgba(255, 148, 49, 0.12);
      .icon-box {
        background: rgba(255, 148, 49, 0.12);
      }
    }

    &-work {
      background-color: rgba(43, 121, 255, 0.1);
      .icon-box {
        animation: rotating 2s linear infinite;
      }
    }

    .icon-box {
      width: 28px;
      height: 28px;
      border-radius: 50%;
      display: inline-flex;
      justify-content: center;
      align-items: center;
    }
  }

  .tb-filter-control {
    margin-bottom: 16px;
    position: relative;

    button i {
      transition: 0.2s all ease-in-out;

      &.rotate-180 {
        transform: rotate(180deg);
      }
    }

    .control-right {
      display: inline-block;
      float: right;
      line-height: 32px;
      height: 32px;

      .tb-count-status {
        color: $type-600;
        margin-right: 16px;
        display: inline-block;

        .num {
          margin: 0 4px;
          font-weight: 600;
          color: $primary-900;
        }

        .update-time {
          margin-left: 8px;

          &:before {
            content: '';
            display: inline-block;
            width: 2px;
            height: 6px;
            margin-right: 8px;
            position: relative;
            top: -1px;
            background: rgba(81, 130, 228, 0.2);
          }
        }
      }
      .tb-tips {
        color: $type-700;
      }
    }

    .right-content {
      display: inline-flex;
      align-items: center;
      height: 32px;
      line-height: 32px;
      margin-left: 8px;
      span {
        // font-weight: 600;
        line-height: 20px;
        // color: $type-600;
        margin-right: 16px;
      }
    }
  }

  .tb-filter-wrap,
  .tb-fields-category-wrap {
    box-shadow: $container-c100;
    padding: 8px 16px;
    border-radius: 8px;
    margin-bottom: 16px;
    position: relative;
  }
  .element {
    margin-top: -8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .tb-count-status {
      color: $type-600;
      margin-right: 16px;
      display: inline-block;

      .num {
        margin: 0 4px;
        font-weight: 600;
        color: $primary-900;
      }

      .update-time {
        margin-left: 8px;

        &:before {
          content: '';
          display: inline-block;
          width: 2px;
          height: 6px;
          margin-right: 8px;
          position: relative;
          top: -1px;
          background: rgba(81, 130, 228, 0.2);
        }
      }
    }
    .element-right {
      display: flex;
      align-items: center;
      .tag-style {
        border-radius: 6px;
        margin-left: 26px;
        font-weight: 600;
      }
      span {
        margin-left: 26px;
        color: #1f71ff;
        &:hover {
          cursor: pointer;
        }
      }
    }
  }

  .tb-filter-wrap-footer {
    margin-top: 8px;
    position: absolute;
    right: 16px;
    transform: translateY(calc(-100% - 8px));
    line-height: 32px;
  }

  .tb-data-preview-table {
    box-shadow: $container-c100;
    padding: 8px 16px;
    border-radius: 8px;
    overflow-x: auto;
    flex: 1;
    position: relative;
    &.un-copy {
      user-select: none;
    }

    td,
    th {
      white-space: nowrap;
    }

    .field-name {
      position: relative;
      padding-right: 20px;

      &:hover {
        .arrow-icon {
          display: inline !important;
        }
      }

      .arrow-icon {
        position: absolute;
        top: 50%;
        transform: translateY(calc(-50% + 2px));
        cursor: pointer;
        display: none;
      }

      .field-more-info {
        position: absolute;
        left: 20px;
        margin: 0;
        border-bottom: 1px $primary-900 dashed;
        color: transparent;

        p {
          margin: 0;
        }
      }
    }

    .tb-no-result {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
    }
  }
  .null-data {
    color: #bababa;
  }
}

::ng-deep .hz-select-dropdown .hz-option-li {
  margin-bottom: 0;
  padding: 0 14px;
}

.field-more-info-tooltip {
  p {
    margin: 0;
  }
}

@keyframes rotating {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(1turn);
  }
}
