<section class="tb-data-preview" *ngIf="tbPreviewInfo">
  <article
    class="tb-status-banner tb-status-banner-work"
    *ngIf="
      (tbPreviewInfo.status == 3 || tbPreviewInfo.status == 0) &&
      !(tbPreviewInfo.status == 0 && tbType === 'TOPIC')
    "
  >
    <div class="icon-box mr8">
      <i hz-icon hzName="loading" hzColor="rgba(31,113,255,1)"></i>
    </div>
    数据表正在更新，请耐心等待...
  </article>
  <article class="tb-status-banner tb-status-banner-error" *ngIf="tbPreviewInfo.status == 2">
    <div class="icon-box mr8">
      <i hz-icon hzName="info" hzColor="#FF9431"></i>
    </div>
    数据表更新失败，请联系客服
  </article>

  <article class="tb-filter-control">
    <button hz-button hzType="action" (click)="switchView('filter')" *ngIf="previewMode === 0">
      <span>{{ showFilter ? '收起筛选' : '数据筛选' }}</span>
      <i hz-icon hzName="triangle" [class.rotate-180]="showFilter"></i>
    </button>
    <button hz-button hzType="action" (click)="switchView('fields')">
      <span>设置显示字段</span>
      <i hz-icon hzName="triangle" [class.rotate-180]="showFields"></i>
    </button>
    <button
      class="ml-8"
      hz-button
      hzType="outline"
      (click)="previewFakeData()"
      *ngIf="previewMode === 1 && tbPreviewInfo.data.length === 0"
    >
      预览假数据
    </button>
    <div class="right-content" *ngIf="tbBaseInfo.storage_type === 3">
      <span>分区名称</span>
      <hz-select
        hzSize="small"
        [(ngModel)]="selectSubregionName"
        (ngModelChange)="onModelChange($event)"
        [hzSearchable]="true"
        [hzClearable]="true"
        [hzIsVirtualScroll]="true"
        style="width: 258px"
      >
        <hz-option hzLabel="全部" hzValue=""></hz-option>
        <hz-option
          *ngFor="let item of selectItems"
          [hzLabel]="item.name"
          [hzValue]="item.query_name"
        ></hz-option>
      </hz-select>
    </div>
    <div class="control-right" *ngIf="tbType !== 'ELEMENT'">
      <div class="tb-count-status" *ngIf="previewMode === 0">
        <span>显示最新</span>
        <em class="num">{{ total }}</em>
        <span>行数据，</span>
        <span>共</span>
        <em class="num">{{ tbPreviewInfo.count }}</em>
        <span>行</span>
        <em class="num">{{ filterFields.length }}</em>
        <span>列数据</span>
        <span class="update-time">
          更新于：{{ tbPreviewInfo.update_time * 1000 | date: 'yyyy-MM-dd HH:mm:ss' }}
        </span>
      </div>
      <ng-container *ngIf="previewMode === 2">
        <i hz-icon hzName="info-sign"></i>
        <span class="tb-tips ml-4">流式表实时更新，查看随机生成的假设数据</span>
      </ng-container>
      <ng-container *ngIf="previewMode === 1">
        <i hz-icon hzName="info-sign"></i>
        <span class="tb-tips ml-4">流式表实时更新，基于实时数据得出抽样数据</span>
      </ng-container>
      <button
        class="right-button"
        hz-button
        hzType="text"
        (click)="formatCheck()"
        *ngIf="
          (tbType === 'RAW' || tbType === 'RESULT' || tbType === 'PROCESS') && previewMode === 0
        "
      >
        格式校验
      </button>
    </div>
  </article>

  <article class="tb-filter-wrap" [hidden]="!showFilter" @dropdownMotion>
    <tb-filter [where]="where" [fields]="fields" [tbId]="tbId"></tb-filter>
    <article class="tb-filter-wrap-footer">
      <button hz-button class="fr" (click)="onFilterClick()">过滤</button>
    </article>
  </article>
  <article class="tb-fields-category-wrap" [hidden]="!showFields" @dropdownMotion>
    <tb-fields-category
      [tbId]="tbId"
      [fields]="fields"
      (modifySuccess)="onModifyFieldSuccess()"
    ></tb-fields-category>
  </article>
  <div class="element" *ngIf="tbType === 'ELEMENT'">
    <div class="tb-count-status" *ngIf="previewMode === 0">
      <span>显示最新</span>
      <em class="num">{{ total }}</em>
      <span>行数据，</span>
      <span>共</span>
      <em class="num">{{ tbPreviewInfo.count }}</em>
      <span>行</span>
      <em class="num">{{ filterFields.length }}</em>
      <span>列数据</span>
      <span class="update-time">
        更新于：{{ tbPreviewInfo.update_time * 1000 | date: 'yyyy-MM-dd HH:mm:ss' }}
      </span>
    </div>
    <div class="element-right mb8">
      <hz-search
        hzWidth="128px"
        [(ngModel)]="searchvalue"
        (ngModelChange)="searchValueChange$.next($event)"
        hzPlaceholder="要素ID查询"
      ></hz-search>
      <hz-tag class="tag-style" (click)="viewElement()">查看要素分布</hz-tag>
    </div>
  </div>
  <article
    class="tb-data-preview-table"
    [ngClass]="{
      'un-copy': !(userInfoService.userInfo$ | async)?.personalization_setting.data_copy
    }"
  >
    <nz-table [nzData]="listOfData" [nzShowPagination]="false">
      <thead>
        <tr>
          <th *ngFor="let field of filterFields; let i = index">
            <div class="field-name">
              <i
                hz-icon
                [hzName]="'type-' + field.data_type"
                hzColor="rgba(31,113,255,1)"
                class="mr-4"
              ></i>
              <span>{{ field.title }}</span>
              <i
                class="ml4 arrow-icon"
                [ngStyle]="{
                  display: this.sort && this.sort.fid === field.fid ? 'inline' : 'none'
                }"
                hz-icon
                [hzName]="
                  this.sort && this.sortMap[field.fid] === 'desc'
                    ? 'desc-sort-arrow'
                    : 'asc-sort-arrow'
                "
                (click)="changeSort(field)"
              ></i>
              <ng-container *ngIf="field.original_name != field.name || field.remark">
                <span
                  class="field-more-info"
                  hz-tooltip
                  [hzTooltipTitle]="infoTpl"
                  hzTooltipType="info"
                >
                  {{ field.title }}
                </span>
                <ng-template #infoTpl>
                  <div class="field-more-info-tooltip">
                    <p>原字段名: {{ field.original_name }}</p>
                    <p style="word-break: break-all">字段描述: {{ field.remark }}</p>
                  </div>
                </ng-template>
              </ng-container>
            </div>
          </th>
        </tr>
      </thead>
      <tbody #tbodyElement>
        <tr *ngFor="let data of filterData">
          <td *ngFor="let it of data" style="min-height: 16px">
            <pre *ngIf="it !== null; else nullData">{{ it }}</pre>
            <ng-template #nullData><span class="null-data">NULL</span></ng-template>
          </td>
        </tr>
        <tr style="height: 280px" *ngIf="filterData.length === 0">
          <div class="tb-no-result">
            <article
              class="tb-status-banner tb-status-banner-work"
              style="background-color: transparent; margin-top: 50px"
              *ngIf="
                (tbPreviewInfo.status == 3 ||
                  tbPreviewInfo.status == 0 ||
                  tbPreviewInfo.status == 6) &&
                tbPreviewInfo.data.length === 0 &&
                tbPreviewInfo.count !== 0 &&
                tableType === '1'
              "
            >
              <!-- <div class="icon-box mr8">
                <i hz-icon hzName="loading" hzColor="rgba(31,113,255,1)"></i>
              </div> -->
              在线表数据正在同步中，请耐心等待...
            </article>
            <hz-empty
              *ngIf="tbPreviewInfo.count === 0"
              hzEmptyIcon="no-result-light"
              hzEmptyTitle="暂无数据"
            ></hz-empty>
          </div>
        </tr>
      </tbody>
    </nz-table>
    <div class="tb-no-result" *ngIf="filterData.length === 0 && tbPreviewInfo.count !== 0">
      <hz-empty hzEmptyIcon="no-result-light" hzEmptyTitle="暂无数据"></hz-empty>
    </div>
  </article>
  <article *ngIf="total > 0 && listOfData.length > 0">
    <hz-pagination
      *ngIf="!loading"
      class="fr mt-16"
      [(hzPageIndex)]="pageIndex"
      [hzTotal]="total"
      [hzPageSize]="pageSize"
      [hzHideOnSinglePage]="true"
      (hzPageIndexChange)="getFilterData()"
      (hzPageSizeChange)="getFilterData()"
    ></hz-pagination>
  </article>
</section>
<hz-loading-gif *ngIf="loading"></hz-loading-gif>
