import { Injectable } from '@angular/core';
import { HttpService } from '../../../core/services/http.service';
import { Observable } from 'rxjs';
import { APIFunctionListResponse, APITbPreviewResponse, ElementTbParam, TableFilterWhere, TbPartitionParam } from './tb-data-preview.model';
import { ElementDistribution } from './tb-element-distribution/tb-element-distribution.model';

@Injectable({
  providedIn: 'root'
})
export class TbDataPreviewService {
  constructor(private http: HttpService) {}

  getSqlFunctionList(): Observable<APIFunctionListResponse> {
    return this.http.get('/api/function/list');
  }

  getTbPreview(params: { tb_id: string; where?: TableFilterWhere }): Observable<APITbPreviewResponse> {
    return this.http.post('/api/tb/preview', params);
  }

  /**
   * 格式校验
   */
  pidNumberCheckFormat(param: { tb_id: string; tb_name: string; field_id: string }) {
    return this.http.post('/api/pidnumber/check', param);
  }

  /**
   * 获取分区详细信息，分区列表
   * 获取表的分区详细信息（同时用于图表和列表展示）
   */
  getTbPartitionList(param: TbPartitionParam) {
    return this.http.get('/api/tb/partition/info', param);
  }
  /**
   *  获取要素分布列表
   */
  getElementTbList(param: ElementTbParam): Observable<{
    data: ElementDistribution[];
    page_no: number;
    page_size: number;
    total: number;
    total_page: number;
  }> {
    return this.http.get('/api/element/distribution', param);
  }
}
