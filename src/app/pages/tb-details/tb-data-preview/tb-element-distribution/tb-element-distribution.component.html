<div class="element-distribution">
  <div class="edit-map-back-bar">
    <back-bar [title]="'要素分布'" (backIconClick)="backPrevious()"></back-bar>
  </div>
  <div class="content">
    <div class="header mb8">
      <div>
        共
        <span>{{ total }}</span>
        张表
      </div>
      <hz-search
        hzWidth="150px"
        [(ngModel)]="searchvalue"
        (ngModelChange)="searchValueChange$.next($event)"
        hzPlaceholder="请输入搜索条件"
      ></hz-search>
    </div>
    <div class="table" *ngIf="!loading">
      <nz-table
        *ngIf="tbList.length > 0"
        class="nz-table"
        #columnTable
        [nzData]="tbList"
        [nzScroll]="{ x: '0', y: 'calc(100vh - 265px)' }"
        nzShowPagination="false"
        nzFrontPagination="false"
      >
        <thead>
          <tr>
            <th nzWidth="200px">要素来源</th>
            <th nzWidth="150px">采集数据条数</th>
            <th [nzWidth]="scrollx">该表所有要素</th>
            <th nzWidth="300px">该表事件信息</th>
          </tr>
        </thead>
        <tbody>
          <tr *ngFor="let tb_item of tbList">
            <td>
              <span class="tb-name" (click)="goToMateData(tb_item.tb_id, tb_item.tb_type)">
                {{ tb_item.tb_name }}
              </span>
            </td>
            <td>{{ tb_item.data_count }}条</td>
            <td class="td-reset">
              <div *ngFor="let element of tb_item.elements" class="element-detail mt8">
                <hz-tag
                  hzType="solid-info"
                  hzSize="tiny"
                  [hzReadonly]="true"
                  class="hz-tag"
                  [title]="element.element_name"
                >
                  {{ element.element_name }}
                </hz-tag>
                <div class="ml4 ele-title">
                  <p class="title">要素ID：</p>
                  <p class="title-info" [title]="element.element_id">{{ element.element_id }}</p>
                </div>
                <div class="ele-desc">
                  <p class="title">要素标识：</p>
                  <p class="title-info">
                    {{ element.element_mark }}
                  </p>
                </div>
              </div>
            </td>
            <td>
              <div *ngIf="tb_item.event_id">
                <p>事件ID：{{ tb_item.event_id }}</p>
                <p>事件标识：{{ tb_item.event_mark }}</p>
              </div>
              <div *ngIf="!tb_item.event_id">无</div>
            </td>
          </tr>
        </tbody>
      </nz-table>
      <article>
        <hz-pagination
          *ngIf="total > 10"
          class="page-content"
          [hzTotal]="total"
          [(hzPageIndex)]="reqParams.page_no"
          [(hzPageSize)]="reqParams.page_size"
          [hzShowQuickJumper]="true"
          (hzPageIndexChange)="pageChangeCall()"
          (hzPageSizeChange)="onPageSizeChange()"
          [hzShowPageSizeChanger]="true"
        ></hz-pagination>
      </article>
      <ng-container>
        <hz-empty
          *ngIf="tbList.length === 0"
          hzEmptyIcon="no-result-light"
          hzEmptyTitle="暂无数据"
        ></hz-empty>
      </ng-container>
    </div>
    <hz-loading-gif *ngIf="loading"></hz-loading-gif>
  </div>
</div>
