import { Component, Inject, OnDestroy, OnInit } from '@angular/core';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged, finalize, takeUntil } from 'rxjs/operators';
import { TbDataPreviewService } from '../tb-data-preview.service';
import { ElementDistribution } from './tb-element-distribution.model';
import * as _ from 'lodash';

@Component({
  selector: 'app-tb-element-distribution',
  templateUrl: './tb-element-distribution.component.html',
  styleUrls: ['./tb-element-distribution.component.scss']
})
export class TbElementDistributionComponent implements OnInit, OnDestroy {
  tbList: ElementDistribution[] = [];
  searchvalue = '';
  originQueryText = '';
  total = 0; // 总数据量
  searchValueChange$ = new Subject<string>();
  destroy$ = new Subject();
  scrollx = '';
  loading = true;
  reqParams = {
    tb_id: '',
    page_no: 1,
    page_size: 10,
    search_key: ''
  };
  constructor(
    private tbDataPreviewService: TbDataPreviewService,
    private dialogRef: MatDialogRef<TbElementDistributionComponent>,
    @Inject(MAT_DIALOG_DATA) public dialogData: { tb_id: string }
  ) {}

  ngOnInit() {
    this.reqParams.tb_id = this.dialogData.tb_id;
    this.getElementTbList();
    this.searchValueChange$.pipe(debounceTime(500), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(res => {
      this.reqParams.search_key = this.searchvalue;
      if (!this.searchvalue.trim()) {
        this.afterSearchClear();
      } else {
        this.doSearch();
      }
    });
  }

  ngOnDestroy(): void {
    localStorage.removeItem('saveData');
    this.destroy$.next();
    this.destroy$.complete();
  }
  // 获取列表
  getElementTbList() {
    this.loading = true;
    this.tbDataPreviewService
      .getElementTbList(this.reqParams)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.tbList = res.data;
        this.setScrollx();
        this.total = res.total;
      });
  }
  setScrollx() {
    const scrollxArr = [];
    this.tbList.forEach(tbItem => {
      tbItem.elements.forEach(eleItem => {
        scrollxArr.push(eleItem.element_mark.length);
      });
    });
    scrollxArr.sort((a, b) => b - a);
    this.scrollx = scrollxArr[0] * 12 + 300 + 'px';
  }
  // 搜索
  doSearch() {
    if (!this.originQueryText.trim()) {
      const saveData = {
        pageSize: this.reqParams.page_size,
        pageNo: this.reqParams.page_no,
        tbList: this.tbList
      };
      localStorage.setItem('saveData', JSON.stringify(saveData));
    }
    this.originQueryText = this.searchvalue;
    this.reqParams.page_no = 1;
    this.reqParams.page_size = 10;
    this.getElementTbList();
  }
  // 清空搜索
  afterSearchClear() {
    const data = JSON.parse(localStorage.getItem('saveData'));
    this.reqParams.page_size = data.pageSize;
    this.reqParams.page_no = data.pageNo;
    this.tbList = data.tbList;
    this.originQueryText = '';
    localStorage.removeItem('saveData');
  }
  // 点击来源表跳转到该表的元数据档案页
  goToMateData(tbId: string, tbType: string) {
    window.open(`/tb-details/${tbId}?tbType=${tbType}`);
  }
  // 分页回调
  pageChangeCall() {
    this.getElementTbList();
  }
  onPageSizeChange() {
    this.reqParams.page_no = 1;
    this.getElementTbList();
  }
  backPrevious() {
    this.dialogRef.close();
  }
}
