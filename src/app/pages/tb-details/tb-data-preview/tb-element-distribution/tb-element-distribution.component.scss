@import '~node_modules/@haizhi/ui/styles/themes/light.data';
.element-distribution {
  height: 100%;
  width: 100%;
  .content {
    margin: 16px 24px 47px;
    height: calc(100% - 96px);
    .header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      span {
        color: #1f71ff;
      }
    }
    .table {
      height: calc(100% - 36px);
      box-shadow: $container-c100;
      border-radius: 8px;
      padding: 16px;
      display: flex;
      justify-content: center;
      .nz-table {
        ::ng-deep .ant-table-fixed-header .ant-table-scroll .ant-table-header {
          min-width: 0 !important;
        }
        .tb-name {
          &:hover {
            cursor: pointer;
            color: blue;
            text-decoration: underline;
          }
        }
        .td-reset {
          padding: 8px 16px;
          .element-detail {
            display: flex;
            // align-items: center;
            &:first-child {
              margin-top: 0;
            }
            .hz-tag {
              width: 90px;
            }
            .ele-title {
              display: flex;
              // align-items: center;
              min-width: 150px;
              .title-info {
                line-height: 18px;
                max-width: 95px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
            .ele-desc {
              display: flex;
              .title-info {
                line-height: 18px;
                // max-width: 170px;
                white-space: nowrap;
              }
            }
            .title {
              line-height: 18px;
              white-space: nowrap;
            }
          }
        }
      }
      .page-content {
        float: right;
        margin-top: 16px;
      }
    }
  }
}
