import { After<PERSON>iew<PERSON>nit, Component, ElementRef, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { TbDetailsService } from './../tb-details.service';
import { dropdownMotion } from '@haizhi/ng-hertz/core/animations';
import { APITbPreviewResponse, TableField, TableFilter, TableFilterSort, TableFilterWhere } from './tb-data-preview.model';
import { TbDataPreviewService } from './tb-data-preview.service';
import { debounceTime, distinctUntilChanged, finalize, startWith, takeUntil, throttleTime } from 'rxjs/operators';
import { TableBaseInfo, TableBaseInfoOriginType, TableBaseInfoType } from '../tb-details.model';
import { MatDialog } from '@angular/material/dialog';
import { VerifyFormatComponent } from 'src/app/ui/verify-format/verify-format.component';
import { UserInfoService } from '../../../core/services/user-info.service';
import { fromEvent, Subject } from 'rxjs';
import { TbElementDistributionComponent } from './tb-element-distribution/tb-element-distribution.component';
import * as _ from 'lodash';

interface SortMap {
  [key: string]: string;
}
@Component({
  selector: 'tb-data-preview',
  templateUrl: './tb-data-preview.component.html',
  styleUrls: ['./tb-data-preview.component.scss'],
  animations: [dropdownMotion]
})
export class TbDataPreviewComponent implements OnInit, AfterViewInit, OnDestroy {
  tbId: string;
  tbType: TableBaseInfoType;
  tbBaseInfo: TableBaseInfo;
  searchvalue = '';
  searchValueChange$ = new Subject<string>();
  showFilter = false;
  showFields = false;
  fields: TableField[];
  filterFields: TableField[];
  listOfData: Array<string[]> = [];
  filterData: Array<string[]> = [];
  total: number; // 返回的总数量，目前最大为 1000
  tbName: string;
  loading = false;
  tableType: string;
  sort: TableFilterSort;
  sortMap: SortMap = {};
  where: TableFilterWhere = {
    where_type: 'condition',
    where_linker: 'and',
    condition: {
      filters: [
        {
          type: null,
          data_type: null,
          value: '',
          operator: null,
          start_date: '',
          end_date: '',
          fid: null,
          field_name: '',
          field_title: ''
        }
      ]
    },
    sql: [
      {
        sql: '',
        tb_id: ''
      }
    ]
  };
  pageIndex = 1;
  pageSize = 10;
  tableHeight: number;
  tbPreviewInfo: APITbPreviewResponse;
  originType: TableBaseInfoOriginType;
  previewMode: 0 | 1 | 2 = 0; // 0: 普通模式; 1: 流式表非罗盘表真实数据; 2: 流式表非罗盘表假数据
  destroy$ = new Subject();
  selectSubregionName = ''; // 选择分区字段
  selectItems: Array<any> = [];
  @ViewChild('tbodyElement', { static: false }) tbodyElement: ElementRef;

  constructor(
    private tbDataPreviewService: TbDataPreviewService,
    private tbDetailsService: TbDetailsService,
    public dialog: MatDialog,
    public userInfoService: UserInfoService
  ) {}

  ngOnInit() {
    this.tableType = localStorage.getItem('tableType');
    this.tbType = this.tbDetailsService.curTbType;
    this.tbBaseInfo = this.tbDetailsService.tbBaseInfo;
    this.originType = this.tbBaseInfo.origin_type;
    if (this.originType === 'streamingkafka' || this.originType === 'streaming' || this.originType === 'streamingdatahub') {
      this.previewMode = 1;
    }
    const resize$ = fromEvent(window, 'resize');
    resize$.pipe(startWith(true), throttleTime(40), takeUntil(this.destroy$)).subscribe(() => {
      this.initPageSize();
    });
    this.tbId = this.tbDetailsService.curTbId;
    this.getTbPreview();
    // 非分区 无需过滤
    if (this.tbDetailsService.tbBaseInfo.storage_type === 3) {
      this.getTbPartitionList();
    }
    this.searchValueChange$.pipe(debounceTime(500), distinctUntilChanged(), takeUntil(this.destroy$)).subscribe(res => {
      this.pageIndex = 1;
      this.getTbPreview();
    });
  }

  ngAfterViewInit() {}

  ngOnDestroy() {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * 根据 window 高度设置合适的 pageSize
   */
  initPageSize() {
    this.tableHeight = window.innerHeight - 64 - 48 - 40 - 44 - 56 - 32;
    const pageSize = this.tableHeight / 40;
    this.pageSize = Math.floor(pageSize) - 1;
  }

  switchView(type: 'filter' | 'fields') {
    if (type === 'filter') {
      this.showFilter = !this.showFilter;
      this.showFields = false;
    } else if (type === 'fields') {
      this.showFields = !this.showFields;
      this.showFilter = false;
    }
  }

  changeSort(item: TableField) {
    this.sort = {
      fid: item.fid,
      type: this.sortMap && this.sortMap[item.fid] === 'desc' ? 'asc' : 'desc'
    };
    this.sortMap[item.fid] = this.sort.type;
    this.pageIndex = 1;
    this.getTbPreview();
  }

  getTbPreview(isFake = false) {
    this.loading = true;
    const params: {
      tb_id: string;
      where?: TableFilterWhere;
      sort?: Array<TableFilterSort>;
      is_streaming?: 0 | 1;
      fake_data?: 0 | 1;
      partition?: string;
      tb_type?: string;
    } = { tb_id: this.tbId };
    const where = _.cloneDeep(this.handleWhere());
    if (where) {
      params.where = where;
    }
    // 当有要素id搜索时
    if (!!this.searchvalue.trim()) {
      const elementIdFile = this.fields.find(item => item.name === '实体ID');
      const tempWhere: TableFilterWhere = {
        condition: {
          filters: [
            {
              type: elementIdFile.type,
              data_type: elementIdFile.data_type,
              value: this.searchvalue,
              operator: 0,
              start_date: '',
              end_date: '',
              fid: elementIdFile.fid,
              field_name: elementIdFile.name,
              field_title: elementIdFile.title
            }
          ]
        },
        where_type: 'condition',
        where_linker: 'and'
      };
      if (params.where) {
        if (params.where.where_type === 'condition') {
          params.where.condition.filters.push(tempWhere.condition.filters[0]);
        } else {
          params.where.sql[0].sql += ` and [实体ID]='${this.searchvalue}'`;
        }
      } else {
        params.where = tempWhere;
      }
    }
    if (this.sort) {
      params.sort = [this.sort];
    }
    params.tb_type = this.tbType;
    // TODO: 特殊参数
    // 字典表
    // if(this.moduleName == 'map') {
    //   params.is_map_tb = 1;
    // }
    // 流式表非罗盘表相关参数
    if (this.tbBaseInfo.tb_storage_flag === 2 && this.tbBaseInfo.type == 'PROCESS') {
      params.is_streaming = 1;
    }
    if (this.previewMode === 1) {
      params.is_streaming = 1;
    }
    if (isFake) {
      params.is_streaming = 1;
      params.fake_data = 1;
    }
    if (this.selectSubregionName) {
      params.partition = this.selectSubregionName;
    }
    this.tbDataPreviewService
      .getTbPreview(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.tbPreviewInfo = res;
        this.fields = res.fields;
        // const columnIndex = [];
        this.filterFields = this.fields.filter((item, index) => {
          if (item.selected) {
            // columnIndex.push(index);
            return item;
          }
        });
        // this.filterFields = this.fields.filter(e => e.selected);
        this.listOfData = res.data;
        this.getFilterData();
        this.tbName = res.tb_name;
        this.total = res.count > 1000 ? 1000 : res.count;
        if (isFake) {
          this.previewMode = 2;
        }
      });
  }
  handleWhere(): TableFilterWhere | null {
    const { sql, condition, ...other } = this.where;
    if (this.where.where_type === 'condition' && condition.filters.filter(e => this.checkFilters(e)).length > 0) {
      return { condition, ...other };
    } else if (this.where.where_type === 'sql' && !!sql[0].sql.trim()) {
      return { sql, ...other };
    }
    return null;
  }

  onFilterClick() {
    this.pageIndex = 1;
    this.getTbPreview();
  }

  checkFilters(item: TableFilter) {
    if (!item.fid) {
      return false;
    }
    if (item.operator === undefined || item.operator === null) {
      return false;
    }
    if (item.operator < 8 && !item.value) {
      return false;
    }
    if (item.operator === 10 && (!item.start_date || !item.end_date)) {
      return false;
    }
    return true;
  }

  formatCheck() {
    this.dialog.open(VerifyFormatComponent, {
      width: '390px',
      disableClose: true,
      data: {
        fields: this.fields,
        tbId: this.tbId,
        tbName: this.tbName
      }
    });
  }

  onModifyFieldSuccess() {
    this.pageIndex = 1;
    this.getTbPreview();
  }

  getFilterData() {
    this.filterData = this.listOfData.slice((this.pageIndex - 1) * this.pageSize, this.pageIndex * this.pageSize);
  }

  previewFakeData() {
    this.pageIndex = 1;
    this.getTbPreview(true);
  }

  onModelChange(item: any) {
    this.pageIndex = 1;
    this.getTbPreview();
  }

  // 获取分区列表
  getTbPartitionList() {
    this.loading = true;
    const params: {
      tb_id: string;
      page_no?: number; // 页码
      page_size?: number; // 每页返回数据量
      sort_key?: string; // 排序key
      sort_direction?: 'asc' | 'desc'; // asc 升序or desc 降序
    } = { tb_id: this.tbId };

    this.tbDataPreviewService
      .getTbPartitionList(params)
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.selectItems = res.pt_list;
      });
  }

  isNotSelected(value: string): boolean {
    return this.selectSubregionName.indexOf(value) === -1;
  }
  // 查看要素分布
  viewElement() {
    const dialogRef = this.dialog.open(TbElementDistributionComponent, {
      width: '100vw',
      height: '100vh',
      maxWidth: 'unset',
      panelClass: 'dialog-full-screen',
      data: {
        tb_id: this.tbId
      }
    });
  }
}
