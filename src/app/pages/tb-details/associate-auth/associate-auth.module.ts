import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { SharedModule } from 'src/app/shared/shared.module';
import { AssociateAuthService } from './associate-auth.service';
import { AssociateAuthComponent } from './associate-auth.component';
import { NzTableModule } from 'ng-zorro-antd/table';
import { NzDrawerModule } from 'ng-zorro-antd/drawer';
import { MatDialogModule } from '@angular/material/dialog';
import { RouterModule } from '@angular/router';
import { ServiceApiVisitComponent } from './service-api-visit/service-api-visit.component';
import { ServiceTableAuthComponent } from './service-table-auth/service-table-auth.component';
import { ServiceDataExportComponent } from './service-data-export/service-data-export.component';
import { ServiceExterViewComponent } from './service-exter-view/service-exter-view.component';

@NgModule({
  imports: [
    SharedModule,
    CommonModule,
    FormsModule,
    NzTableModule,
    NzDrawerModule,
    MatDialogModule,
    RouterModule.forChild([
      {
        path: '',
        component: AssociateAuthComponent,
        children: [
          {
            path: '',
            pathMatch: 'full',
            redirectTo: 'api-visit'
          },
          { path: 'api-visit', component: ServiceApiVisitComponent },
          { path: 'table-auth', component: ServiceTableAuthComponent },
          { path: 'data-export', component: ServiceDataExportComponent },
          { path: 'exter-view', component: ServiceExterViewComponent }
        ]
      }
    ])
  ],
  declarations: [
    AssociateAuthComponent,
    ServiceApiVisitComponent,
    ServiceTableAuthComponent,
    ServiceDataExportComponent,
    ServiceExterViewComponent
  ],
  entryComponents: [],
  exports: [AssociateAuthComponent],
  providers: [AssociateAuthService]
})
export class AssociateAuthModule {}
