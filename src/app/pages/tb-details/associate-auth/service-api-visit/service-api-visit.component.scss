@import '~@haizhi/ui/styles/themes/light.data';
.api-visit {
  height: 100%;
  width: 100%;
  .table-container {
    padding: 16px;
    height: calc(100% - 40px);
    background: #ffffff;
    box-shadow: $container-c100;
    border-radius: 8px;
    .api-name {
      width: 270px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
::ng-deep .ant-drawer-body {
  height: 100%;
  .dra-container {
    height: 100%;
    .service-title {
      font-size: 18px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
    }
    .base-info .product-baseinfo {
      height: calc(100% - 56px);
    }
  }
}
::ng-deep .hz-pagination {
  margin-top: 14px;
  float: right;
}
