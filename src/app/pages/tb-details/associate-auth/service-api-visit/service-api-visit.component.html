<div class="api-visit">
  <div class="table-container">
    <hz-empty hzEmptyIcon="no-result-light" hzEmptyTitle="暂无数据" *ngIf="total === 0"></hz-empty>
    <nz-table
      [nzData]="apiList"
      [nzScroll]="{ y: 'calc(100vh - 265px)' }"
      nzFrontPagination="false"
      nzShowPagination="false"
      *ngIf="total > 0"
    >
      <thead>
        <tr>
          <th nzWidth="200px">API名称</th>
          <th nzWidth="200px">描述</th>
          <th nzWidth="100px">状态</th>
          <th nzWidth="100px">操作</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of apiList">
          <td>
            {{ item.name }}
          </td>
          <td>
            {{ item.desc }}
          </td>
          <td>
            <hz-tag
              hzSize="tiny"
              [hzTextColor]="
                item.status === 1
                  ? '#1F71FF'
                  : item.status === 2
                  ? '#239545'
                  : item.status === 0
                  ? '#FF9431'
                  : 'rgba(21, 22, 24, 0.72)'
              "
            >
              {{
                item.status === 2
                  ? '已发布'
                  : item.status === 1
                  ? '已生成'
                  : item.status === 0
                  ? '草稿'
                  : '已下架'
              }}
            </hz-tag>
          </td>
          <td>
            <button hz-button hzType="action" (click)="viewApiInfo(item.api_id)">
              <i hz-icon hzName="visible-true"></i>
              <span>查看</span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
  <div class="pagination-container" *ngIf="!loading">
    <hz-pagination
      [hzTotal]="total"
      [(hzPageIndex)]="reqParams.page_no"
      [(hzPageSize)]="reqParams.page_size"
      [hzShowQuickJumper]="true"
      (hzPageIndexChange)="pageChangeCall($event)"
      (hzPageSizeChange)="onPageSizeChange($event)"
      [hzShowPageSizeChanger]="true"
    ></hz-pagination>
  </div>
  <hz-loading-gif *ngIf="loading"></hz-loading-gif>
</div>
