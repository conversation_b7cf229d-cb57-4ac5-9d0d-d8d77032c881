import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { ApiFields } from '../associate-auth.model';
import { AssociateAuthService } from '../associate-auth.service';
import { TbDetailsService } from '../../tb-details.service';

@Component({
  selector: 'app-service-api-visit',
  templateUrl: './service-api-visit.component.html',
  styleUrls: ['./service-api-visit.component.scss']
})
export class ServiceApiVisitComponent implements OnInit {
  apiList = [];
  visible = false;
  loading = false;
  tbId: string;
  reqParams = {
    page_no: 1,
    page_size: 10
  };
  total = 0;
  constructor(private associateAuthService: AssociateAuthService, private tbDetailsService: TbDetailsService) {}

  ngOnInit() {
    this.tbId = this.tbDetailsService.curTbId;
    this.getApiList();
  }
  // 查看详情
  viewApiInfo($event) {
    window.location.href = '/dmc/#/api-info?api_id=' + $event;
    // window.open('/dmc/#/api-info', '#');
  }
  // 分页回调
  pageChangeCall($event) {
    this.reqParams.page_no = $event;
    this.getApiList();
  }
  // 切换一页显示条数
  onPageSizeChange($event) {
    this.reqParams.page_size = $event;
    this.getApiList();
  }
  // 获取API服务列表
  getApiList() {
    this.associateAuthService
      .getApiList({
        tb_id: this.tbId,
        page_no: this.reqParams.page_no,
        page_size: this.reqParams.page_size
      })
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.apiList = res.data;
        this.total = res.total;
      });
  }
}
