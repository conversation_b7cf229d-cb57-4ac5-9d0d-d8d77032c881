/* tslint:disable:no-unused-variable */
import { async, ComponentFixture, TestBed } from '@angular/core/testing';
import { By } from '@angular/platform-browser';
import { DebugElement } from '@angular/core';

import { ServiceApiVisitComponent } from './service-api-visit.component';

describe('ServiceApiVisitComponent', () => {
  let component: ServiceApiVisitComponent;
  let fixture: ComponentFixture<ServiceApiVisitComponent>;

  beforeEach(async(() => {
    TestBed.configureTestingModule({
      declarations: [ServiceApiVisitComponent]
    }).compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(ServiceApiVisitComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
