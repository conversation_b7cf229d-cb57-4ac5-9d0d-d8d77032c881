import { Component, OnInit } from '@angular/core';
import { finalize } from 'rxjs/operators';
import { TbDetailsService } from '../../tb-details.service';
import { ProductInfo } from '../associate-auth.model';
import { AssociateAuthService } from '../associate-auth.service';

@Component({
  selector: 'app-service-table-auth',
  templateUrl: './service-table-auth.component.html',
  styleUrls: ['./service-table-auth.component.scss']
})
export class ServiceTableAuthComponent implements OnInit {
  tbList: ProductInfo[] = [];
  tbId = '';
  loading = true;
  proLoading = true;
  productMap: Map<string, string> = new Map();
  constructor(private associateAuthService: AssociateAuthService, private tbDetailsService: TbDetailsService) {}

  ngOnInit() {
    this.tbId = this.tbDetailsService.curTbId;
    this.getPermissionInfo();
    this.getProductList();
  }
  // 获取授权列表
  getPermissionInfo() {
    this.loading = true;
    this.associateAuthService
      .getPermissionInfo({ tb_id: this.tbId })
      .pipe(
        finalize(() => {
          this.loading = false;
        })
      )
      .subscribe(res => {
        this.tbList = res;
      });
  }
  // 获取授权平台
  getProductList() {
    this.proLoading = true;
    this.associateAuthService
      .getProductList()
      .pipe(finalize(() => (this.proLoading = false)))
      .subscribe(res => {
        res.forEach(item => {
          this.productMap.set(item.pro_id, item.pro_name_cn);
        });
      });
  }
  // 查看详情
  viewApiInfo(data: ProductInfo) {
    window.open(
      `/dmc/#/operator-detail?tb_id=${this.tbId}&pro_id=${data.pro_id}&auth_name=${data.auth_name}&type=meta_view&tb_type=${data.tb_type}`,
      '_self'
    );
  }
}
