<div class="table-auth">
  <div class="table-container">
    <nz-table
      *ngIf="!proLoading && !loading && tbList.length > 0"
      [nzData]="tbList"
      [nzShowPagination]="false"
      [nzScroll]="{ x: '1200px', y: 'calc(100vh - 265px)' }"
    >
      <thead>
        <tr>
          <th nzWidth="250px">库表名称</th>
          <th nzWidth="350px">描述</th>
          <th nzWidth="200px">授权平台</th>
          <th nzWidth="200px">状态</th>
          <th nzWidth="200px">操作</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let item of tbList">
          <td>
            {{ item.auth_name }}
          </td>
          <td>
            <div class="pro-desc" [title]="item.auth_comment">{{ item.auth_comment }}</div>
          </td>
          <td>
            {{ productMap.get(item.pro_id) }}
          </td>
          <td>
            <div
              [ngStyle]="{
                color:
                  item.auth_type === 0
                    ? 'rgba(21, 22, 24, 0.92)'
                    : item.auth_type === 1
                    ? '#239545'
                    : '#1F71FF'
              }"
            >
              {{ item.auth_type === 0 ? '暂停授权' : item.auth_type === 1 ? '已授权' : '未授权' }}
            </div>
          </td>
          <td>
            <button hz-button hzType="action" (click)="viewApiInfo(item)">
              <i hz-icon hzName="visible-true"></i>
              <span>查看</span>
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
    <hz-empty
      hzEmptyIcon="no-result-light"
      hzEmptyTitle="暂无数据"
      *ngIf="!loading && !proLoading && tbList.length === 0"
    ></hz-empty>
    <hz-loading-gif *ngIf="loading || proLoading"></hz-loading-gif>
  </div>
</div>
