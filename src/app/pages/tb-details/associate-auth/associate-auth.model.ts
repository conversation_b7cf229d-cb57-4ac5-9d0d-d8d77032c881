// 字段列表详情
export interface FieldInfos {
  name: string;
  type: number;
  remark: string;
}
// 平台列表详情
export interface ProductInfo {
  pro_id: string; // 平台id
  auth_name: string;
  auth_type: number;
  auth_comment: string;
  creator: string;
  create_time: string;
  tb_name: string;
  tb_type: string;
  is_partation: number;
  visits: number;
  last_visit_time: string;
  field_infos: FieldInfos[];
}
// 字段列表详情
export interface FieldInfos {
  name: string;
  type: number;
  remark: string;
}
export interface ApiFields {
  data: [];
  total?: number;
  name: string;
  status: number;
  desc?: string;
  ctime: string;
  utime: string;
  api_id: string;
  id: number;
}
// 访问日志
export interface VisitLogParams {
  api_id?: string;
  access_status?: string; // 0访问成功，1访问失败
  begin_time: string;
  end_time: string;
  page_no: number;
  page_size: number;
  api_plat?: string; // 平台
}
// api服务列表返回
export interface ApiServeTbListRes {
  data: [];
  total: number;
}
export const typeStatusMap = {
  0: '数值',
  1: '数值',
  2: '文本',
  3: '日期'
};
// 操作符默认值
export const operatorMap = {
  '=': '等于',
  '<>': '不等于',
  '>': '大于',
  '<': '小于',
  '>=': '大于等于',
  '<=': '小于等于',
  'is null': '为空',
  'is not null': '不为空',
  'not like': '不包含',
  like: '包含'
};
export interface ApiDataList {
  [x: string]: any;
  name?: string; // Api名称
  desc: string; // Api描述
  url: string; // Api路径
  status?: number; // Api状态
  owner: string; // 发布人
  time?: string; // 发布时间
  api_id?: string;
  ctime?: string; // Api创建时间
  utime?: string; // Api更新时间
  visit_times: number; // Api调用次数
  last_visit_time: string; // Api最后调用时间
  id?: number;
  method: string;
  protocol?: string; // 协议
  result_type?: string; // 返回类型
  req_fields?: string;
  resp_fields?: string;
  success_result?: string;
  faild_result?: string;
  status_result?: string;
  storage_id?: string;
  tb_id?: string;
  tb_name?: string;
  tb_type?: number;
  api_plat?: string[];
}
export const FolderFromMap = {
  standard_lib: '标准表',
  standard: '标准表',
  sdtb: '标准表',
  bench: '专题标准表',
  element: '要素表',
  topic: '主题表',
  relation: '关系表',
  combine_relation: '关系表',
  tag: '标签表',
  topic_new: '部标专题表'
};
// 授权平台
export interface ProductItemConfig {
  pro_name: string;
  pro_name_cn?: string;
  pro_id: string;
  pro_des?: string;
}
export type ExportStatusText = '未更新' | '更新中' | '成功' | '失败' | '停止';
export type ExportStatusType = 0 | 1 | 2 | 3 | 4 | 5;
// 导出状态Map
export const ExportStatusMap: {
  [key: number]: {
    text: ExportStatusText;
    color: string;
  };
} = {
  0: { text: '未更新', color: '#FF9431' },
  1: { text: '更新中', color: '#0099EB' },
  2: { text: '成功', color: '#239545' },
  3: { text: '失败', color: '#FF5266' },
  4: { text: '未更新', color: '#FF9431' },
  5: { text: '停止', color: 'rgba(21, 22, 24, 0.36)' }
};

export const SyncConfigMap = {
  0: '定时更新',
  1: '定时更新',
  2: '自动更新',
  3: '暂停更新'
};

// 数据库类型枚举
type DsTypeName = 'MySQL' | 'PostgreSQL' | 'dataHub' | 'Greenplum' | 'HBase';
type DsTypImg = 'MYSQL' | 'POSTGRESQL' | 'DATAHUB' | 'GREENPLUM' | 'HBASE';
// 数据库类型config
export interface DsTypeConnectInfoConfig {
  title: string;
  name: string;
  value: string | boolean;
  hide?: boolean;
}

export interface DsTypeItemConfig {
  name: DsTypeName;
  type: number;
  dsImg?: DsTypImg;
  ds_list?: [];
  number?: number;
  ds_desc?: string;
  config?: DsTypeConnectInfoConfig[];
}
export const DsTypeList: DsTypeItemConfig[] = [
  {
    name: 'MySQL',
    type: 1,
    dsImg: 'MYSQL',
    ds_list: [], // 数据源列表
    number: 0, // 数据源中表总数
    ds_desc: '',
    config: [
      { title: '数据源名称', name: 'ds_name', value: '' },
      { title: '数据库地址', name: 'url', value: '' },
      { title: '数据库端口', name: 'port', value: '' },
      { title: '用户名', name: 'user', value: '' },
      { title: '密码', name: 'password', value: '' },
      { title: '数据库名称', name: 'db_name', value: '' }
    ]
  },
  {
    name: 'PostgreSQL',
    type: 50,
    dsImg: 'POSTGRESQL',
    ds_list: [], // 数据源列表
    number: 0, // 数据源中表总数
    ds_desc: '',
    config: [
      { title: '数据源名称', name: 'ds_name', value: '' },
      { title: '数据库地址', name: 'url', value: '' },
      { title: '数据库端口', name: 'port', value: '' },
      { title: '用户名', name: 'user', value: '' },
      { title: '密码', name: 'password', value: '' },
      { title: '数据库名称', name: 'db_name', value: '' }
    ]
  },
  {
    name: 'dataHub',
    type: 115,
    dsImg: 'DATAHUB',
    ds_list: [], // 数据源列表
    number: 0, // 数据源中表总数
    ds_desc: '',
    config: [
      { title: '数据源名称', name: 'ds_name', value: '' },
      { title: 'Endpoint', name: 'url', value: '' },
      { title: 'AccessID', name: 'user', value: '' },
      { title: 'AccessKey', name: 'password', value: '' },
      { title: 'Project', name: 'db_name', value: '' },
      { title: '版本号', name: 'version', value: '1' }
    ]
  },
  {
    name: 'Greenplum',
    type: 116,
    dsImg: 'GREENPLUM',
    ds_list: [], // 数据源列表
    number: 0, // 数据源中表总数
    ds_desc: '',
    config: [
      { title: '数据源名称', name: 'ds_name', value: '' },
      { title: '数据库地址', name: 'url', value: '' },
      { title: '数据库端口', name: 'port', value: '' },
      { title: '用户名', name: 'user', value: '' },
      { title: '密码', name: 'password', value: '' },
      { title: '数据库名称', name: 'db_name', value: '' }
    ]
  },
  {
    name: 'HBase',
    type: 117,
    dsImg: 'HBASE',
    ds_list: [], // 数据源列表
    number: 0, // 数据源中表总数
    ds_desc: '',
    config: [
      { title: '数据源名称', name: 'ds_name', value: '' },
      { title: '数据库名称', name: 'db_name', value: '' },
      { title: '集群地址', name: 'hbase.zookeeper.quorum', value: '' },
      { title: '端口', name: 'hbase.zookeeper.property.clientPort', value: '' },
      { title: '数据目录', name: 'hbase.rootdir', value: '' },
      { title: 'zookeeper目录', name: 'zookeeper.znode.parent', value: '' },
      { title: 'Kerberos认证', name: 'hbase.security.auth.enable', value: false },
      { title: 'master节点principal', name: 'hbase.master.kerberos.principal', value: '' },
      { title: 'region节点principal', name: 'hbase.regionserver.kerberos.principal', value: '' },
      { title: 'kerberos_keytab_path', name: 'principalFile', value: '' },
      { title: 'kerberos_krb5_path', name: 'java.security.krb5.conf', value: '' }
    ]
  }
];

export interface JobListRes {
  page_no: number;
  page_size: number;
  total: number;
  total_page: number;
  data: any[];
}
