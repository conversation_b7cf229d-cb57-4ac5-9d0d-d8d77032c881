import { MessageService } from '@haizhi/ng-hertz/message';
import { AssociateAuthService } from './associate-auth.service';
import { Component, OnInit, Input } from '@angular/core';
import { MatDialog } from '@angular/material';
import * as _ from 'lodash';
import { ConfirmDialogComponent } from '../../../ui/common-dialog/dialog-confirm.component';
import { TbDetailsService } from '../tb-details.service';

@Component({
  selector: 'associate-auth',
  templateUrl: './associate-auth.component.html',
  styleUrls: ['./associate-auth.component.scss']
})
export class AssociateAuthComponent implements OnInit {
  selectId = '';
  tbType = '';
  loading: boolean;
  tabs = [
    { route: 'api-visit', title: 'API访问服务', show: true },
    { route: 'table-auth', title: '库表授权服务', show: true },
    { route: 'data-export', title: '数据导出服务', show: true },
    { route: 'exter-view', title: '外部视图', show: false }
  ];
  constructor(
    private dialog: MatDialog,
    private associateAuthService: AssociateAuthService,
    private messageService: MessageService,
    private tbDetailsService: TbDetailsService
  ) {}
  ngOnInit() {
    this.selectId = this.tbDetailsService.curTbId;
    this.tbType = this.tbDetailsService.curTbType;
  }
}
