<div class="service-overview">
  <div class="service-overview-tab">
    <hz-tabs hzTabsType="auxiliary" [hzTabsRouteMode]="true">
      <ng-container *ngFor="let item of tabs; let i = index">
        <ng-container *ngIf="item.show">
          <hz-tab [hzTabTitle]="tabTitleTemplate"></hz-tab>
          <ng-template #tabTitleTemplate>
            <a
              [routerLink]="'./' + item.route"
              [queryParams]="{ tbType: tbType }"
              routerLinkActive="active"
            >
              <span>{{ item.title }}</span>
            </a>
          </ng-template>
        </ng-container>
      </ng-container>
    </hz-tabs>
  </div>
  <div class="service-overview-content">
    <router-outlet></router-outlet>
  </div>
</div>
