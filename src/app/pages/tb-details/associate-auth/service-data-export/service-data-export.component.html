<div class="list-content">
  <div class="table-container">
    <hz-empty hzEmptyIcon="no-result-light" hzEmptyTitle="暂无数据" *ngIf="jobList.length === 0"></hz-empty>
    <nz-table [nzScroll]="{ x: '1238px' }" *ngIf="jobList.length > 0">
      <thead>
        <tr>
          <th nzWidth="176px">目标表名称</th>
          <th nzWidth="132px">目标源类型</th>
          <th nzWidth="144px">目标源</th>
          <th nzWidth="114px">导出状态</th>
          <th nzWidth="114px">导出方式</th>
          <th nzWidth="124px">更新类型</th>
          <th nzWidth="188px">最近更新时间</th>
          <th nzWidth="176px" nzRight="0px">操作</th>
        </tr>
      </thead>
      <tbody>
        <tr *ngFor="let data of jobList">
          <td>
            <p class="nowrap">{{ data.xtb_info.xtb_name }}</p>
          </td>
          <td>
            <p class="icon-name">
              <img
                *ngIf="dsTypeMap[data.ds_info.type]"
                [src]="'/pmresources/noah-database/' + dsTypeMap[data.ds_info.type].dsImg + '.svg'"
                class="db-type-icon mr4"
              />
              <span>{{ dsTypeMap[data.ds_info.type] && dsTypeMap[data.ds_info.type].name }}</span>
            </p>
          </td>
          <td>
            <p class="nowrap">{{ data.ds_info.ds_name }}</p>
          </td>
          <td>
            <p class="status">
              <span
                class="spot"
                [ngStyle]="{ 'background-color': exportStatusMap[data.status].color }"
              ></span>
              <span>{{ exportStatusMap[data.status].text }}</span>
            </p>
          </td>
          <td>
            <p>{{ data.export_mode.mode === 'overwrite' ? '全量' : '增量' }}</p>
          </td>
          <td>
            <p>{{ syncConfigMap[data.scheduler_conf.mode] }}</p>
          </td>
          <td>{{ data.export_time }}</td>
          <td nzRight="0px">
            <button
              hz-button
              hzType="text"
              hzSize="m"
              class="table-btn"
              (click)="onBtnClick($event, data)"
            >
              编辑
            </button>
          </td>
        </tr>
      </tbody>
    </nz-table>
  </div>
  <hz-loading-gif *ngIf="loading"></hz-loading-gif>
</div>
