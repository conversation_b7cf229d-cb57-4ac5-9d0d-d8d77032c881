import { Component, OnInit } from '@angular/core';
import { DsTypeList, DsTypeItemConfig, ExportStatusMap, SyncConfigMap } from '../associate-auth.model';
import { AssociateAuthService } from '../associate-auth.service';
import { finalize } from 'rxjs/operators';
import { TbDetailsService } from '../../tb-details.service';

@Component({
  selector: 'app-service-data-export',
  templateUrl: './service-data-export.component.html',
  styleUrls: ['./service-data-export.component.scss']
})
export class ServiceDataExportComponent implements OnInit {
  listTotal = 0;
  jobList: any[] = [];
  dsTypeMap: { [index: string]: DsTypeItemConfig } = {};
  exportStatusMap = ExportStatusMap;
  syncConfigMap = SyncConfigMap;
  loading = false;
  tbId = '';
  constructor(private associateAuthService: AssociateAuthService, private tbDetailsService: TbDetailsService) {}

  ngOnInit() {
    this.tbId = this.tbDetailsService.curTbId;
    this.setDsTypeMap();
    this.getJobList();
  }
  /**
   * 获取目标库类型列表
   */
  setDsTypeMap() {
    DsTypeList.forEach(item => {
      this.dsTypeMap[item.type] = item;
    });
  }

  onBtnClick(event, data) {
    window.open(
      `/dmc/#/data-serve/data-export/job-manage/new?job_id=${data.job_id}&job_group_id=${data.job_group_id}&type=edit&tb_id=${data.tb_info.tb_id}`
    );
  }

  getJobList() {
    this.associateAuthService
      .getJobList({
        tb_id: this.tbId,
        order_by: 'ctime'
      })
      .pipe(finalize(() => (this.loading = false)))
      .subscribe(res => {
        this.jobList = res.data;
      });
  }
}
