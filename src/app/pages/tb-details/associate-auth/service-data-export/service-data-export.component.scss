@import '~@haizhi/ui/styles/themes/light.data';

.list-content {
  height: 100%;
  .table-container {
    padding: 16px;
    height: calc(100% - 40px);
    background: #ffffff;
    box-shadow: $container-c100;
    border-radius: 8px;
  }
  .icon-name {
    display: flex;
    align-items: center;
  }
  .tb-name,
  .task-name {
    color: $primary-900;
    cursor: pointer;
    &:hover {
      color: $primary-700;
    }
  }
  .task-name {
    &:hover {
      text-decoration: underline;
    }
  }
  .db-type-icon {
    border-radius: 5px;
    width: 20px;
    height: 20px;
  }
  .status {
    display: flex;
    align-items: center;
    .spot {
      width: 6px;
      height: 6px;
      border-radius: 3px;
      margin-right: 8px;
    }
  }
  .list-footer {
    text-align: right;
  }
  .hz-btn-text-default {
    font-weight: normal;
    font-size: 12px;
  }
}
