import { Injectable, EventEmitter } from '@angular/core';
import { HttpService } from '../../../core/services/http.service';
import * as _ from 'lodash';
import { ProductInfo, ApiFields, VisitLogParams, ApiServeTbListRes, ProductItemConfig, JobListRes } from './associate-auth.model';
import { Observable } from 'rxjs';

@Injectable()
export class AssociateAuthService {
  TableChange: EventEmitter<any>;
  DicTypeChange: EventEmitter<any>;
  constructor(private commonHttp: HttpService) {
    this.TableChange = new EventEmitter();
    this.DicTypeChange = new EventEmitter();
  }
  // 获取API服务列表
  getApiList(params: { tb_id: string; page_no: number; page_size: number }): Observable<ApiFields> {
    return this.commonHttp.get('/api/apiInfo/listByTbId', params);
  }
  // 获取授权列表
  getPermissionInfo(params: { tb_id: string }): Observable<ProductInfo[]> {
    return this.commonHttp.get('/api/asset/permission/info', params);
  }
  // 获取单表授权状态
  getTbProdAuthStatus(params: { tb_id: string }): Observable<{ pro_id: string; auth_type: number }[]> {
    return this.commonHttp.get('/api/asset/permission/pro/info', params);
  }
  /**
   * 获取服务配置和管理的支持授权的平台列表
   */
  getProductList(): Observable<ProductItemConfig[]> {
    return this.commonHttp.get('/api/asset/permission/product/list');
  }

  // 访问日志页面
  getVisitLog(params: VisitLogParams): Observable<ApiServeTbListRes> {
    return this.commonHttp.get('/api/apiLog/logInfo', params);
  }
  // Api详情数据
  getApiDetails(api_id: string) {
    return this.commonHttp.post('/api/apiInfo/getVoByApiId', {
      api_id
    });
  }
  getJobList(params: { tb_id: string; order_by: string }): Observable<any> {
    return this.commonHttp.get('/api/dmc/export/job/query', params);
  }
}
