#分离命令行输入参数
for i in "$@"
do
    case "$i" in
        --dest=*)
            DIR_BUILD="${i#*=}"
            ;;
        -D)
            FIS_PARAMS="$FIS_PARAMS -D"
            ;;
        --fast)
            FAST_BUILD=1
            OTHER_PARAMS="$OTHER_PARAMS --defines-nocompile"
            ;;
        *)
            OTHER_PARAMS="$OTHER_PARAMS $i"
            ;;
    esac
done
 #默认输出路径
if [ -z $DIR_BUILD ];
then
    DIR_BUILD="./build"
    #echo 'ERROR: --dest missing!!'
    #exit 1
fi   

echo 'DIR_BUILD:' $DIR_BUILD;

date=$(date "+%Y-%m-%d %H:%M:%S");
log=$(git log -1  --oneline)
branch=$(git symbolic-ref HEAD 2>/dev/null | cut -d"/" -f 3);
gitName=$(git remote -v);

buildFile=${DIR_BUILD}/build.txt;
# text='仓库地址:'$gitName'\nbranch:'$branch'\ncommit_id:'$log'\n构建时间：'$date;
echo '仓库地址：' $gitName >> $buildFile;
echo 'branch：' $branch >> $buildFile;
echo 'commit_id：' $log >> $buildFile;
echo '构建时间：' $date >> $buildFile;