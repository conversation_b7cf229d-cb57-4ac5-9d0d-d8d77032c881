{"name": "metadata", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve --proxy-config proxy.conf.json --port 4500", "build": "ng build --base-href /tb-details/ --prod", "test": "ng test", "lint": "ng lint", "e2e": "ng e2e", "build:dev": "ng build --base-href /tb-details/ ", "build:prod": "ng build --base-href /tb-details/ --prod", "build-pre": "sh scripts/build-pre.sh", "build-prod": "sh scripts/build-prod.sh"}, "private": true, "dependencies": {"@angular/animations": "~8.2.14", "@angular/cdk": "^8.2.3", "@angular/common": "~8.2.14", "@angular/compiler": "~8.2.14", "@angular/core": "~8.2.14", "@angular/forms": "~8.2.14", "@angular/material": "^8.2.3", "@angular/platform-browser": "~8.2.14", "@angular/platform-browser-dynamic": "~8.2.14", "@angular/router": "~8.2.14", "@antv/g6": "4.0.4", "@antv/g6-core": "0.0.9", "@antv/util": "2.0.14", "@haizhi/ng-hertz": "0.0.1-beta110", "@haizhi/ui": "2.5.1-beta22", "@types/lodash": "4.14.170", "@types/sockjs-client": "1.1.0", "codemirror": "^5.59.1", "hz-web-common": "^2.0.6", "jquery": "^3.5.1", "moment": "^2.29.1", "ng-socket-io": "0.2.4", "ng-zorro-antd": "8.5.2", "ng2-codemirror": "^1.1.3", "ng2-file-upload": "^1.4.0", "ngx-cookie-service": "^3.0.4", "rxjs": "~6.5.4", "tslib": "^1.10.0", "echarts": "5.0.2", "zone.js": "~0.9.1"}, "devDependencies": {"@angular-devkit/build-angular": "~0.803.20", "@angular/cli": "~8.3.20", "@angular/compiler-cli": "~8.2.14", "@angular/language-service": "~8.2.14", "@commitlint/cli": "^11.0.0", "@commitlint/config-angular": "^11.0.0", "@types/jasmine": "~3.3.8", "@types/jasminewd2": "~2.0.3", "@types/jquery": "^3.5.5", "@types/node": "~8.9.4", "codelyzer": "^5.0.0", "husky": "^4.3.6", "jasmine-core": "~3.4.0", "jasmine-spec-reporter": "~4.2.1", "karma": "~4.1.0", "karma-chrome-launcher": "~2.2.0", "karma-coverage-istanbul-reporter": "~2.0.1", "karma-jasmine": "~2.0.1", "karma-jasmine-html-reporter": "^1.4.0", "prettier": "^2.2.1", "pretty-quick": "^3.1.0", "protractor": "~5.4.0", "sockjs-client": "1.3.0", "ts-node": "~7.0.0", "tslint": "~5.15.0", "typescript": "~3.5.3"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS", "pre-commit": "pretty-quick --staged"}}}